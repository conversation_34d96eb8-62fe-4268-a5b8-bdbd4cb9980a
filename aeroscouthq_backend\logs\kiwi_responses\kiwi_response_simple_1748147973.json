{"data": {"onewayItineraries": {"__typename": "Itineraries", "server": {"requestId": "a597085ab90e4b779fcbe9c86bd582d2", "environment": "PROD", "packageVersion": "git-bff8ac9486769348e980126f223dbeddabd34ddc", "serverToken": "****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"}, "metadata": {"eligibilityInformation": {"baggageEligibilityInformation": {"topFiveResultsBaggageEligibleForPrompt": null, "numberOfBags": null}, "guaranteeAndRedirectsEligibilityInformation": {"redirect": {"anywhere": true, "top10": true, "isKiwiAvailable": true}, "guarantee": {"anywhere": false, "top10": false}, "combination": {"anywhere": false, "top10": false}}, "kiwiBasicEligibility": {"anywhere": true, "top10": true}, "topThreeResortingOccurred": false, "carriersDeeplinkEligibility": null, "responseContainsKayakItinerary": false, "paretoABTestEligible": null}, "carriers": [{"code": "JX", "id": "Carrier:818", "name": "Starlux Airlines"}, {"code": "UU", "id": "Carrier:187", "name": "Air Austral"}, {"code": "XY", "id": "Carrier:468", "name": "flynas"}, {"code": "UO", "id": "Carrier:349", "name": "Hong Kong Express Airways"}, {"code": "D8", "id": "Carrier:276", "name": "Norwegian Air Sweden"}, {"code": "VN", "id": "Carrier:611", "name": "Vietnam Airlines"}, {"code": "NH", "id": "Carrier:99", "name": "All Nippon Airways"}, {"code": "BJ", "id": "Carrier:42", "name": "NouvelAir"}, {"code": "EI", "id": "Carrier:10", "name": "<PERSON><PERSON>"}, {"code": "GF", "id": "Carrier:333", "name": "Gulf Air Bahrain"}, {"code": "6E", "id": "Carrier:357", "name": "IndiGo Airlines"}, {"code": "A3", "id": "Carrier:39", "name": "Aegean"}, {"code": "HH", "id": "Carrier:788", "name": "Qanot Shark"}, {"code": "KC", "id": "Carrier:174", "name": "Air Astana"}, {"code": "9C", "id": "Carrier:785", "name": "Spring Airlines"}, {"code": "OS", "id": "Carrier:121", "name": "Austrian Airlines"}, {"code": "FD", "id": "Carrier:580", "name": "Thai AirAsia"}, {"code": "VJ", "id": "Carrier:642", "name": "VietJet Air"}, {"code": "M0", "id": "Carrier:1040", "name": "Aero Mongolia"}, {"code": "WY", "id": "Carrier:473", "name": "Oman Air"}, {"code": "ME", "id": "Carrier:445", "name": "Middle East Airlines"}, {"code": "W6", "id": "Carrier:12", "name": "Wizz Air"}, {"code": "ET", "id": "Carrier:295", "name": "Ethiopian Airlines"}, {"code": "DE", "id": "Carrier:28", "name": "Condor"}, {"code": "SG", "id": "Carrier:524", "name": "Spicejet"}, {"code": "EW", "id": "Carrier:301", "name": "Eurowings"}, {"code": "KN", "id": "Carrier:694", "name": "China United"}, {"code": "LH", "id": "Carrier:419", "name": "Lufthansa"}, {"code": "BT", "id": "Carrier:22", "name": "airBaltic"}, {"code": "D7", "id": "Carrier:314", "name": "AirAsia X"}, {"code": "CZ", "id": "Carrier:242", "name": "China Southern Airlines"}, {"code": "MR", "id": "Carrier:756", "name": "<PERSON><PERSON><PERSON> Air"}, {"code": "ZH", "id": "Carrier:550", "name": "Shenzhen Airlines"}, {"code": "IB", "id": "Carrier:352", "name": "Iberia Airlines"}, {"code": "TW", "id": "Carrier:816", "name": "Tway Airlines"}, {"code": "LG", "id": "Carrier:422", "name": "Luxair"}, {"code": "JD", "id": "Carrier:648", "name": "Beijing Capital Airlines"}, {"code": "OZ", "id": "Carrier:51", "name": "Asiana Airlines"}, {"code": "MS", "id": "Carrier:288", "name": "Egyptair"}, {"code": "OD", "id": "Carrier:888", "name": "Batik Air Malaysia"}, {"code": "FR", "id": "Carrier:11", "name": "<PERSON><PERSON>"}, {"code": "FJ", "id": "Carrier:162", "name": "Fiji Airways"}, {"code": "AI", "id": "Carrier:82", "name": "Air India Limited"}, {"code": "NX", "id": "Carrier:96", "name": "Air Macau"}, {"code": "S4", "id": "Carrier:516", "name": "SATA Azores Airlines"}, {"code": "UX", "id": "Carrier:60", "name": "Air Europa"}, {"code": "RJ", "id": "Carrier:512", "name": "Royal Jordanian"}, {"code": "FM", "id": "Carrier:549", "name": "Shanghai Airlines"}, {"code": "JU", "id": "Carrier:378", "name": "Air Serbia"}, {"code": "AF", "id": "Carrier:70", "name": "Air France"}, {"code": "BG", "id": "Carrier:208", "name": "Biman Bangladesh Airlines"}, {"code": "VS", "id": "Carrier:618", "name": "Virgin Atlantic Airways"}, {"code": "SQ", "id": "Carrier:528", "name": "Singapore Airlines"}, {"code": "AT", "id": "Carrier:510", "name": "Royal Air Maroc"}, {"code": "MU", "id": "Carrier:241", "name": "China Eastern Airlines"}, {"code": "X1", "id": "Carrier:2388", "name": "Hahn Air Technologies"}, {"code": "KU", "id": "Carrier:402", "name": "Kuwait Airways"}, {"code": "HV", "id": "Carrier:4", "name": "Transavia"}, {"code": "BA", "id": "Carrier:207", "name": "British Airways"}, {"code": "QR", "id": "Carrier:502", "name": "Qatar Airways"}, {"code": "LX", "id": "Carrier:541", "name": "Swiss International Air Lines"}, {"code": "QF", "id": "Carrier:501", "name": "Qantas"}, {"code": "SV", "id": "Carrier:538", "name": "Saudi Arabian Airlines"}, {"code": "RO", "id": "Carrier:597", "name": "<PERSON><PERSON>"}, {"code": "5J", "id": "Carrier:237", "name": "Cebu Pacific"}, {"code": "LA", "id": "Carrier:405", "name": "LATAM Airlines"}, {"code": "FZ", "id": "Carrier:27", "name": "Fly Dubai"}, {"code": "KQ", "id": "Carrier:395", "name": "Kenya Airways"}, {"code": "HO", "id": "Carrier:388", "name": "Juneyao Airlines"}, {"code": "OM", "id": "Carrier:426", "name": "MIAT Mongolian Airlines"}, {"code": "LY", "id": "Carrier:289", "name": "El Al Israel Airlines"}, {"code": "FB", "id": "Carrier:225", "name": "Bulgaria Air"}, {"code": "PC", "id": "Carrier:16", "name": "Pegasus"}, {"code": "VY", "id": "Carrier:9", "name": "<PERSON><PERSON>ing"}, {"code": "AY", "id": "Carrier:307", "name": "<PERSON><PERSON>"}, {"code": "F3", "id": "Carrier:1018", "name": "Flyadeal"}, {"code": "7C", "id": "Carrier:380", "name": "Je<PERSON>"}, {"code": "TO", "id": "Carrier:654", "name": "Transavia France"}, {"code": "UL", "id": "Carrier:522", "name": "SriLankan Airlines"}, {"code": "VZ", "id": "Carrier:454", "name": "Thai Vietjet"}, {"code": "U2", "id": "Carrier:3", "name": "easyJet"}, {"code": "WB", "id": "Carrier:513", "name": "Rwandair"}, {"code": "3U", "id": "Carrier:548", "name": "Sichuan Airlines"}, {"code": "A9", "id": "Carrier:325", "name": "Georgian Airways"}, {"code": "DV", "id": "Carrier:569", "name": "Scat Airlines"}, {"code": "TG", "id": "Carrier:579", "name": "Thai Airways"}, {"code": "DY", "id": "Carrier:17", "name": "Norwegian Air Shuttle"}, {"code": "G9", "id": "Carrier:26", "name": "Air Arabia"}, {"code": "HU", "id": "Carrier:335", "name": "Hainan Airlines"}, {"code": "SN", "id": "Carrier:223", "name": "Brussels Airlines"}, {"code": "CA", "id": "Carrier:151", "name": "Air China"}, {"code": "W4", "id": "Carrier:820", "name": "Wizz Air Malta"}, {"code": "PG", "id": "Carrier:211", "name": "Bangkok Airways"}, {"code": "MF", "id": "Carrier:638", "name": "Xiamen Airlines"}, {"code": "6H", "id": "Carrier:371", "name": "<PERSON><PERSON><PERSON>"}, {"code": "HY", "id": "Carrier:606", "name": "Uzbekistan Airways"}, {"code": "J2", "id": "Carrier:79", "name": "Azerbaijan Airlines"}, {"code": "AH", "id": "Carrier:156", "name": "Air Algerie"}, {"code": "TP", "id": "Carrier:572", "name": "TAP Portugal"}, {"code": "TR", "id": "Carrier:577", "name": "<PERSON>oot"}, {"code": "UA", "id": "Carrier:601", "name": "United Airlines"}, {"code": "CI", "id": "Carrier:240", "name": "China Airlines"}, {"code": "SM", "id": "Carrier:737", "name": "Air Cairo"}, {"code": "KL", "id": "Carrier:391", "name": "KLM Royal Dutch Airlines"}, {"code": "VF", "id": "Carrier:610", "name": "AJet"}, {"code": "HB", "id": "Carrier:2721", "name": "Greater Bay Airlines"}, {"code": "XJ", "id": "Carrier:443", "name": "Thai AirAsia X"}, {"code": "KE", "id": "Carrier:399", "name": "Korean Air"}, {"code": "QV", "id": "Carrier:412", "name": "Lao Airlines"}, {"code": "PR", "id": "Carrier:492", "name": "Philippine Airlines"}, {"code": "TK", "id": "Carrier:581", "name": "Turkish Airlines"}, {"code": "GJ", "id": "Carrier:299", "name": "Loong Air"}, {"code": "FI", "id": "Carrier:355", "name": "Icelandair"}, {"code": "OV", "id": "Carrier:294", "name": "SalamAir"}, {"code": "HX", "id": "Carrier:348", "name": "Hong Kong Airlines"}, {"code": "KM", "id": "Carrier:87", "name": "KM Malta Airlines"}, {"code": "3K", "id": "Carrier:383", "name": "Jetstar Asia Airways"}, {"code": "AZ", "id": "Carrier:138", "name": "ITA Airways"}, {"code": "TU", "id": "Carrier:573", "name": "<PERSON><PERSON><PERSON>"}, {"code": "BR", "id": "Carrier:282", "name": "EVA Air"}, {"code": "ZE", "id": "Carrier:670", "name": "Eastar Jet"}, {"code": "GQ", "id": "Carrier:1157", "name": "SKY express"}, {"code": "CX", "id": "Carrier:235", "name": "Cathay Pacific"}, {"code": "SK", "id": "Carrier:519", "name": "SAS"}, {"code": "EK", "id": "Carrier:291", "name": "Emirates"}, {"code": "EN", "id": "Carrier:158", "name": "Air Dolomiti"}, {"code": "OU", "id": "Carrier:265", "name": "Croatia Airlines"}, {"code": "IX", "id": "Carrier:134", "name": "Air India Express"}, {"code": "EY", "id": "Carrier:296", "name": "Etihad Airways"}, {"code": "MH", "id": "Carrier:430", "name": "Malaysia Airlines"}, {"code": "LO", "id": "Carrier:409", "name": "LOT Polish Airlines"}], "stopoverCountries": [], "inboundDays": [], "outboundDays": ["THURSDAY"], "travelTips": [], "topResults": {"best": {"__typename": "ItineraryOneWay", "duration": 36900, "price": {"amount": "3805"}, "id": "ItineraryOneWay:****************************************************************************************************************************************************************"}, "cheapest": {"__typename": "ItineraryOneWay", "duration": 36900, "price": {"amount": "3805"}, "id": "ItineraryOneWay:****************************************************************************************************************************************************************"}, "fastest": {"__typename": "ItineraryOneWay", "duration": 36900, "price": {"amount": "3805"}, "id": "ItineraryOneWay:****************************************************************************************************************************************************************"}, "sourceTakeoffAsc": {"__typename": "ItineraryOneWay", "duration": 36900, "price": {"amount": "3805"}, "id": "ItineraryOneWay:****************************************************************************************************************************************************************"}, "destinationLandingAsc": {"__typename": "ItineraryOneWay", "duration": 36900, "price": {"amount": "3805"}, "id": "ItineraryOneWay:****************************************************************************************************************************************************************"}}, "priceAlertExists": null, "existingPriceAlert": null, "searchFingerprint": "************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "hasMorePending": false, "priceAlertsTopResults": {"best": {"price": {"amount": "4209"}}, "cheapest": {"price": {"amount": "4209"}}, "fastest": {"price": {"amount": "4209"}}, "sourceTakeoffAsc": {"price": {"amount": "4209"}}, "destinationLandingAsc": {"price": {"amount": "4209"}}}, "itinerariesCount": 2, "missingProviders": [], "statusPerProvider": [{"provider": {"id": "ContentProvider:KIWI"}, "errorHappened": false, "errorMessage": null}], "hasTier1MarketItineraries": false, "sharedItinerary": null, "kayakEligibilityTest": {"containsKayakWithNewRules": null, "containsKayakWithCurrentRules": false, "containsKayakAirlinesWithNewRules": null}}, "itineraries": [{"__typename": "ItineraryOneWay", "__isItinerary": "ItineraryOneWay", "id": "ItineraryOneWay:****************************************************************************************************************************************************************", "shareId": "********************************************************************************************************************************************************************************", "price": {"amount": "3805", "priceBeforeDiscount": "3805"}, "priceEur": {"amount": "466.059307"}, "provider": {"name": "Kiwi.com", "code": "KIWI-BASIC", "hasHighProbabilityOfPriceChange": false, "contentProvider": null, "id": "ItineraryProvider:KIWI-BASIC"}, "bagsInfo": {"includedCheckedBags": 0, "includedHandBags": 0, "hasNoBaggageSupported": true, "hasNoCheckedBaggage": false, "checkedBagTiers": [], "handBagTiers": [], "includedPersonalItem": 1, "personalItemTiers": [{"tierPrice": {"amount": "0"}, "bags": [{"weight": {"value": 8}, "height": {"value": 30}, "width": {"value": 15}, "length": {"value": 40}}]}]}, "bookingOptions": {"edges": [{"node": {"token": "H8LBCT1uzqPmDuQ5WsbOlz01LtzVoohDJs5wFJRa37D7cNNKEygUPB6WnlUPTanastbyO6WqsJjiO1ydB3YlJrqRtx0YUxk4Z3Ypd20r9WDFuuhLZSYHHV9i5VtHqK0aApRo2Clzzq3F06btmnK6xlmlvtgxsoixqLG5iKJJBIwJnUK24dUocejOC-EiHfyAbYP4UlvOvwSUBVxD-Zv0uks975dowCmsqHYzWjgo1m88rTJ4zHCQ0aBQxzEMK9G0d-KtMUFz5YdgEGscSxNeOkm5DM_Ye3b-g60p7qNc9tRmpMhm4_UcfoNLmFZxBUwKeRlbytx91GZNEQCoUNwhT-s8Indwtvs9wYc8dhydbhFFGt2qdkC9UmXoeBnMi4lhTzavfot9IAKZ3c81tC8vOXkeEX4UGnMO2wXJbM2iztJrXiHK-4CcR4FrLvRZY-_Sw5cNzjmzDSxxPFLxE1GIw2RAARgmZ-qjYCAdypwtxykUfTE7hH1-ld2Sll7Fq86Cwf5MSPH3ND8viGxzt0cZUdK1APCPugVrvVH7-U7CwQ85kGyBxA6KKyAQe6NrQjApawKxoAJzvti5ZEvflKJ5D5uA94LT9L7dfOAv_wNcvab8qffTpjR2BaSZGDWmPFqzfKBffb4gHb-jE5J367UhE1sNbHFgtHS1aNQy-2jYq8gdt_p_anm9RONR5tktPxYJTb5ZOP8IZnSAvjmWzF9EmMEmw5GVRsaCF8-2ahB9fiLsc5H7ppbYRx82ilx8ifYsxitKhqKkx8BHLyt7qM4RCSZ_tOEeOy1qtAse_kqG3N-tJ3FhIT796rZXLa6LMTjQxTHc3MIXUxcKWPOyuUpLmRO8fAvUc4WLXWYYtykXed-fu1GzFFuMvzasGmgcRkZzth6qAmXXJuEnLaA3Bg9jkROi5axp75XJz28LBsfxpRA3WP0MqUUnkII1-uDhUrBitB1zNTcZqa9dHol6ParF4322AcZVRMjeWztkIYau8YPV2Q2fihvJf7f2l095mawEjwbeB14pTMja2oA9obovy4UX_bJJVelWeYlcn-L5ryH7tQJi6p9NNbqKwcLT_7oHQ5lf_CaXJ9UIiO1fmrSoNTg==", "bookingUrl": "/cn/booking/?direct=true&locale=cn&currency=cny&passengers=1-0-0&token=H8LBCT1uzqPmDuQ5WsbOlz01LtzVoohDJs5wFJRa37D7cNNKEygUPB6WnlUPTanastbyO6WqsJjiO1ydB3YlJrqRtx0YUxk4Z3Ypd20r9WDFuuhLZSYHHV9i5VtHqK0aApRo2Clzzq3F06btmnK6xlmlvtgxsoixqLG5iKJJBIwJnUK24dUocejOC-EiHfyAbYP4UlvOvwSUBVxD-Zv0uks975dowCmsqHYzWjgo1m88rTJ4zHCQ0aBQxzEMK9G0d-KtMUFz5YdgEGscSxNeOkm5DM_Ye3b-g60p7qNc9tRmpMhm4_UcfoNLmFZxBUwKeRlbytx91GZNEQCoUNwhT-s8Indwtvs9wYc8dhydbhFFGt2qdkC9UmXoeBnMi4lhTzavfot9IAKZ3c81tC8vOXkeEX4UGnMO2wXJbM2iztJrXiHK-4CcR4FrLvRZY-_Sw5cNzjmzDSxxPFLxE1GIw2RAARgmZ-qjYCAdypwtxykUfTE7hH1-ld2Sll7Fq86Cwf5MSPH3ND8viGxzt0cZUdK1APCPugVrvVH7-U7CwQ85kGyBxA6KKyAQe6NrQjApawKxoAJzvti5ZEvflKJ5D5uA94LT9L7dfOAv_wNcvab8qffTpjR2BaSZGDWmPFqzfKBffb4gHb-jE5J367UhE1sNbHFgtHS1aNQy-2jYq8gdt_p_anm9RONR5tktPxYJTb5ZOP8IZnSAvjmWzF9EmMEmw5GVRsaCF8-2ahB9fiLsc5H7ppbYRx82ilx8ifYsxitKhqKkx8BHLyt7qM4RCSZ_tOEeOy1qtAse_kqG3N-tJ3FhIT796rZXLa6LMTjQxTHc3MIXUxcKWPOyuUpLmRO8fAvUc4WLXWYYtykXed-fu1GzFFuMvzasGmgcRkZzth6qAmXXJuEnLaA3Bg9jkROi5axp75XJz28LBsfxpRA3WP0MqUUnkII1-uDhUrBitB1zNTcZqa9dHol6ParF4322AcZVRMjeWztkIYau8YPV2Q2fihvJf7f2l095mawEjwbeB14pTMja2oA9obovy4UX_bJJVelWeYlcn-L5ryH7tQJi6p9NNbqKwcLT_7oHQ5lf_CaXJ9UIiO1fmrSoNTg%3D%3D&searchType=oneWay&searchBags=0.0", "trackingPixel": "https://www.kiwi.com/pixel?affilid=cj&currency=CNY&flightsId=0f6417874f1b00008e11ea39_0%7C0f6417874f1b00008e11ea39_1&from=LHR&lang=cn&passengers=1&searchBags=0.0&to=PKX&booking_token=H8LBCT1uzqPmDuQ5WsbOlz01LtzVoohDJs5wFJRa37D7cNNKEygUPB6WnlUPTanastbyO6WqsJjiO1ydB3YlJrqRtx0YUxk4Z3Ypd20r9WDFuuhLZSYHHV9i5VtHqK0aApRo2Clzzq3F06btmnK6xlmlvtgxsoixqLG5iKJJBIwJnUK24dUocejOC-EiHfyAbYP4UlvOvwSUBVxD-Zv0uks975dowCmsqHYzWjgo1m88rTJ4zHCQ0aBQxzEMK9G0d-KtMUFz5YdgEGscSxNeOkm5DM_Ye3b-g60p7qNc9tRmpMhm4_UcfoNLmFZxBUwKeRlbytx91GZNEQCoUNwhT-s8Indwtvs9wYc8dhydbhFFGt2qdkC9UmXoeBnMi4lhTzavfot9IAKZ3c81tC8vOXkeEX4UGnMO2wXJbM2iztJrXiHK-4CcR4FrLvRZY-_Sw5cNzjmzDSxxPFLxE1GIw2RAARgmZ-qjYCAdypwtxykUfTE7hH1-ld2Sll7Fq86Cwf5MSPH3ND8viGxzt0cZUdK1APCPugVrvVH7-U7CwQ85kGyBxA6KKyAQe6NrQjApawKxoAJzvti5ZEvflKJ5D5uA94LT9L7dfOAv_wNcvab8qffTpjR2BaSZGDWmPFqzfKBffb4gHb-jE5J367UhE1sNbHFgtHS1aNQy-2jYq8gdt_p_anm9RONR5tktPxYJTb5ZOP8IZnSAvjmWzF9EmMEmw5GVRsaCF8-2ahB9fiLsc5H7ppbYRx82ilx8ifYsxitKhqKkx8BHLyt7qM4RCSZ_tOEeOy1qtAse_kqG3N-tJ3FhIT796rZXLa6LMTjQxTHc3MIXUxcKWPOyuUpLmRO8fAvUc4WLXWYYtykXed-fu1GzFFuMvzasGmgcRkZzth6qAmXXJuEnLaA3Bg9jkROi5axp75XJz28LBsfxpRA3WP0MqUUnkII1-uDhUrBitB1zNTcZqa9dHol6ParF4322AcZVRMjeWztkIYau8YPV2Q2fihvJf7f2l095mawEjwbeB14pTMja2oA9obovy4UX_bJJVelWeYlcn-L5ryH7tQJi6p9NNbqKwcLT_7oHQ5lf_CaXJ9UIiO1fmrSoNTg==", "itineraryProvider": {"code": "KIWI-BASIC", "name": "Kiwi.com", "subprovider": null, "hasHighProbabilityOfPriceChange": false, "contentProvider": null, "providerCategory": null, "id": "ItineraryProvider:KIWI-BASIC"}, "price": {"amount": "3805"}, "priceEur": {"amount": "466.06"}, "priceLocks": {"priceLocksCurr": [{"default": false, "price": {"amount": "190.25", "roundedFormattedValue": "¥190"}}, {"default": true, "price": {"amount": "380.5", "roundedFormattedValue": "¥381"}}, {"default": false, "price": {"amount": "951.25", "roundedFormattedValue": "¥951"}}], "priceLocksEur": [{"default": false, "price": {"amount": "23.3", "roundedFormattedValue": "23 €"}}, {"default": true, "price": {"amount": "46.61", "roundedFormattedValue": "47 €"}}, {"default": false, "price": {"amount": "116.51", "roundedFormattedValue": "117 €"}}]}, "kiwiProduct": "KIWI_BASIC", "disruptionTreatment": "NO_PROTECTION", "usRulesApply": false}}, {"node": {"token": "HvIlqHx8JZuHs52J5xiE6LOT-olIDLcGElmlN26PangvPGyaoLvLQT59GhSqrmlEpfERo_Z8XdofYnXIrGHAx449JGuar-LTXqQxaog8TjjOT09dmqnlQZYdWJJt1NWnhHSNk32N-ujt_PeqXFEsGikDqZLeE-xg4N66tEaR3AmdLyFsqrJDAFLZMrn_erdJ5JE2EmvSMOOeU5A5k9ltM18jnE4Y9lywOP8h4crGv2U66DIjn1JGasFqo3-hEdZ1_vihzlayP73HAFPbP3yzftln5l3FVkJRPNpqLPBQR7ukg4i3NKcHO3844-KK9JEyo4Fzfzl0YQ-aQvb82GlNsRMGJTZR-xFZicOmhNP8RPV0vESEoqThOQdB-zZefE76wVDtOzGFcmfwaK6pUTzMiPnjWIZRHulw87sMMVH901H7IYDyZwExZ9jX4YyD4f3KdHgLwm7DvNws7b94HX99431NOAeOl5HUXezZR6DMV_UQBmGe-yYVZFHP-SmhRuF1J26tpbYCmBxrJqOH18pENCVWYd46Dss71Z8NrE5i1K2Xj1jfZVZVaMPWAlRpvvmtVE4ZyG4fYxFXDWuXyONf49iI9PnJ-9VESw1tk9L5GJgFvzayQsWcMyEirCVpAy9Jwpk0WDPAhadU49ki7knA--xOG8RKVL-1wxQf5S25ZXW4tvvT2jVEs_X1exZSucTgNsgcHvFeYTQGbbVAORuHSz-IraSxvyd62mDS6ZAXH9yQyZJ2FBYF_77lkqW4Ow5VoZ4GHKQc1WG9yB0vqwXjEYQsQpCMDfB5H2bdbpiwrJ5tXJW3Y8qbj_mTKL5xPkNPbg6lXIr-RUdKP3_4y6uQPEtfB3R8mhX9QmjUzVGEfh35puZPNKgYsR2pF-btQGzJ94fGwh8H6oIcY7WdpDbQhb7mGz4LJbit_2RfAM3W5QO_K3XNxxXHyyozDrFdf4HFSIZqd3RK_jI1Jowplrn8F-rmEibyOya9khvbTLQ5uoF2JktsTAHIFgunRSqL3TVEhgZZAmwF_-Wm9y35Ia6ygQkB66n8YfCFecGC5B_pY8O58pFOgc69jFLLHz7oEycxtEa9uQNFx0V4xe4iHwfPvV7eeG43xr4rTQmgrVYj2fH7-wKoIyRn_QoPpDOrr87tg", "bookingUrl": "/cn/booking/?direct=true&locale=cn&currency=cny&passengers=1-0-0&token=HvIlqHx8JZuHs52J5xiE6LOT-olIDLcGElmlN26PangvPGyaoLvLQT59GhSqrmlEpfERo_Z8XdofYnXIrGHAx449JGuar-LTXqQxaog8TjjOT09dmqnlQZYdWJJt1NWnhHSNk32N-ujt_PeqXFEsGikDqZLeE-xg4N66tEaR3AmdLyFsqrJDAFLZMrn_erdJ5JE2EmvSMOOeU5A5k9ltM18jnE4Y9lywOP8h4crGv2U66DIjn1JGasFqo3-hEdZ1_vihzlayP73HAFPbP3yzftln5l3FVkJRPNpqLPBQR7ukg4i3NKcHO3844-KK9JEyo4Fzfzl0YQ-aQvb82GlNsRMGJTZR-xFZicOmhNP8RPV0vESEoqThOQdB-zZefE76wVDtOzGFcmfwaK6pUTzMiPnjWIZRHulw87sMMVH901H7IYDyZwExZ9jX4YyD4f3KdHgLwm7DvNws7b94HX99431NOAeOl5HUXezZR6DMV_UQBmGe-yYVZFHP-SmhRuF1J26tpbYCmBxrJqOH18pENCVWYd46Dss71Z8NrE5i1K2Xj1jfZVZVaMPWAlRpvvmtVE4ZyG4fYxFXDWuXyONf49iI9PnJ-9VESw1tk9L5GJgFvzayQsWcMyEirCVpAy9Jwpk0WDPAhadU49ki7knA--xOG8RKVL-1wxQf5S25ZXW4tvvT2jVEs_X1exZSucTgNsgcHvFeYTQGbbVAORuHSz-IraSxvyd62mDS6ZAXH9yQyZJ2FBYF_77lkqW4Ow5VoZ4GHKQc1WG9yB0vqwXjEYQsQpCMDfB5H2bdbpiwrJ5tXJW3Y8qbj_mTKL5xPkNPbg6lXIr-RUdKP3_4y6uQPEtfB3R8mhX9QmjUzVGEfh35puZPNKgYsR2pF-btQGzJ94fGwh8H6oIcY7WdpDbQhb7mGz4LJbit_2RfAM3W5QO_K3XNxxXHyyozDrFdf4HFSIZqd3RK_jI1Jowplrn8F-rmEibyOya9khvbTLQ5uoF2JktsTAHIFgunRSqL3TVEhgZZAmwF_-Wm9y35Ia6ygQkB66n8YfCFecGC5B_pY8O58pFOgc69jFLLHz7oEycxtEa9uQNFx0V4xe4iHwfPvV7eeG43xr4rTQmgrVYj2fH7-wKoIyRn_QoPpDOrr87tg&searchType=oneWay&searchBags=0.0", "trackingPixel": "https://www.kiwi.com/pixel?affilid=cj&currency=CNY&flightsId=0f6417874f1b00008e11ea39_0%7C0f6417874f1b00008e11ea39_1&from=LHR&lang=cn&passengers=1&searchBags=0.0&to=PKX&booking_token=HvIlqHx8JZuHs52J5xiE6LOT-olIDLcGElmlN26PangvPGyaoLvLQT59GhSqrmlEpfERo_Z8XdofYnXIrGHAx449JGuar-LTXqQxaog8TjjOT09dmqnlQZYdWJJt1NWnhHSNk32N-ujt_PeqXFEsGikDqZLeE-xg4N66tEaR3AmdLyFsqrJDAFLZMrn_erdJ5JE2EmvSMOOeU5A5k9ltM18jnE4Y9lywOP8h4crGv2U66DIjn1JGasFqo3-hEdZ1_vihzlayP73HAFPbP3yzftln5l3FVkJRPNpqLPBQR7ukg4i3NKcHO3844-KK9JEyo4Fzfzl0YQ-aQvb82GlNsRMGJTZR-xFZicOmhNP8RPV0vESEoqThOQdB-zZefE76wVDtOzGFcmfwaK6pUTzMiPnjWIZRHulw87sMMVH901H7IYDyZwExZ9jX4YyD4f3KdHgLwm7DvNws7b94HX99431NOAeOl5HUXezZR6DMV_UQBmGe-yYVZFHP-SmhRuF1J26tpbYCmBxrJqOH18pENCVWYd46Dss71Z8NrE5i1K2Xj1jfZVZVaMPWAlRpvvmtVE4ZyG4fYxFXDWuXyONf49iI9PnJ-9VESw1tk9L5GJgFvzayQsWcMyEirCVpAy9Jwpk0WDPAhadU49ki7knA--xOG8RKVL-1wxQf5S25ZXW4tvvT2jVEs_X1exZSucTgNsgcHvFeYTQGbbVAORuHSz-IraSxvyd62mDS6ZAXH9yQyZJ2FBYF_77lkqW4Ow5VoZ4GHKQc1WG9yB0vqwXjEYQsQpCMDfB5H2bdbpiwrJ5tXJW3Y8qbj_mTKL5xPkNPbg6lXIr-RUdKP3_4y6uQPEtfB3R8mhX9QmjUzVGEfh35puZPNKgYsR2pF-btQGzJ94fGwh8H6oIcY7WdpDbQhb7mGz4LJbit_2RfAM3W5QO_K3XNxxXHyyozDrFdf4HFSIZqd3RK_jI1Jowplrn8F-rmEibyOya9khvbTLQ5uoF2JktsTAHIFgunRSqL3TVEhgZZAmwF_-Wm9y35Ia6ygQkB66n8YfCFecGC5B_pY8O58pFOgc69jFLLHz7oEycxtEa9uQNFx0V4xe4iHwfPvV7eeG43xr4rTQmgrVYj2fH7-wKoIyRn_QoPpDOrr87tg", "itineraryProvider": {"code": "KIWI", "name": "Kiwi.com", "subprovider": null, "hasHighProbabilityOfPriceChange": false, "contentProvider": {"code": "KIWI"}, "providerCategory": null, "id": "ItineraryProvider:KIWI"}, "price": {"amount": "4209"}, "priceEur": {"amount": "515.54"}, "priceLocks": {"priceLocksCurr": [{"default": false, "price": {"amount": "210.45", "roundedFormattedValue": "¥210"}}, {"default": true, "price": {"amount": "420.9", "roundedFormattedValue": "¥421"}}, {"default": false, "price": {"amount": "1052.25", "roundedFormattedValue": "¥1,052"}}], "priceLocksEur": [{"default": false, "price": {"amount": "25.78", "roundedFormattedValue": "26 €"}}, {"default": true, "price": {"amount": "51.55", "roundedFormattedValue": "52 €"}}, {"default": false, "price": {"amount": "128.89", "roundedFormattedValue": "129 €"}}]}, "kiwiProduct": "KIWI_BENEFITS", "disruptionTreatment": "DISRUPTION_PROTECTION", "usRulesApply": null}}]}, "travelHack": {"isTrueHiddenCity": true, "isVirtualInterlining": false, "isThrowawayTicket": false}, "duration": 36900, "pnrCount": 1, "sector": {"id": "Sector:adcfbe213ec4e887f1b4d0174d49824958a2cc45~adcfbe213ec4e887f1b4d0174d49824958a2cc45", "sectorSegments": [{"segment": {"source": {"station": {"code": "LHR", "type": "AIRPORT", "city": {"name": "伦敦", "id": "City:london_gb", "legacyId": "london_gb"}, "id": "Station:airport:LHR", "legacyId": "LHR", "name": "伦敦希思罗机场", "gps": {"lat": 51.4775, "lng": -0.4613889}, "country": {"code": "GB", "id": "Country:GB"}}, "localTime": "2025-06-12T21:00:00", "utcTimeIso": "2025-06-12T20:00:00Z"}, "destination": {"station": {"code": "PKX", "type": "AIRPORT", "city": {"name": "北京市", "id": "City:beijing_cn", "legacyId": "beijing_cn"}, "id": "Station:airport:PKX", "legacyId": "PKX", "name": "北京大兴国际机场", "gps": {"lat": 39.509167, "lng": 116.410556}, "country": {"code": "CN", "id": "Country:CN"}}, "localTime": "2025-06-13T14:15:00", "utcTimeIso": "2025-06-13T06:15:00Z"}, "id": "Segment:adcfbe213ec4e887f1b4d0174d49824958a2cc45", "duration": 36900, "type": "FLIGHT", "code": "674", "carrier": {"id": "Carrier:242", "name": "China Southern Airlines", "code": "CZ"}, "operatingCarrier": {"id": "Carrier:242", "name": "China Southern Airlines", "code": "CZ"}, "cabinClass": "ECONOMY", "hiddenDestination": {"code": "HAK", "name": "海口美兰国际机场", "city": {"name": "海口市", "id": "City:haikou_cn"}, "country": {"name": "中国", "id": "Country:CN"}, "id": "Station:airport:HAK"}, "throwawayDestination": null}, "guarantee": null, "layover": null}], "duration": 36900}, "legacyId": "0f6417874f1b00008e11ea39_0", "lastAvailable": {"seatsLeft": 7}, "isRyanair": false, "benefitsData": {"automaticCheckinAvailable": false, "instantChatSupportAvailable": true, "disruptionProtectionAvailable": true, "guaranteeAvailable": false, "guaranteeFee": null, "guaranteeFeeEur": null, "searchReferencePrice": {"roundedAmount": "3805"}}, "isAirBaggageBundleEligible": null, "testEligibilityInformation": {"paretoABTestNewItinerary": null}}, {"__typename": "ItineraryOneWay", "__isItinerary": "ItineraryOneWay", "id": "ItineraryOneWay:************************************************************************************************************************************************************", "shareId": "****************************************************************************************************************************************************************************", "price": {"amount": "4319", "priceBeforeDiscount": "4319"}, "priceEur": {"amount": "529.017122"}, "provider": {"name": "Kiwi.com", "code": "KIWI-BASIC", "hasHighProbabilityOfPriceChange": false, "contentProvider": null, "id": "ItineraryProvider:KIWI-BASIC"}, "bagsInfo": {"includedCheckedBags": 1, "includedHandBags": 1, "hasNoBaggageSupported": false, "hasNoCheckedBaggage": false, "checkedBagTiers": [{"tierPrice": {"amount": "0"}, "bags": [{"weight": {"value": 23}}]}], "handBagTiers": [{"tierPrice": {"amount": "0"}, "bags": [{"weight": {"value": 8}}]}], "includedPersonalItem": 1, "personalItemTiers": [{"tierPrice": {"amount": "0"}, "bags": [{"weight": {"value": 8}, "height": {"value": 30}, "width": {"value": 15}, "length": {"value": 40}}]}]}, "bookingOptions": {"edges": [{"node": {"token": "HaIg7MxlokaLtA6ozoVsc4IvzWp752tgrnLRJa8bmU_Zg6eRQppywOB5pjxD9i3vJdTX63xvgwNNY5bvGP4zHlXtCCh20Q5Y2oOufzJquO_AXluf7FqjzgafG0vfEPNMedpg-njFN83NCNtEktcX9tHe7y2JxNqc-WmQX3ymctWc_av22x5U0YBK1JJClr2_3-KaFTLd7LQNKOTb1Ttx3rnK4__lQ-awbLNmgy_HqMQhKt3TauHYNeB4WNOEXvcO8mV1dstX2rdH4jMRnZviIt3IjkbnPotCXzz0owNt9hIetw1RVW29qJohYcSXdE_qb2gTS3GERUAyq_eboZUkhwyoRMKYpXP9hARL0g7-Iqrc90S2oWhGwwhuYSk9BpnWddvgr23_eKgpMXH8mlT8pwD8FgwA5HswVSGRTmCWYZJhEfI2rLh9shjYdUnzHJsQ3R6Q36m5MLTzwr7YA5maswMs9yHs11Z5tYhBn6Z38xDp-UcPC7XLmN7VD8oEa1d5uwczskpNmq1DZUDr_vxdaTMxsFgzKOzTc2_gPJqoNukCPqTzdSDRUifcFDZbDoZIaAJnYPXY5vO0SuXA22GcAyvnSVcWL4Wt682WSKPnW8ueptWhaW3j3RtJqzWuBs16YkZqWSyHq1XfNn9uEUjx37O5UTwt1Y0xiuo7WoGUw9R-yrPycgwiL4rpa2rJQcR-LGq72CJGYMKe_lzGl9ZRVzz3pMXWWvVGaraTH6Y-P2y9HyHKDWkvZwLClCVQ9xTCR8wJaf7zYq-ZwQR2VgzV7-k94d1s03NhXKFXi2TI_r8yKJjh2qrjdWc6t-GVr27wUIYbCqFjoGmLg3yLTDW763ooPLjiXUOT1vH-FKCCei6-EGoFxzopCtAgI34yRv2JcVAWIKd804hTkOViiB_qfA6BxgGFrs0k1ZHtqsBXL_FZlnweWrVT_9pcja-2B6BV-bWe0kDiAEof7Ydav0jVYj5UXGsdK_qjItsnMZDXJUAUqLGxmL86E5kRmQ9z6BlrWJExExbSe6Br6Ofx93YDQYw==", "bookingUrl": "/cn/booking/?direct=true&locale=cn&currency=cny&passengers=1-0-0&token=HaIg7MxlokaLtA6ozoVsc4IvzWp752tgrnLRJa8bmU_Zg6eRQppywOB5pjxD9i3vJdTX63xvgwNNY5bvGP4zHlXtCCh20Q5Y2oOufzJquO_AXluf7FqjzgafG0vfEPNMedpg-njFN83NCNtEktcX9tHe7y2JxNqc-WmQX3ymctWc_av22x5U0YBK1JJClr2_3-KaFTLd7LQNKOTb1Ttx3rnK4__lQ-awbLNmgy_HqMQhKt3TauHYNeB4WNOEXvcO8mV1dstX2rdH4jMRnZviIt3IjkbnPotCXzz0owNt9hIetw1RVW29qJohYcSXdE_qb2gTS3GERUAyq_eboZUkhwyoRMKYpXP9hARL0g7-Iqrc90S2oWhGwwhuYSk9BpnWddvgr23_eKgpMXH8mlT8pwD8FgwA5HswVSGRTmCWYZJhEfI2rLh9shjYdUnzHJsQ3R6Q36m5MLTzwr7YA5maswMs9yHs11Z5tYhBn6Z38xDp-UcPC7XLmN7VD8oEa1d5uwczskpNmq1DZUDr_vxdaTMxsFgzKOzTc2_gPJqoNukCPqTzdSDRUifcFDZbDoZIaAJnYPXY5vO0SuXA22GcAyvnSVcWL4Wt682WSKPnW8ueptWhaW3j3RtJqzWuBs16YkZqWSyHq1XfNn9uEUjx37O5UTwt1Y0xiuo7WoGUw9R-yrPycgwiL4rpa2rJQcR-LGq72CJGYMKe_lzGl9ZRVzz3pMXWWvVGaraTH6Y-P2y9HyHKDWkvZwLClCVQ9xTCR8wJaf7zYq-ZwQR2VgzV7-k94d1s03NhXKFXi2TI_r8yKJjh2qrjdWc6t-GVr27wUIYbCqFjoGmLg3yLTDW763ooPLjiXUOT1vH-FKCCei6-EGoFxzopCtAgI34yRv2JcVAWIKd804hTkOViiB_qfA6BxgGFrs0k1ZHtqsBXL_FZlnweWrVT_9pcja-2B6BV-bWe0kDiAEof7Ydav0jVYj5UXGsdK_qjItsnMZDXJUAUqLGxmL86E5kRmQ9z6BlrWJExExbSe6Br6Ofx93YDQYw%3D%3D&searchType=oneWay&searchBags=0.0", "trackingPixel": "https://www.kiwi.com/pixel?affilid=cj&currency=CNY&flightsId=0f640fa04f1b0000902a7bb2_0&from=LHR&lang=cn&passengers=1&searchBags=0.0&to=PKX&booking_token=HaIg7MxlokaLtA6ozoVsc4IvzWp752tgrnLRJa8bmU_Zg6eRQppywOB5pjxD9i3vJdTX63xvgwNNY5bvGP4zHlXtCCh20Q5Y2oOufzJquO_AXluf7FqjzgafG0vfEPNMedpg-njFN83NCNtEktcX9tHe7y2JxNqc-WmQX3ymctWc_av22x5U0YBK1JJClr2_3-KaFTLd7LQNKOTb1Ttx3rnK4__lQ-awbLNmgy_HqMQhKt3TauHYNeB4WNOEXvcO8mV1dstX2rdH4jMRnZviIt3IjkbnPotCXzz0owNt9hIetw1RVW29qJohYcSXdE_qb2gTS3GERUAyq_eboZUkhwyoRMKYpXP9hARL0g7-Iqrc90S2oWhGwwhuYSk9BpnWddvgr23_eKgpMXH8mlT8pwD8FgwA5HswVSGRTmCWYZJhEfI2rLh9shjYdUnzHJsQ3R6Q36m5MLTzwr7YA5maswMs9yHs11Z5tYhBn6Z38xDp-UcPC7XLmN7VD8oEa1d5uwczskpNmq1DZUDr_vxdaTMxsFgzKOzTc2_gPJqoNukCPqTzdSDRUifcFDZbDoZIaAJnYPXY5vO0SuXA22GcAyvnSVcWL4Wt682WSKPnW8ueptWhaW3j3RtJqzWuBs16YkZqWSyHq1XfNn9uEUjx37O5UTwt1Y0xiuo7WoGUw9R-yrPycgwiL4rpa2rJQcR-LGq72CJGYMKe_lzGl9ZRVzz3pMXWWvVGaraTH6Y-P2y9HyHKDWkvZwLClCVQ9xTCR8wJaf7zYq-ZwQR2VgzV7-k94d1s03NhXKFXi2TI_r8yKJjh2qrjdWc6t-GVr27wUIYbCqFjoGmLg3yLTDW763ooPLjiXUOT1vH-FKCCei6-EGoFxzopCtAgI34yRv2JcVAWIKd804hTkOViiB_qfA6BxgGFrs0k1ZHtqsBXL_FZlnweWrVT_9pcja-2B6BV-bWe0kDiAEof7Ydav0jVYj5UXGsdK_qjItsnMZDXJUAUqLGxmL86E5kRmQ9z6BlrWJExExbSe6Br6Ofx93YDQYw==", "itineraryProvider": {"code": "KIWI-BASIC", "name": "Kiwi.com", "subprovider": null, "hasHighProbabilityOfPriceChange": false, "contentProvider": null, "providerCategory": null, "id": "ItineraryProvider:KIWI-BASIC"}, "price": {"amount": "4319"}, "priceEur": {"amount": "529.02"}, "priceLocks": {"priceLocksCurr": [{"default": false, "price": {"amount": "215.95", "roundedFormattedValue": "¥216"}}, {"default": true, "price": {"amount": "431.9", "roundedFormattedValue": "¥432"}}, {"default": false, "price": {"amount": "1079.75", "roundedFormattedValue": "¥1,080"}}], "priceLocksEur": [{"default": false, "price": {"amount": "26.45", "roundedFormattedValue": "26 €"}}, {"default": true, "price": {"amount": "52.9", "roundedFormattedValue": "53 €"}}, {"default": false, "price": {"amount": "132.25", "roundedFormattedValue": "132 €"}}]}, "kiwiProduct": "KIWI_BASIC", "disruptionTreatment": "NO_PROTECTION", "usRulesApply": false}}, {"node": {"token": "HP28yUi2Y0vNvVFe3dqH9mecx2RfQhvkOOfV2-xaaPrRDOewLwTiC6Bk-P-LfwD9fT5vX5wSp-Cs6kla_KWigiiuTCCwdkGrkPhqNWFvd0PaZDiYo3avmbQB06f3TmM448zyn2bCHiC5AnR8uJcuBzNQXtCfSykm2kPnjlzkM18hFUx0GMqCiTxLLvKwmMDkFaTQ3pxCZA-W-zcqYK_JcuCreBkaGxN6XNxasTJi9D4fvkuWUN6Aj-0Nd90lThaetCP5ZpXy3X9PtAktm9Yn69te-pRCCKw3OJ4YZqm2yv5w-Msf1zD2zX_dGIAL-ynEzrvDWvQVveSOn8-nLf4SLISGsvCg6yxfobq_rpzi7vzOCxNW1F5ov1b2FC-NJVjyi9drRF5mEiBG2Qwh0upqTT-BQYCdfixg-GPwad13c6DFAzqz-TZDboAtBJhnh_NIcz2T08Bo8hya-jtw7F9-fOLMh6wv-j43ND5oQbD7FvV0ZNjsq2ceNe5HLPl8yyO_3afXCYwD2Ae1nE3bp8qtSwaC9A6LBjNFo06cIajDS3Z3dcZLFv7mkg9DTIW4GIRCX7iSrPOkuF-B0xEXRe4XC6sHlibgCjXTunnvGaj_v5wqtbMUVOvPu1DSd9pScRznCUP-L8uVqvZ_AmDd-cKPezopBY1fp5OPu7bwepd7JcSIDIOCqP2udMzasKSpJVm8MaziQUwJw2ltGHPZfqNVUwOq15PHxZ4Mged1arnqEcsqZLhLgPCJNZxOYyyamb0vOUfziO0D3lJ-LmbqyK1cijWs6lUdurcqugm9Dq3l0JLl79w41ElR5b74nLJYzqqcT_My7rH_okpiSC0QY48Ej_nzsTnrcRNMoBuCZyugl3nbTqUWraRp9SEByheEk_gZ70FPl2nfNyx20tnYR6xbqiiv0GrW2HzG3P2Aoo1pj-aojjGVhbn-X5mYezDEaJp9C8S1XjyjScr8ZHKIcKWs2QevMpXmnw4stXLKHkLNZ8axBIr5iGR8ooTsAkZGJ8qPh_yaiZ_6pdXNvV_LWFrJk-T5Zm8dM-EgCHyyFyxXM8zeVGEm_1aU99bMAD85O1sAt1DlLJ0TthfU6Os4ExD9yOQs5CSFUSUgR9iLgQ1Jq1PQ=", "bookingUrl": "/cn/booking/?direct=true&locale=cn&currency=cny&passengers=1-0-0&token=HP28yUi2Y0vNvVFe3dqH9mecx2RfQhvkOOfV2-xaaPrRDOewLwTiC6Bk-P-LfwD9fT5vX5wSp-Cs6kla_KWigiiuTCCwdkGrkPhqNWFvd0PaZDiYo3avmbQB06f3TmM448zyn2bCHiC5AnR8uJcuBzNQXtCfSykm2kPnjlzkM18hFUx0GMqCiTxLLvKwmMDkFaTQ3pxCZA-W-zcqYK_JcuCreBkaGxN6XNxasTJi9D4fvkuWUN6Aj-0Nd90lThaetCP5ZpXy3X9PtAktm9Yn69te-pRCCKw3OJ4YZqm2yv5w-Msf1zD2zX_dGIAL-ynEzrvDWvQVveSOn8-nLf4SLISGsvCg6yxfobq_rpzi7vzOCxNW1F5ov1b2FC-NJVjyi9drRF5mEiBG2Qwh0upqTT-BQYCdfixg-GPwad13c6DFAzqz-TZDboAtBJhnh_NIcz2T08Bo8hya-jtw7F9-fOLMh6wv-j43ND5oQbD7FvV0ZNjsq2ceNe5HLPl8yyO_3afXCYwD2Ae1nE3bp8qtSwaC9A6LBjNFo06cIajDS3Z3dcZLFv7mkg9DTIW4GIRCX7iSrPOkuF-B0xEXRe4XC6sHlibgCjXTunnvGaj_v5wqtbMUVOvPu1DSd9pScRznCUP-L8uVqvZ_AmDd-cKPezopBY1fp5OPu7bwepd7JcSIDIOCqP2udMzasKSpJVm8MaziQUwJw2ltGHPZfqNVUwOq15PHxZ4Mged1arnqEcsqZLhLgPCJNZxOYyyamb0vOUfziO0D3lJ-LmbqyK1cijWs6lUdurcqugm9Dq3l0JLl79w41ElR5b74nLJYzqqcT_My7rH_okpiSC0QY48Ej_nzsTnrcRNMoBuCZyugl3nbTqUWraRp9SEByheEk_gZ70FPl2nfNyx20tnYR6xbqiiv0GrW2HzG3P2Aoo1pj-aojjGVhbn-X5mYezDEaJp9C8S1XjyjScr8ZHKIcKWs2QevMpXmnw4stXLKHkLNZ8axBIr5iGR8ooTsAkZGJ8qPh_yaiZ_6pdXNvV_LWFrJk-T5Zm8dM-EgCHyyFyxXM8zeVGEm_1aU99bMAD85O1sAt1DlLJ0TthfU6Os4ExD9yOQs5CSFUSUgR9iLgQ1Jq1PQ%3D&searchType=oneWay&searchBags=0.0", "trackingPixel": "https://www.kiwi.com/pixel?affilid=cj&currency=CNY&flightsId=0f640fa04f1b0000902a7bb2_0&from=LHR&lang=cn&passengers=1&searchBags=0.0&to=PKX&booking_token=HP28yUi2Y0vNvVFe3dqH9mecx2RfQhvkOOfV2-xaaPrRDOewLwTiC6Bk-P-LfwD9fT5vX5wSp-Cs6kla_KWigiiuTCCwdkGrkPhqNWFvd0PaZDiYo3avmbQB06f3TmM448zyn2bCHiC5AnR8uJcuBzNQXtCfSykm2kPnjlzkM18hFUx0GMqCiTxLLvKwmMDkFaTQ3pxCZA-W-zcqYK_JcuCreBkaGxN6XNxasTJi9D4fvkuWUN6Aj-0Nd90lThaetCP5ZpXy3X9PtAktm9Yn69te-pRCCKw3OJ4YZqm2yv5w-Msf1zD2zX_dGIAL-ynEzrvDWvQVveSOn8-nLf4SLISGsvCg6yxfobq_rpzi7vzOCxNW1F5ov1b2FC-NJVjyi9drRF5mEiBG2Qwh0upqTT-BQYCdfixg-GPwad13c6DFAzqz-TZDboAtBJhnh_NIcz2T08Bo8hya-jtw7F9-fOLMh6wv-j43ND5oQbD7FvV0ZNjsq2ceNe5HLPl8yyO_3afXCYwD2Ae1nE3bp8qtSwaC9A6LBjNFo06cIajDS3Z3dcZLFv7mkg9DTIW4GIRCX7iSrPOkuF-B0xEXRe4XC6sHlibgCjXTunnvGaj_v5wqtbMUVOvPu1DSd9pScRznCUP-L8uVqvZ_AmDd-cKPezopBY1fp5OPu7bwepd7JcSIDIOCqP2udMzasKSpJVm8MaziQUwJw2ltGHPZfqNVUwOq15PHxZ4Mged1arnqEcsqZLhLgPCJNZxOYyyamb0vOUfziO0D3lJ-LmbqyK1cijWs6lUdurcqugm9Dq3l0JLl79w41ElR5b74nLJYzqqcT_My7rH_okpiSC0QY48Ej_nzsTnrcRNMoBuCZyugl3nbTqUWraRp9SEByheEk_gZ70FPl2nfNyx20tnYR6xbqiiv0GrW2HzG3P2Aoo1pj-aojjGVhbn-X5mYezDEaJp9C8S1XjyjScr8ZHKIcKWs2QevMpXmnw4stXLKHkLNZ8axBIr5iGR8ooTsAkZGJ8qPh_yaiZ_6pdXNvV_LWFrJk-T5Zm8dM-EgCHyyFyxXM8zeVGEm_1aU99bMAD85O1sAt1DlLJ0TthfU6Os4ExD9yOQs5CSFUSUgR9iLgQ1Jq1PQ=", "itineraryProvider": {"code": "KIWI", "name": "Kiwi.com", "subprovider": null, "hasHighProbabilityOfPriceChange": false, "contentProvider": {"code": "KIWI"}, "providerCategory": null, "id": "ItineraryProvider:KIWI"}, "price": {"amount": "4799"}, "priceEur": {"amount": "587.81"}, "priceLocks": {"priceLocksCurr": [{"default": false, "price": {"amount": "239.95", "roundedFormattedValue": "¥240"}}, {"default": true, "price": {"amount": "479.9", "roundedFormattedValue": "¥480"}}, {"default": false, "price": {"amount": "1199.75", "roundedFormattedValue": "¥1,200"}}], "priceLocksEur": [{"default": false, "price": {"amount": "29.39", "roundedFormattedValue": "29 €"}}, {"default": true, "price": {"amount": "58.78", "roundedFormattedValue": "59 €"}}, {"default": false, "price": {"amount": "146.95", "roundedFormattedValue": "147 €"}}]}, "kiwiProduct": "KIWI_BENEFITS", "disruptionTreatment": "DISRUPTION_PROTECTION", "usRulesApply": null}}]}, "travelHack": {"isTrueHiddenCity": false, "isVirtualInterlining": false, "isThrowawayTicket": false}, "duration": 36900, "pnrCount": 1, "sector": {"id": "Sector:4fff6654af63bb4f848453d32185cfb1e8fbdab5~4fff6654af63bb4f848453d32185cfb1e8fbdab5", "sectorSegments": [{"segment": {"source": {"station": {"code": "LHR", "type": "AIRPORT", "city": {"name": "伦敦", "id": "City:london_gb", "legacyId": "london_gb"}, "id": "Station:airport:LHR", "legacyId": "LHR", "name": "伦敦希思罗机场", "gps": {"lat": 51.4775, "lng": -0.4613889}, "country": {"code": "GB", "id": "Country:GB"}}, "localTime": "2025-06-12T21:00:00", "utcTimeIso": "2025-06-12T20:00:00Z"}, "destination": {"station": {"code": "PKX", "type": "AIRPORT", "city": {"name": "北京市", "id": "City:beijing_cn", "legacyId": "beijing_cn"}, "id": "Station:airport:PKX", "legacyId": "PKX", "name": "北京大兴国际机场", "gps": {"lat": 39.509167, "lng": 116.410556}, "country": {"code": "CN", "id": "Country:CN"}}, "localTime": "2025-06-13T14:15:00", "utcTimeIso": "2025-06-13T06:15:00Z"}, "id": "Segment:4fff6654af63bb4f848453d32185cfb1e8fbdab5", "duration": 36900, "type": "FLIGHT", "code": "674", "carrier": {"id": "Carrier:242", "name": "China Southern Airlines", "code": "CZ"}, "operatingCarrier": {"id": "Carrier:242", "name": "China Southern Airlines", "code": "CZ"}, "cabinClass": "ECONOMY", "hiddenDestination": null, "throwawayDestination": null}, "guarantee": null, "layover": null}], "duration": 36900}, "legacyId": "0f640fa04f1b0000902a7bb2_0", "lastAvailable": {"seatsLeft": 9}, "isRyanair": false, "benefitsData": {"automaticCheckinAvailable": false, "instantChatSupportAvailable": true, "disruptionProtectionAvailable": true, "guaranteeAvailable": false, "guaranteeFee": null, "guaranteeFeeEur": null, "searchReferencePrice": {"roundedAmount": "4319"}}, "isAirBaggageBundleEligible": null, "testEligibilityInformation": {"paretoABTestNewItinerary": null}}]}}}