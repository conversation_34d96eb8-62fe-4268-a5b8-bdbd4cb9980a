#!/usr/bin/env python3
"""
测试头等舱搜索的脚本
"""

import requests
import json
from datetime import datetime, timedelta

def test_first_class_search():
    """测试头等舱搜索"""
    print("=== 测试头等舱搜索 ===")
    
    # 构建测试请求
    base_url = "http://localhost:8000"
    endpoint = "/api/v2/simplified-flights/search-simple"
    
    # 设置搜索日期为未来的日期
    departure_date = (datetime.now() + timedelta(days=30)).strftime("%Y-%m-%d")
    
    payload = {
        "origin_iata": "LHR",
        "destination_iata": "PKX", 
        "departure_date_from": departure_date,
        "departure_date_to": departure_date,
        "adults": 1,
        "cabin_class": "FIRST_CLASS",  # 使用前端映射后的值
        "preferred_currency": "CNY",
        "max_results_per_type": 5,
        "max_pages_per_search": 1,
        "direct_flights_only_for_primary": False
    }
    
    params = {
        "include_direct": True,
        "include_hidden_city": True
    }
    
    print(f"发送请求到: {base_url}{endpoint}")
    print(f"请求载荷: {json.dumps(payload, indent=2, ensure_ascii=False)}")
    print(f"查询参数: {params}")
    
    try:
        response = requests.post(
            f"{base_url}{endpoint}",
            json=payload,
            params=params,
            timeout=60
        )
        
        print(f"\n响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 请求成功!")
            print(f"搜索ID: {result.get('search_id', 'N/A')}")
            print(f"直飞航班数量: {len(result.get('direct_flights', []))}")
            print(f"隐藏城市航班数量: {len(result.get('hidden_city_flights', []))}")
            print(f"搜索耗时: {result.get('search_time_ms', 'N/A')} ms")
            
            if result.get('disclaimers'):
                print(f"免责声明: {result['disclaimers']}")
                
        else:
            print("❌ 请求失败!")
            print(f"错误响应: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求异常: {e}")
    except Exception as e:
        print(f"❌ 未知错误: {e}")

if __name__ == "__main__":
    test_first_class_search()
