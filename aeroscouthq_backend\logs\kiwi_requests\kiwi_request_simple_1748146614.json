{"query": "\n        query SearchOneWayItinerariesQuery(\n          $search: SearchOnewayInput\n          $filter: ItinerariesFilterInput\n          $options: ItinerariesOptionsInput\n          $conditions: Boolean!\n        ) {\n          onewayItineraries(search: $search, filter: $filter, options: $options) {\n            __typename\n            ... on AppError {\n              error: message\n            }\n            ... on Itineraries {\n              server {\n                requestId\n                environment\n                packageVersion\n                serverToken\n              }\n              metadata {\n                eligibilityInformation {\n                  baggageEligibilityInformation {\n                    topFiveResultsBaggageEligibleForPrompt\n                    numberOfBags\n                  }\n                  guaranteeAndRedirectsEligibilityInformation {\n                    redirect {\n                      anywhere\n                      top10\n                      isKiwiAvailable\n                    }\n                    guarantee {\n                      anywhere\n                      top10\n                    }\n                    combination {\n                      anywhere\n                      top10\n                    }\n                  }\n                  kiwiBasicEligibility {\n                    anywhere\n                    top10\n                  }\n                  topThreeResortingOccurred\n                  carriersDeeplinkEligibility\n                  responseContainsKayakItinerary\n                  paretoABTestEligible\n                }\n                carriers {\n                  code\n                  id\n                  name\n                }\n                ...AirlinesFilter_data\n                ...CountriesFilter_data\n                ...WeekDaysFilter_data\n                ...TravelTip_data\n                ...Sorting_data\n                ...useSortingModes_data\n                ...PriceAlert_data\n                itinerariesCount\n                hasMorePending\n                missingProviders {\n                  code\n                }\n                searchFingerprint\n                statusPerProvider {\n                  provider {\n                    id\n                  }\n                  errorHappened\n                  errorMessage\n                }\n                hasTier1MarketItineraries\n                sharedItinerary { # 如果需要分享功能，否则可以考虑移除以简化\n                  __typename\n                  ...TripInfo\n                  ...ItineraryDebug @include(if: $conditions)\n                  # ... (sharedItinerary 展开的类型和字段) ...\n                  # 这部分如果不需要可以大大简化 query\n                }\n                kayakEligibilityTest {\n                  containsKayakWithNewRules\n                  containsKayakWithCurrentRules\n                  containsKayakAirlinesWithNewRules\n                }\n              }\n              itineraries {\n                __typename\n                ...TripInfo # 主要行程信息从此 Fragment 获取\n                ...ItineraryDebug @include(if: $conditions) # 条件性调试信息\n                ... on ItineraryOneWay {\n                  # ItineraryOneWay 特有的一些字段，不在TripInfo里的\n                  legacyId # 例如\n                  # sector { id duration ... } # sector 的部分核心信息可能在 TripInfo, 更细节的直接请求\n                  # 如果 TripInfo 包含了 sector 的全部所需信息，这里就不需要重复\n                  # 但通常 ItineraryOneWay 会直接请求 sector 及其子结构\n\n                  # 以下是示例，实际应基于 TripInfo 和你的需求决定哪些在此处直接请求\n                  sector {\n                      id\n                      duration\n                      sectorSegments {\n                          guarantee\n                          segment {\n                              id\n                              source {\n                                  localTime\n                                  utcTimeIso\n                                  station {\n                                      # ... (从 PrebookingStation 或直接请求) ...\n                                      # 基于你的响应，这里请求了全部细节\n                                      id legacyId name code type gps { lat lng }\n                                      city { legacyId name id }\n                                      country { code id }\n                                  }\n                              }\n                              destination {\n                                  localTime\n                                  utcTimeIso\n                                  station {\n                                      # ... (同上) ...\n                                      id legacyId name code type gps { lat lng }\n                                      city { legacyId name id }\n                                      country { code id }\n                                  }\n                              }\n                              duration\n                              type\n                              code # flight number\n                              carrier { id name code }\n                              operatingCarrier { id name code }\n                              cabinClass\n                              hiddenDestination { # 重要，为隐藏城市信息\n                                  code name city { name id } country { name id } id\n                              }\n                              throwawayDestination { id }\n                          }\n                          layover {\n                              duration isBaggageRecheck isWalkingDistance transferDuration id\n                          }\n                      }\n                  }\n                  lastAvailable { seatsLeft }\n                  isRyanair\n                  benefitsData { # 内容省略，按需添加\n                      automaticCheckinAvailable instantChatSupportAvailable disruptionProtectionAvailable\n                      guaranteeAvailable guaranteeFee { roundedAmount } guaranteeFeeEur { amount }\n                      searchReferencePrice { roundedAmount }\n                  }\n                  isAirBaggageBundleEligible\n                  testEligibilityInformation { paretoABTestNewItinerary }\n                }\n                id # Itinerary 顶层的ID\n              }\n            }\n          }\n        }\n\n        # --- 以下是所有必须的 Fragment 定义 ---\n        fragment AirlinesFilter_data on ItinerariesMetadata {\n          carriers { id code name }\n        }\n        fragment CountriesFilter_data on ItinerariesMetadata {\n          stopoverCountries { code name id }\n        }\n        fragment ItineraryDebug on Itinerary {\n          __isItinerary: __typename\n          itineraryDebugData { debug }\n        }\n        fragment PrebookingStation on Station {\n          code type city { name id }\n        }\n        fragment PriceAlert_data on ItinerariesMetadata {\n          priceAlertExists existingPriceAlert { id } searchFingerprint hasMorePending\n          priceAlertsTopResults {\n            best { price { amount } } cheapest { price { amount } } fastest { price { amount } }\n            sourceTakeoffAsc { price { amount } } destinationLandingAsc { price { amount } }\n          }\n        }\n        fragment Sorting_data on ItinerariesMetadata {\n          topResults {\n            best { __typename duration price { amount } id }\n            cheapest { __typename duration price { amount } id }\n            fastest { __typename duration price { amount } id }\n            sourceTakeoffAsc { __typename duration price { amount } id }\n            destinationLandingAsc { __typename duration price { amount } id }\n          }\n        }\n        fragment TravelTip_data on ItinerariesMetadata {\n          travelTips { # 内容非常多，后端按需解析或直接透传\n            __typename\n            ... on TravelTipRadiusMoney { radius params { name value } savingMoney: saving { amount currency { id code name } formattedValue } location { __typename id legacyId name slug } }\n            ... on TravelTipRadiusTime { radius params { name value } saving location { __typename id legacyId name slug } }\n            ... on TravelTipRadiusSome { radius params { name value } location { __typename id legacyId name slug } }\n            ... on TravelTipDateMoney { dates { start end } params { name value } savingMoney: saving { amount currency { id code name } formattedValue } }\n            ... on TravelTipDateTime { dates { start end } params { name value } saving }\n            ... on TravelTipDateSome { dates { start end } params { name value } }\n            ... on TravelTipExtend { destination { __typename id name slug } locations { __typename id name slug } price { amount currency { id code name } formattedValue } }\n          }\n        }\n        fragment TripInfo on Itinerary { # 核心行程信息\n          __isItinerary: __typename\n          id # 通常 id, shareId 在 Itinerary 级别获取，不一定在 TripInfo\n          shareId\n          price { amount priceBeforeDiscount }\n          priceEur { amount }\n          provider { name code hasHighProbabilityOfPriceChange contentProvider { code } id }\n          bagsInfo {\n            includedCheckedBags includedHandBags hasNoBaggageSupported hasNoCheckedBaggage\n            checkedBagTiers { tierPrice { amount } bags { weight { value } } }\n            handBagTiers { tierPrice { amount } bags { weight { value } } }\n            includedPersonalItem\n            personalItemTiers { tierPrice { amount } bags { weight { value } height { value } width { value } length { value } } }\n          }\n          bookingOptions {\n            edges {\n              node {\n                token bookingUrl trackingPixel\n                itineraryProvider { code name subprovider hasHighProbabilityOfPriceChange contentProvider { code } providerCategory id }\n                price { amount } priceEur { amount }\n                priceLocks {\n                  priceLocksCurr { default price { amount roundedFormattedValue } }\n                  priceLocksEur { default price { amount roundedFormattedValue } }\n                }\n                kiwiProduct disruptionTreatment usRulesApply\n              }\n            }\n          }\n          travelHack { isTrueHiddenCity isVirtualInterlining isThrowawayTicket }\n          duration\n          pnrCount\n          # sector 信息可以在这里定义一部分，或者在具体类型如 ItineraryOneWay 中定义\n          # 基于你的响应，TripInfo 里也包含了 sector 的基本结构\n          ... on ItineraryOneWay { # TripInfo 也可以针对不同类型有不同字段\n            sector {\n              id # 确保 TripInfo 包含的 sector 结构与主 query 一致\n              sectorSegments {\n                segment {\n                  source { station { ...PrebookingStation id } localTime }\n                  destination { station { ...PrebookingStation id } }\n                  id\n                }\n              }\n            }\n          }\n          # ... (其他 ItineraryReturn, ItineraryMulticity 的 TripInfo 展开)\n        }\n        fragment WeekDaysFilter_data on ItinerariesMetadata {\n          inboundDays outboundDays\n        }\n        fragment useSortingModes_data on ItinerariesMetadata { # 与 Sorting_data 类似\n          topResults {\n            best { __typename duration price { amount } id }\n            cheapest { __typename duration price { amount } id }\n            fastest { __typename duration price { amount } id }\n            sourceTakeoffAsc { __typename duration price { amount } id }\n            destinationLandingAsc { __typename duration price { amount } id }\n          }\n        }\n        ", "variables": {"search": {"itinerary": {"source": {"ids": ["Station:airport:LHR"]}, "destination": {"ids": ["Station:airport:PKX"]}, "outboundDepartureDate": {"start": "2025-06-12T00:00:00", "end": "2025-06-12T23:59:59"}}, "passengers": {"adults": 1, "children": 0, "infants": 0, "adultsHoldBags": [0], "adultsHandBags": [0], "childrenHoldBags": [], "childrenHandBags": []}, "cabinClass": {"cabinClass": "ECONOMY", "applyMixedClasses": false}}, "filter": {"allowChangeInboundDestination": true, "allowChangeInboundSource": true, "allowDifferentStationConnection": true, "enableSelfTransfer": true, "enableThrowAwayTicketing": true, "enableTrueHiddenCity": true, "maxStopsCount": 0, "transportTypes": ["FLIGHT"], "contentProviders": ["KIWI"], "flightsApiLimit": 25, "limit": 10}, "options": {"sortBy": "QUALITY", "mergePriceDiffRule": "INCREASED", "currency": "cny", "apiUrl": null, "locale": "cn", "market": "us", "partner": "skypicker", "partnerMarket": "cn", "affilID": "cj_5250933", "storeSearch": false, "searchStrategy": "REDUCED", "abTestInput": {"priceElasticityGuarantee": "DISABLE", "baggageProtectionBundle": "ENABLE", "paretoProtectVanilla": "ENABLE", "kiwiBasicThirdIteration": "C", "offerStrategiesNonT1": "DISABLE", "kayakWithoutBags": "DISABLE", "carriersDeeplinkResultsEnable": true, "carriersDeeplinkOnSEMEnable": true}, "serverToken": null, "searchSessionId": null}, "conditions": false}}