import asyncio
import concurrent.futures
import json
import logging
import os
import platform
import time
import traceback
from typing import Optional, Dict, Any
from pathlib import Path
from functools import partial

from playwright.async_api import async_playwright, Error as PlaywrightError
from fastapi import HTTPException, status
# import httpx # httpx is imported in the original request but not used in the provided logic. Keep if needed later.

# 添加异步直接获取函数
async def _direct_fetch_trip_headers() -> Optional[Dict[str, str]]:
    """
    直接异步获取Trip.com的headers，不依赖Celery任务。
    用作应急方案当Celery任务失败时。

    Returns:
        Dict[str, str]: 包含Trip.com headers的字典，如果失败则返回None
    """
    logger.info("开始异步直接获取Trip.com headers...")
    try:
        async with async_playwright() as p:
            browser = None
            context = None
            page = None
            try:
                browser = await p.chromium.launch(headless=True)
                context = await browser.new_context(
                    user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
                    locale="zh-HK",
                    timezone_id="Asia/Hong_Kong",
                )
                page = await context.new_page()

                target_url = "https://hk.trip.com/flights/"
                logger.info(f"导航到: {target_url}")

                try:
                    await page.goto(target_url, timeout=30000, wait_until="domcontentloaded")
                    logger.info("页面加载完成")
                except PlaywrightError as e:
                    logger.warning(f"页面加载出错，但继续执行: {e}")

                await page.wait_for_timeout(5000)

                # 尝试接受cookie横幅
                for selector in ['#ibu_cookie_banner_agree', 'button[class*="cookie_accept"]']:
                    try:
                        element = await page.query_selector(selector)
                        if element and await element.is_visible(timeout=2000):
                            logger.info(f"点击cookie按钮 ({selector})")
                            await element.click(timeout=3000)
                            await page.wait_for_timeout(1000)
                            break
                    except Exception:
                        pass

                # 获取cookies
                cookies_list = await context.cookies(urls=[target_url])
                if cookies_list:
                    cookies_str = "; ".join([f"{cookie['name']}={cookie['value']}" for cookie in cookies_list])

                    try:
                        user_agent = await page.evaluate("() => navigator.userAgent")
                    except Exception as e:
                        logger.warning(f"获取User-Agent失败: {e}")
                        user_agent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********'

                    # 构建headers - 确保包含所有原始代码中的关键字段
                    headers = {
                        'cookie': cookies_str,
                        'user-agent': user_agent,
                        'content-type': 'application/json',
                        'origin': 'https://hk.trip.com',
                        'referer': target_url,
                        'accept': '*/*',
                        'accept-language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7,en-GB;q=0.6',
                        'sec-ch-ua': '"Chromium";v="125", "Microsoft Edge";v="125", "Not.A/Brand";v="24"',
                        'sec-ch-ua-mobile': '?0',
                        'sec-ch-ua-platform': '"Windows"',
                        'sec-fetch-dest': 'empty',
                        'sec-fetch-mode': 'cors',
                        'sec-fetch-site': 'same-origin',
                        'x-ibu-flt-currency': 'CNY',
                        'x-ibu-flt-language': 'hk',
                        'x-ibu-flt-locale': 'zh-HK'
                    }

                    logger.info("添加了所有必要的请求头字段，包括sec-前缀字段")

                    # 保存到文件
                    try:
                        with open(settings.TRIP_COOKIE_FILE, 'w') as f:
                            json.dump(headers, f, indent=2)
                        logger.info(f"保存headers到文件: {settings.TRIP_COOKIE_FILE}")
                    except Exception as e:
                        logger.error(f"保存到文件失败: {e}")

                    logger.info("异步直接获取成功")
                    return headers
                else:
                    logger.error("未能获取任何cookies")
                    return None
            finally:
                if page:
                    await page.close()
                if context:
                    await context.close()
                if browser:
                    await browser.close()
    except Exception as e:
        logger.error(f"异步直接获取失败: {e}", exc_info=True)
        return None

from app.core.config import settings
# Import Celery tasks moved into functions to avoid circular import

logger = logging.getLogger(__name__)
# --- Global State ---
TRIP_CURRENT_HEADERS: Optional[Dict[str, str]] = None
TRIP_LAST_FETCH_TIME: float = 0
TRIP_FETCH_LOCK = asyncio.Lock()

# Ensure the cache directory exists
Path(settings.TRIP_COOKIE_FILE).parent.mkdir(parents=True, exist_ok=True)

# Removed _fetch_new_trip_playwright_session_data as its logic is now in tasks.py

async def get_effective_trip_headers(force_refresh: bool = False) -> Dict[str, str]:
    """
    Gets effective Trip.com headers. Handles caching (memory-first, then file-first)
    and triggers background refresh via Celery only if both caches are invalid.

    Args:
        force_refresh: If True, bypasses cache checks and forces a background refresh task,
                       but still returns current cache if available.

    Returns:
        A dictionary containing Trip.com headers (potentially stale if refresh is in progress).

    Raises:
        HTTPException:
            - 503 Service Unavailable: If no headers are available (neither memory nor file cache is valid)
              and a background refresh has been triggered. Client should retry.
            - 500 Internal Server Error: For unexpected errors like failing to enqueue the task when needed.
    """
    global TRIP_CURRENT_HEADERS, TRIP_LAST_FETCH_TIME

    async with TRIP_FETCH_LOCK:
        now = time.time()
        headers_to_return: Optional[Dict[str, str]] = None
        memory_cache_valid = False
        file_cache_valid = False
        file_mod_time = 0
        expiry_seconds = settings.TRIP_COOKIE_EXPIRY_SECONDS
        cache_file_path = settings.TRIP_COOKIE_FILE

        # 1. Check in-memory cache (unless force_refresh)
        memory_expired = (now - TRIP_LAST_FETCH_TIME) > expiry_seconds
        if not force_refresh and TRIP_CURRENT_HEADERS and not memory_expired:
            memory_cache_valid = True
            headers_to_return = TRIP_CURRENT_HEADERS.copy()
            logger.info("Using valid in-memory Trip.com headers.")

        # 2. Check file cache if memory wasn't valid (unless force_refresh)
        if not headers_to_return and not force_refresh and os.path.exists(cache_file_path):
            try:
                file_mod_time = os.path.getmtime(cache_file_path)
                file_expired = (now - file_mod_time) > expiry_seconds
                if not file_expired:
                    with open(cache_file_path, 'r') as f:
                        cached_headers = json.load(f)
                        if isinstance(cached_headers, dict) and 'cookie' in cached_headers and 'user-agent' in cached_headers:
                            file_cache_valid = True
                            headers_to_return = cached_headers.copy() # Prepare to return file cache
                            logger.info(f"Found valid Trip.com headers in file cache: {cache_file_path}")
                        else:
                            logger.warning(f"Invalid format in Trip.com cookie cache file: {cache_file_path}.")
                else:
                    logger.info(f"Trip.com cookie cache file expired: {cache_file_path}")
            except Exception as e:
                logger.warning(f"Error reading Trip.com cookie cache file {cache_file_path}: {e}.")

        # 3. Determine if a background refresh task is needed
        # Needed if forced OR if neither memory nor file cache was valid.
        needs_background_refresh = force_refresh or not (memory_cache_valid or file_cache_valid)
        task_triggered = False

        # 4. Trigger task if needed
        if needs_background_refresh:
            logger.info(f"Triggering background fetch for Trip.com headers (Force: {force_refresh}, No Valid Cache: {not (memory_cache_valid or file_cache_valid)}).")
            task_triggered = False

            # First try the sync version (Windows compatibility)
            try:
                # Use the synchronous version of the task for Windows compatibility
                from app.core.sync_playwright_tasks import fetch_trip_session_task_sync # Delayed import
                fetch_trip_session_task_sync.delay()
                task_triggered = True
                logger.info("Successfully enqueued fetch_trip_session_task_sync (Windows-compatible version).")
            except Exception as e_sync:
                logger.warning(f"Failed to enqueue fetch_trip_session_task_sync: {e_sync}. Trying async version...")

                # Try the async version as fallback
                try:
                    from app.core.tasks import fetch_trip_session_task # Delayed import
                    fetch_trip_session_task.delay()
                    task_triggered = True
                    logger.info("Successfully enqueued fetch_trip_session_task (async version).")
                except Exception as e_async:
                    logger.error(f"Failed to enqueue both sync and async fetch_trip_session_task: {e_async}", exc_info=True)

                    # If both enqueue attempts fail AND we have no headers to return, try direct fetch
                    if not headers_to_return:
                        logger.warning("No valid headers and task enqueue failed. Attempting direct synchronous fetch...")
                        try:
                            # 使用我们新创建的异步函数
                            logger.info("尝试使用异步直接获取函数...")
                            direct_headers = await _direct_fetch_trip_headers()
                            if direct_headers and 'cookie' in direct_headers and 'user-agent' in direct_headers:
                                logger.info("异步直接获取成功。使用这些headers。")
                                headers_to_return = direct_headers.copy()
                                TRIP_CURRENT_HEADERS = direct_headers.copy()
                                TRIP_LAST_FETCH_TIME = time.time()
                                task_triggered = True  # 防止503错误
                            else:
                                logger.error("Direct synchronous fetch failed to get valid headers.")
                                raise HTTPException(
                                    status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                                    detail="Trip.com session data is unavailable. Direct fetch failed."
                                )
                        except Exception as e_direct:
                            logger.error(f"Direct synchronous fetch failed: {e_direct}", exc_info=True)
                            raise HTTPException(
                                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                                detail="Trip.com session data is unavailable. All fetch methods failed."
                            )

        # 5. Decide what to return or raise
        if headers_to_return:
            # If we loaded from file, update memory cache now
            if file_cache_valid and not memory_cache_valid:
                TRIP_CURRENT_HEADERS = headers_to_return.copy() # Use the copy we made
                TRIP_LAST_FETCH_TIME = file_mod_time
                logger.info("Updated in-memory Trip.com cache from file.")

            if task_triggered: # Log if we are returning stale data while refresh runs
                 logger.warning("Returning potentially stale Trip.com headers while background refresh is running.")
            return headers_to_return
        else:
            # No valid cache found (memory or file).
            # Background task should have been triggered (or failed to trigger).
            logger.error("No valid Trip.com headers available. Background refresh initiated (or failed).")

            # Last resort - try direct fetch inline (using async API)
            try:
                logger.warning("Attempting last-resort inline fetch of Trip.com headers (using platform-aware method)...")

                # 使用平台感知的方法选择合适的获取方式
                if platform.system() == "Windows":
                    logger.info("检测到Windows系统，使用纯HTTP方法获取会话数据")
                    # 首先尝试纯HTTP方法，不依赖于Playwright
                    try:
                        from app.core.sync_playwright_tasks import fetch_trip_cookies_http
                        with concurrent.futures.ThreadPoolExecutor() as executor:
                            headers = await asyncio.get_event_loop().run_in_executor(
                                executor,
                                fetch_trip_cookies_http
                            )

                            if headers and isinstance(headers, dict) and 'cookie' in headers and 'user-agent' in headers:
                                logger.info("Windows环境下使用纯HTTP方法获取成功")
                                # 更新内存缓存
                                TRIP_CURRENT_HEADERS = headers.copy()
                                TRIP_LAST_FETCH_TIME = time.time()

                                # 保存到文件
                                try:
                                    with open(settings.TRIP_COOKIE_FILE, 'w') as f:
                                        json.dump(headers, f, indent=2)
                                    logger.info(f"纯HTTP方法获取结果保存到文件: {settings.TRIP_COOKIE_FILE}")
                                except Exception as e:
                                    logger.error(f"保存到文件失败: {e}")

                                return headers
                            else:
                                logger.error("纯HTTP方法未返回有效headers")
                                raise Exception("纯HTTP方法获取未返回有效数据")
                    except Exception as e:
                        logger.error(f"纯HTTP方法执行失败: {str(e)}")
                        logger.exception(e)
                        # 失败后尝试旧方法作为后备
                        logger.warning("纯HTTP方法失败，尝试其他方法作为后备...")

                        # 尝试使用线程池执行同步Playwright操作（旧方法，可能在Python 3.12上失败）
                        try:
                            from app.core.sync_playwright_tasks import fetch_trip_cookies_direct
                            logger.info("尝试使用线程池执行同步Playwright API（旧方法）")
                            with concurrent.futures.ThreadPoolExecutor() as executor:
                                headers = await asyncio.get_event_loop().run_in_executor(
                                    executor,
                                    fetch_trip_cookies_direct
                                )

                                if headers and isinstance(headers, dict) and 'cookie' in headers and 'user-agent' in headers:
                                    logger.info("Windows环境下使用线程池同步获取成功")
                                    # 更新内存缓存
                                    TRIP_CURRENT_HEADERS = headers.copy()
                                    TRIP_LAST_FETCH_TIME = time.time()

                                    # 保存到文件
                                    try:
                                        with open(settings.TRIP_COOKIE_FILE, 'w') as f:
                                            json.dump(headers, f, indent=2)
                                        logger.info(f"线程池获取结果保存到文件: {settings.TRIP_COOKIE_FILE}")
                                    except Exception as e:
                                        logger.error(f"保存到文件失败: {e}")

                                    return headers
                                else:
                                    logger.error("线程池同步获取未返回有效headers")
                                    raise Exception("同步获取未返回有效数据")
                        except Exception as e:
                            logger.error(f"线程池执行失败: {str(e)}")
                            logger.exception(e)
                            # 失败后继续尝试异步方法
                            logger.warning("Windows线程池获取失败，尝试异步API作为后备...")

                # Linux环境或Windows线程池失败后，尝试异步API
                logger.info("使用异步API获取Trip.com会话数据")
                async with async_playwright() as p:
                    logger.info("启动浏览器获取Trip.com cookies...")
                    browser = await p.chromium.launch(headless=True)
                    context = await browser.new_context(
                        user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
                        locale="zh-HK",
                        timezone_id="Asia/Hong_Kong",
                    )
                    page = await context.new_page()

                    try:
                        target_url = "https://hk.trip.com/flights/"
                        logger.info(f"正在导航到: {target_url}")
                        await page.goto(target_url, timeout=30000, wait_until="domcontentloaded")
                        await page.wait_for_timeout(5000)

                        # 尝试点击cookie按钮
                        for selector in ['#ibu_cookie_banner_agree', 'button[class*="cookie_accept"]']:
                            try:
                                element = await page.query_selector(selector)
                                if element and await element.is_visible(timeout=2000):
                                    logger.info(f"点击cookie接受按钮 ({selector})")
                                    await element.click(timeout=3000)
                                    await page.wait_for_timeout(1000)
                                    break
                            except Exception:
                                pass

                        # 获取cookies
                        cookies_list = await context.cookies(urls=[target_url])
                        if cookies_list:
                            cookies_str = "; ".join([f"{cookie['name']}={cookie['value']}" for cookie in cookies_list])
                            user_agent = await page.evaluate("() => navigator.userAgent")

                            direct_headers = {
                                'cookie': cookies_str,
                                'user-agent': user_agent,
                                'content-type': 'application/json',
                                'origin': 'https://hk.trip.com',
                                'referer': target_url,
                                'accept': '*/*',
                                'accept-language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7,en-GB;q=0.6',
                                'sec-ch-ua': '"Chromium";v="125", "Microsoft Edge";v="125", "Not.A/Brand";v="24"',
                                'sec-ch-ua-mobile': '?0',
                                'sec-ch-ua-platform': '"Windows"',
                                'sec-fetch-dest': 'empty',
                                'sec-fetch-mode': 'cors',
                                'sec-fetch-site': 'same-origin',
                                'x-ibu-flt-currency': 'CNY',
                                'x-ibu-flt-language': 'hk',
                                'x-ibu-flt-locale': 'zh-HK'
                            }

                            logger.info("内联获取过程添加了所有必要的请求头字段")

                            # 保存到文件缓存
                            try:
                                with open(settings.TRIP_COOKIE_FILE, 'w') as f:
                                    json.dump(direct_headers, f, indent=2)
                                logger.info(f"内联获取成功并保存到缓存文件: {settings.TRIP_COOKIE_FILE}")
                            except Exception as e:
                                logger.error(f"保存到缓存文件失败: {e}")

                            # 更新内存缓存
                            TRIP_CURRENT_HEADERS = direct_headers.copy()
                            TRIP_LAST_FETCH_TIME = time.time()
                            return direct_headers
                        else:
                            logger.error("内联获取过程中无法提取cookies")
                    finally:
                        # 关闭资源
                        if 'page' in locals() and page:
                            await page.close()
                        if 'context' in locals() and context:
                            await context.close()
                        if 'browser' in locals() and browser:
                            await browser.close()

                # 如果执行到这里，说明上面的异步代码没有成功返回
                logger.error("内联获取失败，未能获取有效headers")
                raise HTTPException(
                    status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                    detail="Trip.com session data is unavailable or being refreshed. Please try again shortly."
                )
            except Exception as e_last:
                logger.error(f"内联获取失败，异常: {e_last}", exc_info=True)
                raise HTTPException(
                    status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                    detail="Trip.com session data is unavailable or being refreshed. Please try again shortly."
                )

async def load_initial_trip_cookies():
    """
    Attempts to load Trip.com cookies from the cache file into memory on application startup.
    Does not trigger a fetch if the file is invalid or expired.
    """
    global TRIP_CURRENT_HEADERS, TRIP_LAST_FETCH_TIME
    logger.info(f"Attempting to load initial Trip.com cookies from {settings.TRIP_COOKIE_FILE}...")
    if os.path.exists(settings.TRIP_COOKIE_FILE):
        try:
            now = time.time()
            file_mod_time = os.path.getmtime(settings.TRIP_COOKIE_FILE)
            if (now - file_mod_time) <= settings.TRIP_COOKIE_EXPIRY_SECONDS:
                with open(settings.TRIP_COOKIE_FILE, 'r') as f:
                    cached_headers = json.load(f)
                    if isinstance(cached_headers, dict) and 'cookie' in cached_headers and 'user-agent' in cached_headers:
                        # Only load if memory is empty or file is newer
                        if not TRIP_CURRENT_HEADERS or file_mod_time > TRIP_LAST_FETCH_TIME:
                            TRIP_CURRENT_HEADERS = cached_headers
                            TRIP_LAST_FETCH_TIME = file_mod_time
                            logger.info("Successfully loaded initial Trip.com headers from valid cache file into memory.")
                        else:
                            logger.info("In-memory Trip.com headers are already present and up-to-date.")
                    else:
                        logger.warning("Initial Trip.com cookie cache file has invalid format. Skipping load.")
            else:
                logger.info("Initial Trip.com cookie cache file found but expired. Skipping load.")
        except Exception as e:
            logger.warning(f"Error loading initial Trip.com cookie cache file: {e}. Skipping load.")
    else:
        logger.info("No initial Trip.com cookie cache file found. Headers will be fetched by the first request if needed.")

# --- Application Startup Integration ---
# Ensure load_initial_trip_cookies and load_initial_kiwi_token are called
# during application startup (e.g., in main.py lifespan).
# Example (in main.py):
# from contextlib import asynccontextmanager
# from app.core.dynamic_fetcher import load_initial_trip_cookies, load_initial_kiwi_token
#
# @asynccontextmanager
# async def lifespan(app: FastAPI):
#     # Connect to DB, etc.
#     await load_initial_trip_cookies()
#     await load_initial_kiwi_token()
#     yield
#     # Disconnect DB, etc.
#
# app = FastAPI(lifespan=lifespan)
# --- Global State for Kiwi.com ---
KIWI_CURRENT_HEADERS: Optional[Dict[str, str]] = None
KIWI_LAST_FETCH_TIME: float = 0
KIWI_FETCH_LOCK = asyncio.Lock()

# Ensure the Kiwi cache directory exists (assuming settings.KIWI_TOKEN_FILE is defined)
# This should ideally be done once, maybe near the Trip.com check or in startup
try:
    Path(settings.KIWI_TOKEN_FILE).parent.mkdir(parents=True, exist_ok=True)
except AttributeError:
    logger.error("settings.KIWI_TOKEN_FILE is not defined in config.py!")
except Exception as e:
    logger.error(f"Error creating directory for KIWI_TOKEN_FILE: {e}")


# --- Kiwi.com Functions ---

# Removed _fetch_new_kiwi_playwright_session_data as its logic is now in tasks.py

async def get_effective_kiwi_headers(force_refresh: bool = False) -> Dict[str, str]:
    """
    获取有效的 Kiwi.com headers

    新的简化策略：
    1. 优先使用预获取的缓存（内存或文件）
    2. 只在缓存完全无效时才触发立即获取
    3. 依赖 token 调度器进行定期刷新，避免请求时延迟

    Args:
        force_refresh: 如果为 True，强制立即获取新 token

    Returns:
        包含 Kiwi.com headers 的字典

    Raises:
        HTTPException: 如果无法获取有效 headers
    """
    global KIWI_CURRENT_HEADERS, KIWI_LAST_FETCH_TIME

    # 检查配置
    try:
        expiry_seconds = settings.KIWI_COOKIE_EXPIRY_SECONDS
        cache_file_path = settings.KIWI_TOKEN_FILE
    except AttributeError as e:
        logger.critical(f"Missing required Kiwi settings: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Server configuration error: {e}"
        )

    async with KIWI_FETCH_LOCK:
        now = time.time()

        # 1. 检查内存缓存（最快）
        memory_expired = (now - KIWI_LAST_FETCH_TIME) > expiry_seconds
        if not force_refresh and KIWI_CURRENT_HEADERS and not memory_expired:
            logger.info("使用有效的内存 Kiwi headers")
            return KIWI_CURRENT_HEADERS.copy()

        # 2. 检查文件缓存
        if not force_refresh and os.path.exists(cache_file_path):
            try:
                file_mod_time = os.path.getmtime(cache_file_path)
                file_expired = (now - file_mod_time) > expiry_seconds
                if not file_expired:
                    with open(cache_file_path, 'r') as f:
                        cached_headers = json.load(f)
                        if isinstance(cached_headers, dict) and 'kw-umbrella-token' in cached_headers:
                            # 更新内存缓存
                            KIWI_CURRENT_HEADERS = cached_headers.copy()
                            KIWI_LAST_FETCH_TIME = file_mod_time
                            logger.info("从文件缓存加载有效的 Kiwi headers")
                            return cached_headers.copy()
            except Exception as e:
                logger.warning(f"读取 Kiwi 缓存文件失败: {e}")

        # 3. 缓存无效或强制刷新，立即获取
        logger.warning("Kiwi 缓存无效或被强制刷新，开始立即获取...")
        try:
            fresh_headers = await _get_fresh_kiwi_headers_sync()
            if fresh_headers and 'kw-umbrella-token' in fresh_headers:
                # 更新内存缓存
                KIWI_CURRENT_HEADERS = fresh_headers.copy()
                KIWI_LAST_FETCH_TIME = time.time()

                # 保存到文件缓存
                try:
                    with open(cache_file_path, 'w') as f:
                        json.dump(fresh_headers, f, indent=2)
                    logger.info("立即获取 Kiwi headers 成功，已更新缓存")
                except Exception as e:
                    logger.warning(f"保存 Kiwi 缓存失败: {e}")

                return fresh_headers
            else:
                logger.error("立即获取 Kiwi headers 失败")
                raise HTTPException(
                    status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                    detail="Unable to obtain fresh Kiwi.com session data"
                )
        except Exception as e:
            logger.error(f"立即获取 Kiwi headers 异常: {e}")
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Kiwi.com session data temporarily unavailable"
            )

async def load_initial_kiwi_token():
    """
    Attempts to load Kiwi.com token/headers from the cache file into memory on application startup.
    Does not trigger a fetch if the file is invalid or expired.
    """
    global KIWI_CURRENT_HEADERS, KIWI_LAST_FETCH_TIME
    logger.info(f"Attempting to load initial Kiwi.com token from settings.KIWI_TOKEN_FILE...")

    try:
        token_file = settings.KIWI_TOKEN_FILE
        expiry_seconds = settings.KIWI_COOKIE_EXPIRY_SECONDS
    except AttributeError as e:
        logger.error(f"Missing required Kiwi settings for initial load: {e}. Cannot load initial token.")
        return # Cannot proceed without settings

    if os.path.exists(token_file):
        try:
            now = time.time()
            file_mod_time = os.path.getmtime(token_file)
            if (now - file_mod_time) <= expiry_seconds:
                with open(token_file, 'r') as f:
                    cached_headers = json.load(f)
                    if isinstance(cached_headers, dict) and 'kw-umbrella-token' in cached_headers:
                         # Only load if memory is empty or file is newer
                        if not KIWI_CURRENT_HEADERS or file_mod_time > KIWI_LAST_FETCH_TIME:
                            KIWI_CURRENT_HEADERS = cached_headers
                            KIWI_LAST_FETCH_TIME = file_mod_time
                            logger.info("Successfully loaded initial Kiwi.com headers from valid cache file into memory.")
                        else:
                            logger.info("In-memory Kiwi.com headers are already present and up-to-date.")
                    else:
                        logger.warning("Initial Kiwi.com token cache file has invalid format. Skipping load.")
            else:
                logger.info("Initial Kiwi.com token cache file found but expired. Skipping load.")
        except Exception as e:
            logger.warning(f"Error loading initial Kiwi.com token cache file: {e}. Skipping load.")
    else:
        logger.info("No initial Kiwi.com token cache file found. Headers will be fetched by the first request if needed.")

# --- 同步 Kiwi Token 获取（基于工作代码的降级方案）---

async def _get_fresh_kiwi_headers_sync() -> Optional[Dict[str, str]]:
    """
    获取新鲜的 Kiwi headers，使用固定token策略
    """
    import asyncio
    import concurrent.futures

    logger.info("开始获取 Kiwi headers（固定token策略）...")

    # 使用固定token策略
    logger.info("使用固定token策略...")
    try:
        from app.core.sync_playwright_tasks import get_fallback_kiwi_headers, validate_kiwi_token

        fallback_headers = get_fallback_kiwi_headers()

        with concurrent.futures.ThreadPoolExecutor() as executor:
            is_valid = await asyncio.get_event_loop().run_in_executor(
                executor, validate_kiwi_token, fallback_headers
            )

        if is_valid:
            logger.info("固定token验证有效")
            return fallback_headers
        else:
            logger.warning("固定token验证无效")
    except Exception as e:
        logger.warning(f"固定token验证失败: {e}")

    # 如果固定token验证失败，返回None
    logger.error("固定token验证失败，无法获取有效的Kiwi headers")
    return None