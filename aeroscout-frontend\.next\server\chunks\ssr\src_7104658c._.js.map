{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/AeroScout/aeroscout-frontend/src/hooks/useDebounce.ts"], "sourcesContent": ["import { useState, useEffect } from 'react';\r\n\r\n/**\r\n * Custom hook to debounce a value.\r\n * @param value The value to debounce.\r\n * @param delay The debounce delay in milliseconds.\r\n * @returns The debounced value.\r\n */\r\nfunction useDebounce<T>(value: T, delay: number): T {\r\n  // State and setters for debounced value\r\n  const [debouncedValue, setDebouncedValue] = useState<T>(value);\r\n\r\n  useEffect(\r\n    () => {\r\n      // Update debounced value after delay\r\n      const handler = setTimeout(() => {\r\n        setDebouncedValue(value);\r\n      }, delay);\r\n\r\n      // Cancel the timeout if value changes (also on delay change or unmount)\r\n      // This is how we prevent debounced value from updating if value is changed ...\r\n      // .. within the delay period. Timeout gets cleared and restarted.\r\n      return () => {\r\n        clearTimeout(handler);\r\n      };\r\n    },\r\n    [value, delay] // Only re-call effect if value or delay changes\r\n  );\r\n\r\n  return debouncedValue;\r\n}\r\n\r\nexport default useDebounce;"], "names": [], "mappings": ";;;AAAA;;AAEA;;;;;CAKC,GACD,SAAS,YAAe,KAAQ,EAAE,KAAa;IAC7C,wCAAwC;IACxC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAK;IAExD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EACN;QACE,qCAAqC;QACrC,MAAM,UAAU,WAAW;YACzB,kBAAkB;QACpB,GAAG;QAEH,wEAAwE;QACxE,+EAA+E;QAC/E,kEAAkE;QAClE,OAAO;YACL,aAAa;QACf;IACF,GACA;QAAC;QAAO;KAAM,CAAC,gDAAgD;;IAGjE,OAAO;AACT;uCAEe", "debugId": null}}, {"offset": {"line": 45, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/AeroScout/aeroscout-frontend/src/components/airport/AirportSelector.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect, useRef } from 'react';\nimport { searchAirports, AirportInfo } from '@/lib/apiService';\nimport useDebounce from '@/hooks/useDebounce';\n// import { useAlertStore } from '@/store/alertStore'; // showAlert 未使用，移除\n\ninterface AirportSelectorProps {\n  label: string;\n  placeholder?: string;\n  onAirportSelected: (airport: AirportInfo | null) => void;\n  value?: AirportInfo | null;\n  disabled?: boolean;\n  error?: string; // 添加错误消息属性\n}\n\n/**\n * 简化版AirportSelector组件\n * 使用单一数据源和清晰的状态流，避免竞态条件\n */\nconst AirportSelector: React.FC<AirportSelectorProps> = ({\n  label,\n  placeholder = \"搜索机场或城市...\",\n  onAirportSelected,\n  value,\n  disabled = false,\n  error\n}) => {\n  // 基本状态\n  const [inputValue, setInputValue] = useState('');\n  const [searchResults, setSearchResults] = useState<AirportInfo[]>([]);\n  const [isLoading, setIsLoading] = useState(false);\n  const [isDropdownOpen, setIsDropdownOpen] = useState(false);\n  const [highlightedIndex, setHighlightedIndex] = useState(-1);\n\n  // 引用\n  const inputRef = useRef<HTMLInputElement>(null);\n  const selectingRef = useRef(false); // 跟踪是否正在进行选择操作\n\n  // 格式化机场信息为显示文本\n  const formatAirportText = (airport: AirportInfo | null | undefined): string => {\n    if (!airport || !airport.name || !airport.code) return '';\n    return `${airport.name} (${airport.code})`;\n  };\n\n  // 当value属性变化时，更新输入框显示\n  useEffect(() => {\n    const currentTime = Date.now();\n    const newText = formatAirportText(value);\n    console.log(`[AirportSelector] useEffect[value] triggered at ${currentTime}:`, {\n      value,\n      currentInputValue: inputValue,\n      selectingRefCurrent: selectingRef.current,\n      newText\n    });\n\n    // 如果外部value变更且不是在选择中，更新输入值\n    // 只有当新文本与当前输入值不同，且当前输入值等于之前value的格式化文本时才更新\n    // 这避免了在用户正在输入时被干扰\n    if (!selectingRef.current) {\n      const shouldUpdate = newText !== inputValue &&\n        (inputValue === '' || inputValue === formatAirportText(null));\n\n      if (shouldUpdate) {\n        console.log(`[AirportSelector] useEffect[value] at ${currentTime}: updating inputValue from \"${inputValue}\" to \"${newText}\"`);\n        setInputValue(newText);\n      } else {\n        console.log(`[AirportSelector] useEffect[value] at ${currentTime}: skipping update - shouldUpdate: ${shouldUpdate}`);\n      }\n    } else {\n      console.log(`[AirportSelector] useEffect[value] at ${currentTime}: skipping update - selectingRef is true`);\n    }\n  }, [value]); // 只依赖value，不依赖inputValue\n\n  // 处理搜索查询\n  const debouncedQuery = useDebounce(inputValue, 300);\n\n  useEffect(() => {\n    // 只有当输入不是当前选中值的显示文本时才搜索\n    // 同时确保不在选择过程中触发搜索\n    if (debouncedQuery && debouncedQuery.length >= 2 &&\n        debouncedQuery !== formatAirportText(value) &&\n        !selectingRef.current) { // 添加selectingRef检查\n\n      console.log(`[AirportSelector] Search triggered for query: \"${debouncedQuery}\", selectingRef: ${selectingRef.current}`);\n\n      const fetchAirports = async () => {\n        setIsLoading(true);\n        try {\n          const response = await searchAirports({\n            query: debouncedQuery,\n            trip_type: 'flight',\n            mode: 'dep'\n          });\n          setSearchResults(response.airports);\n          setIsDropdownOpen(response.airports.length > 0);\n        } catch (error) {\n          console.error('搜索机场失败:', error);\n          setSearchResults([]);\n          setIsDropdownOpen(false);\n        } finally {\n          setIsLoading(false);\n        }\n      };\n\n      fetchAirports();\n    } else if (debouncedQuery.length < 2) {\n      setSearchResults([]);\n      setIsDropdownOpen(false);\n    } else {\n      console.log(`[AirportSelector] Search skipped for query: \"${debouncedQuery}\", reasons: length=${debouncedQuery.length}, matchesValue=${debouncedQuery === formatAirportText(value)}, selectingRef=${selectingRef.current}`);\n    }\n  }, [debouncedQuery, value]);\n\n  // 处理输入变化\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const newValue = e.target.value;\n    setInputValue(newValue);\n\n    // 如果清空输入，则清空选择\n    if (newValue === '' && value) {\n      onAirportSelected(null);\n    }\n\n    // 如果输入的不是当前选中的值，打开下拉列表\n    if (newValue !== formatAirportText(value)) {\n      setIsDropdownOpen(newValue.length >= 2);\n    }\n  };\n\n  // 选择机场\n  const handleSelectAirport = (airport: AirportInfo) => {\n    console.log(`[AirportSelector] handleSelectAirport called at ${Date.now()}: selecting airport`, airport);\n    selectingRef.current = true;\n\n    // 更新UI\n    const formattedText = formatAirportText(airport);\n    setInputValue(formattedText);\n    setIsDropdownOpen(false);\n    setSearchResults([]);\n\n    console.log(`[AirportSelector] handleSelectAirport: set inputValue to \"${formattedText}\"`);\n\n    // 通知父组件\n    onAirportSelected(airport);\n\n    // 重置选择状态 - 延长时间以确保状态更新链完成\n    setTimeout(() => {\n      console.log(`[AirportSelector] selectingRef reset at ${Date.now()}: resetting selectingRef to false`);\n      selectingRef.current = false;\n    }, 300); // 增加到300ms以提供足够的状态更新时间\n\n    // 移除焦点\n    inputRef.current?.blur();\n  };\n\n  // 处理输入框失去焦点\n  const handleBlur = () => {\n    console.log(`[AirportSelector] handleBlur called at ${Date.now()}: current selectingRef: ${selectingRef.current}`);\n\n    // 延迟处理失焦，以便可以先处理选择事件\n    // 延迟时间与handleSelectAirport保持一致\n    setTimeout(() => {\n      if (!selectingRef.current) {\n        console.log(`[AirportSelector] handleBlur timeout executed at ${Date.now()}: closing dropdown`);\n        setIsDropdownOpen(false);\n\n        // 简化逻辑：如果当前输入不匹配任何选择的值且不为空，则重置\n        const currentValueText = formatAirportText(value);\n        if (inputValue !== currentValueText && inputValue.trim() !== '') {\n          console.log(`[AirportSelector] handleBlur: resetting inputValue from \"${inputValue}\" to \"${currentValueText}\"`);\n          setInputValue(currentValueText);\n        }\n      } else {\n        console.log(`[AirportSelector] handleBlur timeout skipped at ${Date.now()}: selectingRef is still true`);\n      }\n    }, 300); // 与handleSelectAirport的延迟保持一致\n  };\n\n  // 清除选择\n  const handleClear = (e: React.MouseEvent) => {\n    e.stopPropagation(); // 阻止冒泡，避免触发其他事件\n    setInputValue('');\n    setSearchResults([]);\n    setIsDropdownOpen(false);\n    onAirportSelected(null);\n    inputRef.current?.focus();\n  };\n\n  // 处理键盘导航\n  const handleKeyDown = (e: React.KeyboardEvent) => {\n    if (!isDropdownOpen || searchResults.length === 0) return;\n\n    switch (e.key) {\n      case 'ArrowDown':\n        e.preventDefault();\n        setHighlightedIndex(prev =>\n          prev < searchResults.length - 1 ? prev + 1 : 0\n        );\n        break;\n      case 'ArrowUp':\n        e.preventDefault();\n        setHighlightedIndex(prev =>\n          prev > 0 ? prev - 1 : searchResults.length - 1\n        );\n        break;\n      case 'Enter':\n        e.preventDefault();\n        if (highlightedIndex >= 0 && highlightedIndex < searchResults.length) {\n          handleSelectAirport(searchResults[highlightedIndex]);\n        }\n        break;\n      case 'Escape':\n        e.preventDefault();\n        setIsDropdownOpen(false);\n        inputRef.current?.blur();\n        break;\n    }\n  };\n\n  return (\n    <div className=\"relative w-full\">\n      <label className=\"block text-[15px] font-semibold text-[#1D1D1F] mb-3 flex items-center\">\n        <svg className=\"w-4 h-4 mr-2 text-[#0071E3]\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 19l9 2-9-18-9 18 9-2zm0 0v-8\" />\n        </svg>\n        {label}\n      </label>\n\n      <div className=\"relative\">\n        <input\n          ref={inputRef}\n          type=\"text\"\n          value={inputValue}\n          onChange={handleInputChange}\n          onFocus={() => setIsDropdownOpen(inputValue.length >= 2)}\n          onBlur={handleBlur}\n          onKeyDown={handleKeyDown}\n          placeholder={placeholder}\n          disabled={disabled}\n          className={`\n            w-full px-4 py-3.5 pr-16 text-[#1D1D1F] bg-white border border-[#D2D2D7]\n            rounded-apple-lg focus:outline-none focus:ring-2 focus:ring-[#0071E3]/20 focus:border-[#0071E3]\n            transition-all duration-200 ease-out placeholder-[#86868B] text-[15px] font-medium\n            ${disabled ? 'bg-[#F5F5F7] cursor-not-allowed' : 'hover:border-[#AEAEB2] hover:shadow-apple-sm'}\n            ${error ? 'border-[#FF3B30] focus:border-[#FF3B30] focus:ring-[#FF3B30]/20' : ''}\n            ${isDropdownOpen ? 'border-[#0071E3] ring-2 ring-[#0071E3]/20 shadow-apple-sm' : ''}\n          `}\n          autoComplete=\"off\"\n          role=\"combobox\"\n          aria-expanded={isDropdownOpen}\n          aria-haspopup=\"listbox\"\n          aria-autocomplete=\"list\"\n        />\n\n        {/* 右侧图标 */}\n        <div className=\"absolute right-3 top-1/2 transform -translate-y-1/2 flex items-center space-x-2\">\n          {isLoading ? (\n            <div className=\"w-5 h-5 border-2 border-[#0071E3] border-t-transparent rounded-full animate-spin\"></div>\n          ) : value ? (\n            <button\n              type=\"button\"\n              onClick={handleClear}\n              className=\"w-5 h-5 rounded-full bg-[#86868B] hover:bg-[#FF3B30] flex items-center justify-center transition-all duration-200 group\"\n              aria-label=\"清除选择\"\n            >\n              <svg className=\"w-3 h-3 text-white\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2.5} d=\"M6 18L18 6M6 6l12 12\" />\n              </svg>\n            </button>\n          ) : null}\n\n          <svg className={`h-4 w-4 transition-colors duration-200 ${isDropdownOpen ? 'text-[#0071E3]' : 'text-[#86868B]'}`} xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\" />\n          </svg>\n        </div>\n      </div>\n\n      {/* 下拉菜单 */}\n      {isDropdownOpen && (\n        <div className=\"absolute z-[99999] w-full mt-1 bg-white border border-[#D2D2D7] rounded-apple shadow-apple-lg max-h-64 overflow-hidden animate-slideDown\" style={{ zIndex: 99999 }}>\n          {/* 搜索状态指示器 */}\n          {isLoading && (\n            <div className=\"px-4 py-3 border-b border-[#F5F5F7]\">\n              <div className=\"flex items-center justify-center space-x-2\">\n                <div className=\"w-4 h-4 border-2 border-[#0071E3] border-t-transparent rounded-full animate-spin\"></div>\n                <span className=\"text-sm text-[#86868B]\">搜索中...</span>\n              </div>\n            </div>\n          )}\n\n          {/* 搜索结果 */}\n          <div className=\"max-h-72 overflow-y-auto\">\n            {searchResults.length === 0 && !isLoading ? (\n              <div className=\"px-4 py-8 text-center\">\n                <div className=\"w-12 h-12 mx-auto mb-3 bg-[#F5F5F7] rounded-full flex items-center justify-center\">\n                  <svg className=\"w-6 h-6 text-[#86868B]\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1.5} d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\" />\n                  </svg>\n                </div>\n                <p className=\"text-sm text-[#86868B] font-medium\">未找到相关机场</p>\n                <p className=\"text-xs text-[#86868B] mt-1\">请尝试输入城市名称或机场代码</p>\n              </div>\n            ) : (\n              searchResults.map((airport, index) => (\n                <div\n                  key={airport.code}\n                  onClick={() => handleSelectAirport(airport)}\n                  className={`\n                    group px-4 py-3 cursor-pointer transition-all duration-150 ease-out border-b border-[#F5F5F7] last:border-b-0\n                    ${index === highlightedIndex\n                      ? 'bg-[#0071E3] text-white'\n                      : 'hover:bg-[#F5F5F7]'\n                    }\n                  `}\n                  role=\"option\"\n                  aria-selected={index === highlightedIndex}\n                >\n                  <div className=\"flex items-center justify-between\">\n                    <div className=\"flex items-center space-x-3\">\n                      {/* 机场图标 */}\n                      <div className={`\n                        w-7 h-7 rounded-md flex items-center justify-center flex-shrink-0 transition-all duration-150\n                        ${index === highlightedIndex\n                          ? 'bg-white/20 text-white'\n                          : 'bg-[#0071E3] text-white'\n                        }\n                      `}>\n                        <svg className=\"w-3.5 h-3.5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 19l9 2-9-18-9 18 9-2zm0 0v-8\" />\n                        </svg>\n                      </div>\n\n                      {/* 机场信息 */}\n                      <div className=\"flex-1 min-w-0 mr-3\">\n                        <div className={`\n                          font-medium text-sm leading-tight transition-colors duration-150\n                          ${index === highlightedIndex ? 'text-white' : 'text-[#1D1D1F]'}\n                        `}>\n                          {airport.name}\n                        </div>\n                        <div className={`\n                          text-xs leading-tight transition-colors duration-150\n                          ${index === highlightedIndex ? 'text-white/80' : 'text-[#86868B]'}\n                        `}>\n                          {airport.city}, {airport.country}\n                        </div>\n                      </div>\n                    </div>\n\n                    {/* 机场代码 */}\n                    <div className={`\n                      text-xs font-mono font-bold transition-colors duration-150 flex-shrink-0\n                      ${index === highlightedIndex ? 'text-white/90' : 'text-[#86868B]'}\n                    `}>\n                      {airport.code}\n                    </div>\n                  </div>\n                </div>\n              ))\n            )}\n          </div>\n        </div>\n      )}\n\n      {/* 错误提示 */}\n      {error && (\n        <p className=\"text-[#FF3B30] text-xs mt-1\">{error}</p>\n      )}\n    </div>\n  );\n};\n\nexport default AirportSelector;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAgBA;;;CAGC,GACD,MAAM,kBAAkD,CAAC,EACvD,KAAK,EACL,cAAc,YAAY,EAC1B,iBAAiB,EACjB,KAAK,EACL,WAAW,KAAK,EAChB,KAAK,EACN;IACC,OAAO;IACP,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IACpE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;IAE1D,KAAK;IACL,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAC1C,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE,QAAQ,eAAe;IAEnD,eAAe;IACf,MAAM,oBAAoB,CAAC;QACzB,IAAI,CAAC,WAAW,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,IAAI,EAAE,OAAO;QACvD,OAAO,GAAG,QAAQ,IAAI,CAAC,EAAE,EAAE,QAAQ,IAAI,CAAC,CAAC,CAAC;IAC5C;IAEA,sBAAsB;IACtB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,cAAc,KAAK,GAAG;QAC5B,MAAM,UAAU,kBAAkB;QAClC,QAAQ,GAAG,CAAC,CAAC,gDAAgD,EAAE,YAAY,CAAC,CAAC,EAAE;YAC7E;YACA,mBAAmB;YACnB,qBAAqB,aAAa,OAAO;YACzC;QACF;QAEA,2BAA2B;QAC3B,2CAA2C;QAC3C,kBAAkB;QAClB,IAAI,CAAC,aAAa,OAAO,EAAE;YACzB,MAAM,eAAe,YAAY,cAC/B,CAAC,eAAe,MAAM,eAAe,kBAAkB,KAAK;YAE9D,IAAI,cAAc;gBAChB,QAAQ,GAAG,CAAC,CAAC,sCAAsC,EAAE,YAAY,4BAA4B,EAAE,WAAW,MAAM,EAAE,QAAQ,CAAC,CAAC;gBAC5H,cAAc;YAChB,OAAO;gBACL,QAAQ,GAAG,CAAC,CAAC,sCAAsC,EAAE,YAAY,kCAAkC,EAAE,cAAc;YACrH;QACF,OAAO;YACL,QAAQ,GAAG,CAAC,CAAC,sCAAsC,EAAE,YAAY,wCAAwC,CAAC;QAC5G;IACF,GAAG;QAAC;KAAM,GAAG,yBAAyB;IAEtC,SAAS;IACT,MAAM,iBAAiB,CAAA,GAAA,2HAAA,CAAA,UAAW,AAAD,EAAE,YAAY;IAE/C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,wBAAwB;QACxB,kBAAkB;QAClB,IAAI,kBAAkB,eAAe,MAAM,IAAI,KAC3C,mBAAmB,kBAAkB,UACrC,CAAC,aAAa,OAAO,EAAE;YAEzB,QAAQ,GAAG,CAAC,CAAC,+CAA+C,EAAE,eAAe,iBAAiB,EAAE,aAAa,OAAO,EAAE;YAEtH,MAAM,gBAAgB;gBACpB,aAAa;gBACb,IAAI;oBACF,MAAM,WAAW,MAAM,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD,EAAE;wBACpC,OAAO;wBACP,WAAW;wBACX,MAAM;oBACR;oBACA,iBAAiB,SAAS,QAAQ;oBAClC,kBAAkB,SAAS,QAAQ,CAAC,MAAM,GAAG;gBAC/C,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,WAAW;oBACzB,iBAAiB,EAAE;oBACnB,kBAAkB;gBACpB,SAAU;oBACR,aAAa;gBACf;YACF;YAEA;QACF,OAAO,IAAI,eAAe,MAAM,GAAG,GAAG;YACpC,iBAAiB,EAAE;YACnB,kBAAkB;QACpB,OAAO;YACL,QAAQ,GAAG,CAAC,CAAC,6CAA6C,EAAE,eAAe,mBAAmB,EAAE,eAAe,MAAM,CAAC,eAAe,EAAE,mBAAmB,kBAAkB,OAAO,eAAe,EAAE,aAAa,OAAO,EAAE;QAC5N;IACF,GAAG;QAAC;QAAgB;KAAM;IAE1B,SAAS;IACT,MAAM,oBAAoB,CAAC;QACzB,MAAM,WAAW,EAAE,MAAM,CAAC,KAAK;QAC/B,cAAc;QAEd,eAAe;QACf,IAAI,aAAa,MAAM,OAAO;YAC5B,kBAAkB;QACpB;QAEA,uBAAuB;QACvB,IAAI,aAAa,kBAAkB,QAAQ;YACzC,kBAAkB,SAAS,MAAM,IAAI;QACvC;IACF;IAEA,OAAO;IACP,MAAM,sBAAsB,CAAC;QAC3B,QAAQ,GAAG,CAAC,CAAC,gDAAgD,EAAE,KAAK,GAAG,GAAG,mBAAmB,CAAC,EAAE;QAChG,aAAa,OAAO,GAAG;QAEvB,OAAO;QACP,MAAM,gBAAgB,kBAAkB;QACxC,cAAc;QACd,kBAAkB;QAClB,iBAAiB,EAAE;QAEnB,QAAQ,GAAG,CAAC,CAAC,0DAA0D,EAAE,cAAc,CAAC,CAAC;QAEzF,QAAQ;QACR,kBAAkB;QAElB,0BAA0B;QAC1B,WAAW;YACT,QAAQ,GAAG,CAAC,CAAC,wCAAwC,EAAE,KAAK,GAAG,GAAG,iCAAiC,CAAC;YACpG,aAAa,OAAO,GAAG;QACzB,GAAG,MAAM,uBAAuB;QAEhC,OAAO;QACP,SAAS,OAAO,EAAE;IACpB;IAEA,YAAY;IACZ,MAAM,aAAa;QACjB,QAAQ,GAAG,CAAC,CAAC,uCAAuC,EAAE,KAAK,GAAG,GAAG,wBAAwB,EAAE,aAAa,OAAO,EAAE;QAEjH,qBAAqB;QACrB,+BAA+B;QAC/B,WAAW;YACT,IAAI,CAAC,aAAa,OAAO,EAAE;gBACzB,QAAQ,GAAG,CAAC,CAAC,iDAAiD,EAAE,KAAK,GAAG,GAAG,kBAAkB,CAAC;gBAC9F,kBAAkB;gBAElB,+BAA+B;gBAC/B,MAAM,mBAAmB,kBAAkB;gBAC3C,IAAI,eAAe,oBAAoB,WAAW,IAAI,OAAO,IAAI;oBAC/D,QAAQ,GAAG,CAAC,CAAC,yDAAyD,EAAE,WAAW,MAAM,EAAE,iBAAiB,CAAC,CAAC;oBAC9G,cAAc;gBAChB;YACF,OAAO;gBACL,QAAQ,GAAG,CAAC,CAAC,gDAAgD,EAAE,KAAK,GAAG,GAAG,4BAA4B,CAAC;YACzG;QACF,GAAG,MAAM,8BAA8B;IACzC;IAEA,OAAO;IACP,MAAM,cAAc,CAAC;QACnB,EAAE,eAAe,IAAI,gBAAgB;QACrC,cAAc;QACd,iBAAiB,EAAE;QACnB,kBAAkB;QAClB,kBAAkB;QAClB,SAAS,OAAO,EAAE;IACpB;IAEA,SAAS;IACT,MAAM,gBAAgB,CAAC;QACrB,IAAI,CAAC,kBAAkB,cAAc,MAAM,KAAK,GAAG;QAEnD,OAAQ,EAAE,GAAG;YACX,KAAK;gBACH,EAAE,cAAc;gBAChB,oBAAoB,CAAA,OAClB,OAAO,cAAc,MAAM,GAAG,IAAI,OAAO,IAAI;gBAE/C;YACF,KAAK;gBACH,EAAE,cAAc;gBAChB,oBAAoB,CAAA,OAClB,OAAO,IAAI,OAAO,IAAI,cAAc,MAAM,GAAG;gBAE/C;YACF,KAAK;gBACH,EAAE,cAAc;gBAChB,IAAI,oBAAoB,KAAK,mBAAmB,cAAc,MAAM,EAAE;oBACpE,oBAAoB,aAAa,CAAC,iBAAiB;gBACrD;gBACA;YACF,KAAK;gBACH,EAAE,cAAc;gBAChB,kBAAkB;gBAClB,SAAS,OAAO,EAAE;gBAClB;QACJ;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAM,WAAU;;kCACf,8OAAC;wBAAI,WAAU;wBAA8B,MAAK;wBAAO,SAAQ;wBAAY,QAAO;kCAClF,cAAA,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;oBAEtE;;;;;;;0BAGH,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,KAAK;wBACL,MAAK;wBACL,OAAO;wBACP,UAAU;wBACV,SAAS,IAAM,kBAAkB,WAAW,MAAM,IAAI;wBACtD,QAAQ;wBACR,WAAW;wBACX,aAAa;wBACb,UAAU;wBACV,WAAW,CAAC;;;;YAIV,EAAE,WAAW,oCAAoC,+CAA+C;YAChG,EAAE,QAAQ,oEAAoE,GAAG;YACjF,EAAE,iBAAiB,8DAA8D,GAAG;UACtF,CAAC;wBACD,cAAa;wBACb,MAAK;wBACL,iBAAe;wBACf,iBAAc;wBACd,qBAAkB;;;;;;kCAIpB,8OAAC;wBAAI,WAAU;;4BACZ,0BACC,8OAAC;gCAAI,WAAU;;;;;uCACb,sBACF,8OAAC;gCACC,MAAK;gCACL,SAAS;gCACT,WAAU;gCACV,cAAW;0CAEX,cAAA,8OAAC;oCAAI,WAAU;oCAAqB,MAAK;oCAAO,SAAQ;oCAAY,QAAO;8CACzE,cAAA,8OAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAK,GAAE;;;;;;;;;;;;;;;uCAGzE;0CAEJ,8OAAC;gCAAI,WAAW,CAAC,uCAAuC,EAAE,iBAAiB,mBAAmB,kBAAkB;gCAAE,OAAM;gCAA6B,MAAK;gCAAO,SAAQ;gCAAY,QAAO;0CAC1L,cAAA,8OAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;YAM1E,gCACC,8OAAC;gBAAI,WAAU;gBAA2I,OAAO;oBAAE,QAAQ;gBAAM;;oBAE9K,2BACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAK,WAAU;8CAAyB;;;;;;;;;;;;;;;;;kCAM/C,8OAAC;wBAAI,WAAU;kCACZ,cAAc,MAAM,KAAK,KAAK,CAAC,0BAC9B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;wCAAyB,MAAK;wCAAO,SAAQ;wCAAY,QAAO;kDAC7E,cAAA,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAK,GAAE;;;;;;;;;;;;;;;;8CAG3E,8OAAC;oCAAE,WAAU;8CAAqC;;;;;;8CAClD,8OAAC;oCAAE,WAAU;8CAA8B;;;;;;;;;;;mCAG7C,cAAc,GAAG,CAAC,CAAC,SAAS,sBAC1B,8OAAC;gCAEC,SAAS,IAAM,oBAAoB;gCACnC,WAAW,CAAC;;oBAEV,EAAE,UAAU,mBACR,4BACA,qBACH;kBACH,CAAC;gCACD,MAAK;gCACL,iBAAe,UAAU;0CAEzB,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DAEb,8OAAC;oDAAI,WAAW,CAAC;;wBAEf,EAAE,UAAU,mBACR,2BACA,0BACH;sBACH,CAAC;8DACC,cAAA,8OAAC;wDAAI,WAAU;wDAAc,MAAK;wDAAO,SAAQ;wDAAY,QAAO;kEAClE,cAAA,8OAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;;;;;;;;;;;;8DAKzE,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAW,CAAC;;0BAEf,EAAE,UAAU,mBAAmB,eAAe,iBAAiB;wBACjE,CAAC;sEACE,QAAQ,IAAI;;;;;;sEAEf,8OAAC;4DAAI,WAAW,CAAC;;0BAEf,EAAE,UAAU,mBAAmB,kBAAkB,iBAAiB;wBACpE,CAAC;;gEACE,QAAQ,IAAI;gEAAC;gEAAG,QAAQ,OAAO;;;;;;;;;;;;;;;;;;;sDAMtC,8OAAC;4CAAI,WAAW,CAAC;;sBAEf,EAAE,UAAU,mBAAmB,kBAAkB,iBAAiB;oBACpE,CAAC;sDACE,QAAQ,IAAI;;;;;;;;;;;;+BAjDZ,QAAQ,IAAI;;;;;;;;;;;;;;;;YA4D5B,uBACC,8OAAC;gBAAE,WAAU;0BAA+B;;;;;;;;;;;;AAIpD;uCAEe", "debugId": null}}, {"offset": {"line": 591, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/AeroScout/aeroscout-frontend/src/store/flightResultsStore.ts"], "sourcesContent": ["import { create } from 'zustand';\r\nimport { ApiFlightItinerary } from '@/lib/apiService';\r\n\r\nexport const POLLING_TIMEOUT_DURATION = 2 * 60 * 1000; // 2 minutes\r\nexport const MAX_CONSECUTIVE_FAILURES = 5;\r\n\r\nexport type SearchStatus = 'idle' | 'loading' | 'success' | 'error' | 'stopped'; // 添加 'stopped' 状态\r\nexport type PollingStoppedReason = 'timeout' | 'max_failures';\r\n\r\n// 定义更具体的类型\r\nexport interface AirportInfo {\r\n  code: string; // IATA code, e.g., \"PVG\"\r\n  name: string; // e.g., \"Shanghai Pudong International Airport\"\r\n  cityName: string; // e.g., \"Shanghai\" - mapped from apiService.ApiAirportInfo.city_name\r\n  cityCode?: string;\r\n  countryName?: string;\r\n  countryCode?: string;\r\n}\r\n\r\nexport interface AirlineInfo {\r\n  code: string; // IATA code\r\n  name: string;\r\n  logoUrl?: string; // mapped from apiService.ApiAirlineInfo.logo_url\r\n}\r\n\r\nexport interface FlightSegment {\r\n  id?: string;\r\n  airlineCode: string; // mapped from apiService.ApiFlightSegment.airline_code\r\n  airlineName: string; // mapped from apiService.ApiFlightSegment.airline_name\r\n  airlineLogoUrl?: string; // 航空公司 Logo 的 URL\r\n  flightNumber: string; // mapped from apiService.ApiFlightSegment.flight_number\r\n  departureAirportCode: string; // mapped from apiService.ApiFlightSegment.departure_airport_code\r\n  departureAirportName: string; // mapped from apiService.ApiFlightSegment.departure_airport_name\r\n  departureAirportFull?: string; // 出发机场的详细名称/信息\r\n  departureCityName: string; // mapped from apiService.ApiFlightSegment.departure_city_name\r\n  departureTime: string; // mapped from apiService.ApiFlightSegment.departure_time\r\n  departureTerminal?: string; // Added: e.g., \"T2\" - assumed from apiSegment.departure_terminal\r\n  arrivalAirportCode: string; // mapped from apiService.ApiFlightSegment.arrival_airport_code\r\n  arrivalAirportName: string; // mapped from apiService.ApiFlightSegment.arrival_airport_name\r\n  arrivalAirportFull?: string; // 到达机场的详细名称/信息\r\n  arrivalCityName: string; // mapped from apiService.ApiFlightSegment.arrival_city_name\r\n  arrivalTime: string; // mapped from apiService.ApiFlightSegment.arrival_time\r\n  arrivalTerminal?: string; // Added: e.g., \"T1\" - assumed from apiSegment.arrival_terminal\r\n  durationMinutes: number; // mapped from apiService.ApiFlightSegment.duration_minutes\r\n  cabinClass?: string; // mapped from apiService.ApiFlightSegment.cabin_class\r\n  equipment?: string; // 机型信息\r\n  isLayover?: boolean; // 标记此航段是否为中转停留的一部分\r\n  layoverDuration?: string; // 如果是中转，中转时长\r\n  nextSegmentRequiresAirportChange?: boolean; // 如果是中转，下一段是否需要更换机场\r\n  isBaggageRecheck?: boolean; // 如果是中转，是否需要重新托运行李\r\n  operatingCarrierCode?: string; // 实际执飞航司代码\r\n  operatingCarrierName?: string; // 实际执飞航司名称\r\n}\r\n\r\nexport interface TransferInfo {\r\n  city?: string; // City where the transfer occurs\r\n  durationMinutes: number; // Duration of the layover in minutes\r\n  isDifferentAirport: boolean; // True if transfer involves changing airports\r\n  airportChangeDetail?: {\r\n    fromAirportCode: string;\r\n    toAirportCode: string;\r\n  };\r\n  layoverTime?: string; // Formatted layover time e.g., \"2h 30m\"\r\n  isBaggageRecheck?: boolean; // Whether baggage needs to be rechecked during this transfer\r\n  isAirlineChange?: boolean; // Whether the airline changes during this transfer\r\n  fromAirline?: {\r\n    code: string;\r\n    name: string;\r\n  };\r\n  toAirline?: {\r\n    code: string;\r\n    name: string;\r\n  };\r\n}\r\n\r\nexport interface FlightItinerary {\r\n  id: string; // Unique ID for the itinerary\r\n  segments: FlightSegment[];\r\n  transfers?: TransferInfo[]; // Added: Detailed transfer information\r\n  totalDurationMinutes: number; // mapped from apiService.ApiFlightItinerary.total_duration_minutes\r\n  totalTravelTime?: string; // 整个行程的总旅行时间（包括中转）\r\n  price: {\r\n    amount: number;\r\n    currency: string;\r\n  };\r\n  airlines?: Array<{ // 参与该行程的所有航司信息\r\n    code: string;\r\n    name: string;\r\n    logoUrl?: string;\r\n  }>;\r\n  isDirectFlight: boolean; // mapped from apiService.ApiFlightItinerary.is_direct_flight\r\n  bookingToken?: string; // mapped from apiService.ApiFlightItinerary.booking_token\r\n  deepLink?: string; // mapped from apiService.ApiFlightItinerary.deep_link\r\n  // Added for more itinerary details\r\n  numberOfStops?: number;\r\n  isProbeSuggestion?: boolean; // 是否为通过\"中国中转城市探测\"逻辑找到的建议\r\n  probeHub?: string; // 如果是探测建议，相关的枢纽城市代码或名称\r\n  probeDisclaimer?: string; // Specific disclaimer for this probe suggestion\r\n  isComboDeal?: boolean; // Though often determined by array, map if API provides\r\n  providerName?: string; // e.g., \"Kiwi.com\"\r\n  tags?: string[]; // For any other relevant tags from the API\r\n  // 添加自行中转和隐藏城市标志\r\n  isSelfTransfer?: boolean; // 是否为自行中转航班\r\n  isHiddenCity?: boolean; // 是否为隐藏城市航班\r\n  isThrowawayDeal?: boolean; // 是否为甩尾票\r\n  isTrueHiddenCity?: boolean; // 是否为真正的隐藏城市航班\r\n  hiddenDestination?: { // 隐藏目的地信息（用于甩尾票）\r\n    code: string; // 机场代码\r\n    name: string; // 机场名称\r\n    cityName: string; // 城市名称\r\n    countryName?: string; // 国家名称\r\n  };\r\n  // Optional: store the raw API data for debugging or advanced use cases\r\n  // rawApiItinerary?: ApiFlightItinerary;\r\n}\r\n\r\nexport interface FlightData { // API 响应中 'result' 字段的结构 (TaskResultData in apiService)\r\n  directFlights?: ApiFlightItinerary[]; // Corresponds to \"direct_flights\" in TaskResultData\r\n  comboDeals?: ApiFlightItinerary[]; // Corresponds to \"combo_deals\" in TaskResultData\r\n  disclaimers?: string[];\r\n  // 🔧 添加后端实际返回的字段名支持\r\n  direct_flights?: ApiFlightItinerary[]; // 后端实际返回的字段名\r\n  hidden_city_flights?: ApiFlightItinerary[]; // 后端实际返回的字段名\r\n  // 根据 API_Documentation.md 或实际 API 响应，可能还有 search_parameters, context 等字段\r\n}\r\n\r\nexport interface ErrorInfo {\r\n  message: string;\r\n  type?: 'network' | 'server' | 'client' | 'timeout' | string;\r\n  details?: Record<string, unknown>; // 使用Record<string, unknown>代替any，更安全\r\n}\r\n\r\ninterface FlightResultsState {\r\n  taskId: string | null;\r\n  searchStatus: SearchStatus;\r\n  directFlights: FlightItinerary[];\r\n  comboDeals: FlightItinerary[];\r\n  disclaimers: string[];\r\n  error: string | ErrorInfo | null;\r\n  // 新增状态用于超时和连续失败处理\r\n  pollingTimeoutId: NodeJS.Timeout | null;\r\n  consecutiveFailures: number;\r\n  pollingStoppedReason: PollingStoppedReason | null;\r\n  lastActivityTime: number | null;\r\n\r\n  // Actions\r\n  setSearchInitiated: (taskId: string) => void;\r\n  setSearchPolling: () => void; // 用于任务状态 PENDING/STARTED 时，维持 loading 状态\r\n  setSearchSuccess: (data: FlightData, isFinalResult: boolean) => void; // 添加 isFinalResult 参数\r\n  setSearchError: (errorMessage: string | ErrorInfo) => void;\r\n  resetFlightSearch: () => void;\r\n  // 新增 action 用于处理轮询停止\r\n  stopPolling: (reason: PollingStoppedReason) => void;\r\n  // 新增立即设置loading状态的action\r\n  setSearchLoading: () => void;\r\n}\r\n\r\nconst initialState: Omit<FlightResultsState, 'setSearchInitiated' | 'setSearchPolling' | 'setSearchSuccess' | 'setSearchError' | 'resetFlightSearch' | 'stopPolling' | 'setSearchLoading'> = {\r\n  taskId: null,\r\n  searchStatus: 'idle',\r\n  directFlights: [],\r\n  comboDeals: [],\r\n  disclaimers: [],\r\n  error: null,\r\n  // 初始化新增状态\r\n  pollingTimeoutId: null,\r\n  consecutiveFailures: 0,\r\n  pollingStoppedReason: null,\r\n  lastActivityTime: null,\r\n};\r\n\r\nexport const useFlightResultsStore = create<FlightResultsState>((set, get) => ({\r\n  ...initialState,\r\n\r\n  setSearchLoading: () => {\r\n    console.log('🔄 [DEBUG] 立即设置loading状态');\r\n    set({\r\n      searchStatus: 'loading',\r\n      error: null,\r\n      lastActivityTime: Date.now(),\r\n    });\r\n  },\r\n\r\n  setSearchInitiated: (taskId) => {\r\n    const { pollingTimeoutId } = get();\r\n    if (pollingTimeoutId) {\r\n      clearTimeout(pollingTimeoutId);\r\n    }\r\n    const newTimeoutId = setTimeout(() => get().stopPolling('timeout'), POLLING_TIMEOUT_DURATION);\r\n\r\n    set({\r\n      ...initialState, // 重置结果和错误，开始新的搜索\r\n      taskId,\r\n      searchStatus: 'loading',\r\n      lastActivityTime: Date.now(),\r\n      pollingTimeoutId: newTimeoutId,\r\n      consecutiveFailures: 0,\r\n      pollingStoppedReason: null,\r\n    });\r\n  },\r\n\r\n  setSearchPolling: () =>\r\n    set((state) => {\r\n      if (state.pollingStoppedReason) { // 如果已停止，则不改变状态\r\n        return {};\r\n      }\r\n      // 仅当当前状态为 'loading' 时，才需要显式设置为 'loading'\r\n      // 如果已经是 'success' 或 'error'，则不应覆盖\r\n      if (state.searchStatus === 'loading') {\r\n        return { searchStatus: 'loading', lastActivityTime: Date.now() };\r\n      }\r\n      // 如果是从 'idle' 状态因为某种原因调用（理论上不应该），也设置为 'loading'\r\n      if (state.searchStatus === 'idle' && state.taskId) {\r\n         return { searchStatus: 'loading', lastActivityTime: Date.now() };\r\n      }\r\n      return {}; // 其他状态下不改变\r\n    }),\r\n\r\n  setSearchSuccess: (data: FlightData, isFinalResult: boolean) => {\r\n    console.log('🔍 === setSearchSuccess 调试信息 ===');\r\n    console.log('🔍 接收到的原始数据:', data);\r\n    console.log('🔍 数据类型:', typeof data);\r\n    console.log('🔍 是否为最终结果:', isFinalResult);\r\n    console.log('🔍 directFlights 字段:', data.directFlights);\r\n    console.log('🔍 comboDeals 字段:', data.comboDeals);\r\n    console.log('🔍 directFlights 长度:', data.directFlights?.length);\r\n    console.log('🔍 comboDeals 长度:', data.comboDeals?.length);\r\n    \r\n    // 🔍 添加详细的API响应数据结构分析\r\n    console.log('🔍 === API响应数据结构详细分析 ===');\r\n    console.log('🔍 完整的data对象键名:', Object.keys(data));\r\n    \r\n    // 检查是否有其他可能的字段名\r\n    const possibleFlightFields = ['directFlights', 'direct_flights', 'comboDeals', 'combo_deals', 'hiddenCityFlights', 'hidden_city_flights'];\r\n    possibleFlightFields.forEach(field => {\r\n      const fieldValue = (data as Record<string, unknown>)[field];\r\n      if (fieldValue) {\r\n        console.log(`🔍 发现字段 ${field}:`, fieldValue);\r\n        console.log(`🔍 ${field} 长度:`, Array.isArray(fieldValue) ? fieldValue.length : '不是数组');\r\n        \r\n        // 如果是数组且有数据，显示第一个元素的结构\r\n        if (Array.isArray(fieldValue) && fieldValue.length > 0) {\r\n          const firstItem = fieldValue[0] as Record<string, unknown>;\r\n          console.log(`🔍 ${field} 第一个元素的键名:`, Object.keys(firstItem));\r\n          console.log(`🔍 ${field} 第一个元素完整数据:`, firstItem);\r\n          \r\n          // 检查航段数据结构\r\n          const possibleSegmentFields = ['segments', 'outbound_segments', 'inbound_segments', 'sector', 'sectorSegments'];\r\n          possibleSegmentFields.forEach(segField => {\r\n            const segmentValue = firstItem[segField];\r\n            if (segmentValue) {\r\n              console.log(`🔍 ${field} 中发现航段字段 ${segField}:`, segmentValue);\r\n              if (Array.isArray(segmentValue) && segmentValue.length > 0) {\r\n                console.log(`🔍 ${segField} 第一个航段数据:`, segmentValue[0]);\r\n              }\r\n            }\r\n          });\r\n        }\r\n      }\r\n    });\r\n    \r\n    const { pollingTimeoutId } = get();\r\n    if (pollingTimeoutId) {\r\n      clearTimeout(pollingTimeoutId);\r\n    }\r\n\r\n    let newTimeoutId: NodeJS.Timeout | null = null;\r\n    if (!isFinalResult) {\r\n      newTimeoutId = setTimeout(() => get().stopPolling('timeout'), POLLING_TIMEOUT_DURATION);\r\n    }\r\n\r\n    // 🔧 修复数据映射逻辑，支持后端实际返回的字段名\r\n    console.log('开始映射 API 数据到 Store 格式...');\r\n    \r\n    // 支持后端实际返回的字段名\r\n    const directFlights = data.directFlights || data.direct_flights || [];\r\n    const hiddenCityFlights = data.comboDeals || data.hidden_city_flights || [];\r\n    \r\n    console.log('🔧 使用的数据源:');\r\n    console.log('- directFlights 来源:', data.directFlights ? 'data.directFlights' : 'data.direct_flights');\r\n    console.log('- hiddenCityFlights 来源:', data.comboDeals ? 'data.comboDeals' : 'data.hidden_city_flights');\r\n    console.log('- directFlights 数量:', directFlights.length);\r\n    console.log('- hiddenCityFlights 数量:', hiddenCityFlights.length);\r\n    \r\n    const mappedDirectFlights = directFlights.map(mapApiItineraryToStoreItinerary);\r\n    const mappedComboDeals = hiddenCityFlights.map(mapApiItineraryToStoreItinerary);\r\n    \r\n    console.log('映射完成:');\r\n    console.log('- 直飞航班映射结果数量:', mappedDirectFlights.length);\r\n    console.log('- 隐藏城市航班映射结果数量:', mappedComboDeals.length);\r\n    \r\n    // 后端已经处理去重，前端直接使用映射结果\r\n    console.log('使用后端去重结果:');\r\n    console.log('- 直飞航班数量:', mappedDirectFlights.length);\r\n    console.log('- 组合航班数量:', mappedComboDeals.length);\r\n    \r\n    // 如果有映射结果，显示第一个的详细信息\r\n    if (mappedDirectFlights.length > 0) {\r\n      console.log('第一个映射后的直飞航班:', mappedDirectFlights[0]);\r\n    }\r\n    if (mappedComboDeals.length > 0) {\r\n      console.log('第一个映射后的组合航班:', mappedComboDeals[0]);\r\n    }\r\n\r\n    set({\r\n      directFlights: mappedDirectFlights,\r\n      comboDeals: mappedComboDeals,\r\n      disclaimers: data.disclaimers || [],\r\n      searchStatus: isFinalResult ? 'success' : 'loading', // 如果不是最终结果，保持loading以继续轮询\r\n      error: null, // 清除之前的错误\r\n      consecutiveFailures: 0, // 重置连续失败次数\r\n      lastActivityTime: Date.now(),\r\n      pollingTimeoutId: newTimeoutId,\r\n      pollingStoppedReason: isFinalResult ? get().pollingStoppedReason : null, // 如果是最终结果，保留之前的停止原因（如果有），否则重置\r\n    });\r\n    \r\n    console.log('Store 状态更新完成');\r\n    console.log('当前 Store 状态:', get());\r\n  },\r\n\r\n  setSearchError: (error: string | ErrorInfo) => {\r\n    const { pollingTimeoutId, consecutiveFailures: prevFailures } = get();\r\n    if (pollingTimeoutId) {\r\n      clearTimeout(pollingTimeoutId);\r\n    }\r\n    const currentFailures = prevFailures + 1;\r\n\r\n    if (currentFailures >= MAX_CONSECUTIVE_FAILURES) {\r\n      get().stopPolling('max_failures');\r\n      set({\r\n        searchStatus: 'stopped', // 更新状态为 stopped\r\n        error: error,\r\n        consecutiveFailures: currentFailures,\r\n        pollingTimeoutId: null,\r\n        lastActivityTime: Date.now(),\r\n      });\r\n    } else {\r\n      // 如果未达到最大失败次数，仍然设置超时以便下次轮询\r\n      const newTimeoutId = setTimeout(() => get().stopPolling('timeout'), POLLING_TIMEOUT_DURATION);\r\n      set({\r\n        searchStatus: 'error', // 或 'loading' 如果希望错误后继续尝试轮询直到超时或最大失败\r\n        error: error,\r\n        consecutiveFailures: currentFailures,\r\n        pollingTimeoutId: newTimeoutId, // 重新设置超时\r\n        lastActivityTime: Date.now(),\r\n      });\r\n    }\r\n  },\r\n\r\n  stopPolling: (reason: PollingStoppedReason) => {\r\n    const { pollingTimeoutId } = get();\r\n    if (pollingTimeoutId) {\r\n      clearTimeout(pollingTimeoutId);\r\n    }\r\n    let errorMessage: ErrorInfo | string = '搜索已停止。';\r\n    if (reason === 'timeout') {\r\n        errorMessage = { type: 'timeout', message: '搜索超时，请稍后重试或调整搜索条件。' };\r\n    } else if (reason === 'max_failures') {\r\n        errorMessage = { type: 'max_failures', message: '多次尝试连接失败，请检查您的网络连接或稍后重试。' };\r\n    }\r\n\r\n    set({\r\n      searchStatus: 'stopped',\r\n      pollingStoppedReason: reason,\r\n      pollingTimeoutId: null,\r\n      error: errorMessage, // 设置相应的错误信息\r\n      lastActivityTime: Date.now(),\r\n    });\r\n  },\r\n\r\n  resetFlightSearch: () => {\r\n    const { pollingTimeoutId } = get();\r\n    if (pollingTimeoutId) {\r\n      clearTimeout(pollingTimeoutId);\r\n    }\r\n    set(initialState);\r\n  },\r\n}));\r\n\r\n\r\n// 以上类型定义已移至文件顶部并取消注释\r\n\r\n// 映射函数：将 API 响应类型转换为前端状态类型\r\n// 导出以便测试\r\nexport const mapApiItineraryToStoreItinerary = (apiItinerary: Record<string, unknown>): FlightItinerary => {\r\n  console.log('=== mapApiItineraryToStoreItinerary 调试 ===');\r\n  console.log('输入的 apiItinerary:', apiItinerary);\r\n  \r\n  // === 价格调试信息 ===\r\n  console.log('=== 价格字段调试 ===');\r\n  console.log('price:', apiItinerary.price, typeof apiItinerary.price);\r\n  console.log('currency:', apiItinerary.currency, typeof apiItinerary.currency);\r\n  console.log('原始price字段（deprecated）:', apiItinerary.price_eur);\r\n  \r\n  // 🔧 处理后端实际返回的数据结构\r\n  const segments = (apiItinerary.segments as Record<string, unknown>[]) ||\r\n                   (apiItinerary.outbound_segments as Record<string, unknown>[]) || [];\r\n  console.log('🔧 提取的 segments:', segments);\r\n  console.log('🔧 segments 来源:', apiItinerary.segments ? 'apiItinerary.segments' : 'apiItinerary.outbound_segments');\r\n  \r\n  // 计算总旅行时间 - 支持多种字段名\r\n  const totalDurationMinutes = (apiItinerary.totalDurationMinutes as number) ||\r\n                               (apiItinerary.total_duration_minutes as number) ||\r\n                               (apiItinerary.duration_minutes as number) ||\r\n                               (apiItinerary.duration as number) || 0;\r\n  \r\n  // 添加飞行时间调试信息\r\n  console.log('=== 飞行时间字段调试 ===');\r\n  console.log('totalDurationMinutes:', apiItinerary.totalDurationMinutes, typeof apiItinerary.totalDurationMinutes);\r\n  console.log('total_duration_minutes:', apiItinerary.total_duration_minutes, typeof apiItinerary.total_duration_minutes);\r\n  console.log('duration_minutes:', apiItinerary.duration_minutes, typeof apiItinerary.duration_minutes);\r\n  console.log('duration:', apiItinerary.duration, typeof apiItinerary.duration);\r\n  console.log('最终使用的totalDurationMinutes:', totalDurationMinutes);\r\n  \r\n  const totalTravelTime = (() => {\r\n    const hours = Math.floor(totalDurationMinutes / 60);\r\n    const minutes = totalDurationMinutes % 60;\r\n    const timeStr = `${hours}h ${minutes > 0 ? `${minutes}m` : ''}`.trim();\r\n    console.log('格式化后的飞行时间:', timeStr);\r\n    return timeStr;\r\n  })();\r\n\r\n  // 确定是否为直飞航班\r\n  const isDirectFlight = segments.length === 1;\r\n\r\n  // 计算中转次数\r\n  const numberOfStops = segments.length > 0 ? segments.length - 1 : 0;\r\n  \r\n  // 🔍 添加调试日志来诊断中转判断问题\r\n  console.log('🔍 数据映射器中转诊断 - 航班ID:', apiItinerary.id);\r\n  console.log('🔍 原始segments数量:', segments.length);\r\n  console.log('🔍 计算的isDirectFlight:', isDirectFlight);\r\n  console.log('🔍 计算的numberOfStops:', numberOfStops);\r\n  console.log('🔍 原始segments详情:', segments.map((seg, idx) => ({\r\n    index: idx,\r\n    from: `${seg.departure_city || seg.departureCityName}(${seg.departure_airport || seg.departureAirportCode})`,\r\n    to: `${seg.arrival_city || seg.arrivalCityName}(${seg.arrival_airport || seg.arrivalAirportCode})`,\r\n    flight: seg.flight_number || seg.flightNumber\r\n  })));\r\n\r\n  // 映射航段信息\r\n  const mappedSegments = segments.map((segment: Record<string, unknown>, index: number) => {\r\n    console.log(`映射航段 ${index + 1}:`, segment);\r\n    \r\n    // 🔧 处理后端Kiwi API字段名到前端字段名的映射\r\n    const carrier = segment.carrier as Record<string, unknown> || {};\r\n    const origin = segment.origin as Record<string, unknown> || {};\r\n    const destination = segment.destination as Record<string, unknown> || {};\r\n    const departure = segment.departure as Record<string, unknown> || {};\r\n    const arrival = segment.arrival as Record<string, unknown> || {};\r\n    \r\n    const mappedSegment = {\r\n      id: (segment.id as string) || `segment-${index}`,\r\n      // 🔧 支持Kiwi API的carrier结构\r\n      airlineCode: (carrier.code as string) || (segment.carrier_code as string) || (segment.airlineCode as string) || '',\r\n      airlineName: (carrier.name as string) || (segment.carrier_name as string) || (segment.airlineName as string) || '',\r\n      flightNumber: (segment.flight_number as string) || (segment.flightNumber as string) || '',\r\n      // 🔧 支持Kiwi API的origin/destination结构\r\n      departureAirportCode: (origin.code as string) || (segment.departure_airport as string) || (segment.departureAirportCode as string) || '',\r\n      departureAirportName: (origin.name as string) || (segment.departure_airport_name as string) || (segment.departureAirportName as string) || '',\r\n      departureCityName: (origin.city as string) || (segment.departure_city as string) || (segment.departureCityName as string) || '',\r\n      departureTime: (departure.local_time as string) || (segment.departure_time as string) || (segment.departureTime as string) || '',\r\n      arrivalAirportCode: (destination.code as string) || (segment.arrival_airport as string) || (segment.arrivalAirportCode as string) || '',\r\n      arrivalAirportName: (destination.name as string) || (segment.arrival_airport_name as string) || (segment.arrivalAirportName as string) || '',\r\n      arrivalCityName: (destination.city as string) || (segment.arrival_city as string) || (segment.arrivalCityName as string) || '',\r\n      arrivalTime: (arrival.local_time as string) || (segment.arrival_time as string) || (segment.arrivalTime as string) || '',\r\n      durationMinutes: (segment.duration_minutes as number) || (segment.durationMinutes as number) || 0,\r\n      cabinClass: (segment.cabin_class as string) || (segment.cabinClass as string) || '',\r\n      airlineLogoUrl: (segment.airlineLogoUrl as string) || ((carrier.code as string) || (segment.carrier_code as string) || (segment.airlineCode as string)\r\n        ? `https://daisycon.io/images/airline/?width=300&height=150&iata=${(carrier.code as string) || (segment.carrier_code as string) || (segment.airlineCode as string)}`\r\n        : undefined),\r\n      departureAirportFull: (origin.name as string) || (segment.departure_airport_name as string) || (segment.departureAirportName as string)\r\n        ? `${(origin.name as string) || (segment.departure_airport_name as string) || (segment.departureAirportName as string)} (${(origin.code as string) || (segment.departure_airport as string) || (segment.departureAirportCode as string)})`\r\n        : (origin.code as string) || (segment.departure_airport as string) || (segment.departureAirportCode as string) || '',\r\n      arrivalAirportFull: (destination.name as string) || (segment.arrival_airport_name as string) || (segment.arrivalAirportName as string)\r\n        ? `${(destination.name as string) || (segment.arrival_airport_name as string) || (segment.arrivalAirportName as string)} (${(destination.code as string) || (segment.arrival_airport as string) || (segment.arrivalAirportCode as string)})`\r\n        : (destination.code as string) || (segment.arrival_airport as string) || (segment.arrivalAirportCode as string) || '',\r\n      equipment: (segment.aircraft as string) || (segment.equipment as string),\r\n      isLayover: index > 0,\r\n      operatingCarrierCode: (segment.operating_carrier_code as string) || (segment.operatingCarrierCode as string),\r\n      operatingCarrierName: (segment.operating_carrier_name as string) || (segment.operatingCarrierName as string),\r\n    };\r\n    \r\n    console.log(`映射后的航段 ${index + 1}:`, mappedSegment);\r\n    return mappedSegment;\r\n  });\r\n\r\n  // 构建中转信息\r\n  const transfers = segments.length > 1\r\n    ? segments.slice(0, -1).map((currentSegment: Record<string, unknown>, index: number) => {\r\n        const nextSegment = segments[index + 1];\r\n\r\n        // 🔧 支持Kiwi API的嵌套结构\r\n        const currentArrivalData = (currentSegment.arrival as Record<string, unknown>) || {};\r\n        const nextDepartureData = (nextSegment.departure as Record<string, unknown>) || {};\r\n        const currentDestination = (currentSegment.destination as Record<string, unknown>) || {};\r\n        const nextOrigin = (nextSegment.origin as Record<string, unknown>) || {};\r\n        const currentCarrierData = (currentSegment.carrier as Record<string, unknown>) || {};\r\n        const nextCarrierData = (nextSegment.carrier as Record<string, unknown>) || {};\r\n        \r\n        // 计算中转时长\r\n        const currentArrivalTime = (currentArrivalData.local_time as string) || (currentSegment.arrival_time as string) || (currentSegment.arrivalTime as string);\r\n        const nextDepartureTime = (nextDepartureData.local_time as string) || (nextSegment.departure_time as string) || (nextSegment.departureTime as string);\r\n        const currentArrival = new Date(currentArrivalTime).getTime();\r\n        const nextDeparture = new Date(nextDepartureTime).getTime();\r\n        const durationMinutes = Math.round((nextDeparture - currentArrival) / (1000 * 60));\r\n\r\n        // 格式化中转时间\r\n        const hours = Math.floor(durationMinutes / 60);\r\n        const minutes = durationMinutes % 60;\r\n        const layoverTime = `${hours > 0 ? `${hours}h ` : ''}${minutes > 0 ? `${minutes}m` : (hours === 0 ? '0m' : '')}`.trim();\r\n\r\n        // 检查是否需要换机场\r\n        const currentArrivalAirport = (currentDestination.code as string) || (currentSegment.arrival_airport as string) || (currentSegment.arrivalAirportCode as string);\r\n        const nextDepartureAirport = (nextOrigin.code as string) || (nextSegment.departure_airport as string) || (nextSegment.departureAirportCode as string);\r\n        const isDifferentAirport = currentArrivalAirport !== nextDepartureAirport;\r\n        \r\n        let airportChangeDetail;\r\n        if (isDifferentAirport) {\r\n          airportChangeDetail = {\r\n            fromAirportCode: currentArrivalAirport,\r\n            toAirportCode: nextDepartureAirport,\r\n          };\r\n        }\r\n\r\n        // 检查是否更换航空公司\r\n        const currentCarrier = (currentCarrierData.code as string) || (currentSegment.carrier_code as string) || (currentSegment.airlineCode as string);\r\n        const nextCarrier = (nextCarrierData.code as string) || (nextSegment.carrier_code as string) || (nextSegment.airlineCode as string);\r\n        const isAirlineChange = currentCarrier !== nextCarrier;\r\n\r\n        return {\r\n          city: (currentDestination.city as string) || (currentSegment.arrival_city as string) || (currentSegment.arrivalCityName as string) || '未知城市',\r\n          durationMinutes,\r\n          isDifferentAirport,\r\n          airportChangeDetail,\r\n          layoverTime,\r\n          isBaggageRecheck: isDifferentAirport, // 简化逻辑：不同机场需要重新托运\r\n          isAirlineChange,\r\n          fromAirline: {\r\n            code: currentCarrier || '',\r\n            name: (currentCarrierData.name as string) || (currentSegment.carrier_name as string) || (currentSegment.airlineName as string) || '',\r\n          },\r\n          toAirline: {\r\n            code: nextCarrier || '',\r\n            name: (nextCarrierData.name as string) || (nextSegment.carrier_name as string) || (nextSegment.airlineName as string) || '',\r\n          },\r\n        };\r\n      })\r\n    : undefined;\r\n\r\n  // 🔧 构建所有参与航司信息，支持Kiwi API的carrier结构\r\n  const airlines = segments.reduce((acc: Array<{ code: string; name: string; logoUrl?: string }>, segment: Record<string, unknown>) => {\r\n    const carrier = segment.carrier as Record<string, unknown> || {};\r\n    const carrierCode = (carrier.code as string) || (segment.carrier_code as string) || (segment.airlineCode as string);\r\n    const carrierName = (carrier.name as string) || (segment.carrier_name as string) || (segment.airlineName as string);\r\n    \r\n    if (carrierCode && !acc.find(a => a.code === carrierCode)) {\r\n      acc.push({\r\n        code: carrierCode,\r\n        name: carrierName || '',\r\n        logoUrl: `https://daisycon.io/images/airline/?width=300&height=150&iata=${carrierCode}`,\r\n      });\r\n    }\r\n    return acc;\r\n  }, []);\r\n\r\n  // 查找隐藏目的地信息（优先从顶层字段提取，然后从segments中提取）\r\n  let hiddenDestination: FlightItinerary['hiddenDestination'];\r\n  \r\n  // 首先检查顶层的hiddenDestination字段（V2 API适配器传入的）\r\n  if (apiItinerary.hiddenDestination) {\r\n    const hiddenDest = apiItinerary.hiddenDestination as Record<string, unknown>;\r\n    hiddenDestination = {\r\n      code: (hiddenDest.code as string) || '',\r\n      name: (hiddenDest.name as string) || '',\r\n      cityName: (hiddenDest.cityName as string) || (hiddenDest.city_name as string) ||\r\n                ((hiddenDest.city as Record<string, unknown>)?.name as string) || '',\r\n      countryName: (hiddenDest.countryName as string) || (hiddenDest.country_name as string) ||\r\n                   ((hiddenDest.country as Record<string, unknown>)?.name as string) || '',\r\n    };\r\n    console.log('🎯 从顶层字段找到隐藏目的地:', hiddenDestination);\r\n  } else {\r\n    // 如果顶层没有，再从segments中查找（兼容旧版本）\r\n    for (const segment of segments) {\r\n      if (segment.hidden_destination || segment.hiddenDestination) {\r\n        const hiddenDest = segment.hidden_destination || segment.hiddenDestination;\r\n        if (typeof hiddenDest === 'object' && hiddenDest !== null) {\r\n          const dest = hiddenDest as Record<string, unknown>;\r\n          hiddenDestination = {\r\n            code: (dest.code as string) || '',\r\n            name: (dest.name as string) || '',\r\n            cityName: (dest.city_name as string) || (dest.cityName as string) ||\r\n                      ((dest.city as Record<string, unknown>)?.name as string) || '',\r\n            countryName: (dest.country_name as string) || (dest.countryName as string) ||\r\n                         ((dest.country as Record<string, unknown>)?.name as string) || '',\r\n          };\r\n          console.log('🎯 从segments中找到隐藏目的地:', hiddenDestination);\r\n          break; // 找到第一个隐藏目的地就停止\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  // 🔧 构建最终的航班行程对象，正确处理隐藏城市航班标记\r\n  const isHiddenCityFlight = (apiItinerary.is_hidden_city as boolean) ||\r\n                             (apiItinerary.flight_type as string) === 'hidden_city' ||\r\n                             false;\r\n  \r\n  // 🔧 如果是隐藏城市航班，重新计算直飞状态\r\n  // 隐藏城市航班虽然只有一个航段，但实际上是中转航班（在中转城市下机）\r\n  const actualIsDirectFlight = isHiddenCityFlight ? false : isDirectFlight;\r\n  const actualNumberOfStops = isHiddenCityFlight ? 1 : numberOfStops; // 隐藏城市航班至少有1次中转\r\n  \r\n  console.log('🔧 隐藏城市航班处理:');\r\n  console.log('- 原始isDirectFlight:', isDirectFlight);\r\n  console.log('- isHiddenCityFlight:', isHiddenCityFlight);\r\n  console.log('- 修正后的actualIsDirectFlight:', actualIsDirectFlight);\r\n  console.log('- 原始numberOfStops:', numberOfStops);\r\n  console.log('- 修正后的actualNumberOfStops:', actualNumberOfStops);\r\n  \r\n  const result: FlightItinerary = {\r\n    id: (apiItinerary.id as string) || '',\r\n    segments: mappedSegments,\r\n    transfers,\r\n    totalDurationMinutes,\r\n    totalTravelTime,\r\n    price: {\r\n      amount: (() => {\r\n        const priceValue = apiItinerary.price;\r\n        if (typeof priceValue === 'number') {\r\n          return priceValue;\r\n        }\r\n        if (typeof priceValue === 'object' && priceValue !== null) {\r\n          const priceObj = priceValue as Record<string, unknown>;\r\n          const amount = priceObj.amount;\r\n          if (typeof amount === 'string') {\r\n            return parseFloat(amount) || 0;\r\n          }\r\n          if (typeof amount === 'number') {\r\n            return amount;\r\n          }\r\n        }\r\n        return 0;\r\n      })(),\r\n      currency: (apiItinerary.currency as string) || 'CNY',\r\n    },\r\n    airlines,\r\n    isDirectFlight: actualIsDirectFlight, // 🔧 使用修正后的直飞状态\r\n    bookingToken: (apiItinerary.booking_token as string),\r\n    deepLink: (apiItinerary.deep_link as string),\r\n    numberOfStops: actualNumberOfStops, // 🔧 使用修正后的中转次数\r\n    isProbeSuggestion: (apiItinerary.is_probe_suggestion as boolean) || false,\r\n    probeHub: (apiItinerary.probe_hub as string),\r\n    probeDisclaimer: (apiItinerary.probe_disclaimer as string),\r\n    isComboDeal: false, // 根据数组位置确定\r\n    providerName: 'Kiwi.com',\r\n    isSelfTransfer: (apiItinerary.is_self_transfer as boolean) || false,\r\n    isHiddenCity: isHiddenCityFlight, // 🔧 使用正确的隐藏城市标记\r\n    isThrowawayDeal: (apiItinerary.is_throwaway_deal as boolean) || false,\r\n    isTrueHiddenCity: (apiItinerary.is_true_hidden_city as boolean) || false,\r\n    hiddenDestination,\r\n  };\r\n\r\n  console.log('=== 简化后的价格映射结果 ===');\r\n  console.log('最终价格对象:', result.price);\r\n  console.log('甩尾票标记:', result.isHiddenCity, result.isThrowawayDeal);\r\n  console.log('最终映射结果:', result);\r\n  return result;\r\n};"], "names": [], "mappings": ";;;;;;AAAA;;AAGO,MAAM,2BAA2B,IAAI,KAAK,MAAM,YAAY;AAC5D,MAAM,2BAA2B;AAyJxC,MAAM,eAAuL;IAC3L,QAAQ;IACR,cAAc;IACd,eAAe,EAAE;IACjB,YAAY,EAAE;IACd,aAAa,EAAE;IACf,OAAO;IACP,UAAU;IACV,kBAAkB;IAClB,qBAAqB;IACrB,sBAAsB;IACtB,kBAAkB;AACpB;AAEO,MAAM,wBAAwB,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,EAAsB,CAAC,KAAK,MAAQ,CAAC;QAC7E,GAAG,YAAY;QAEf,kBAAkB;YAChB,QAAQ,GAAG,CAAC;YACZ,IAAI;gBACF,cAAc;gBACd,OAAO;gBACP,kBAAkB,KAAK,GAAG;YAC5B;QACF;QAEA,oBAAoB,CAAC;YACnB,MAAM,EAAE,gBAAgB,EAAE,GAAG;YAC7B,IAAI,kBAAkB;gBACpB,aAAa;YACf;YACA,MAAM,eAAe,WAAW,IAAM,MAAM,WAAW,CAAC,YAAY;YAEpE,IAAI;gBACF,GAAG,YAAY;gBACf;gBACA,cAAc;gBACd,kBAAkB,KAAK,GAAG;gBAC1B,kBAAkB;gBAClB,qBAAqB;gBACrB,sBAAsB;YACxB;QACF;QAEA,kBAAkB,IAChB,IAAI,CAAC;gBACH,IAAI,MAAM,oBAAoB,EAAE;oBAC9B,OAAO,CAAC;gBACV;gBACA,yCAAyC;gBACzC,kCAAkC;gBAClC,IAAI,MAAM,YAAY,KAAK,WAAW;oBACpC,OAAO;wBAAE,cAAc;wBAAW,kBAAkB,KAAK,GAAG;oBAAG;gBACjE;gBACA,gDAAgD;gBAChD,IAAI,MAAM,YAAY,KAAK,UAAU,MAAM,MAAM,EAAE;oBAChD,OAAO;wBAAE,cAAc;wBAAW,kBAAkB,KAAK,GAAG;oBAAG;gBAClE;gBACA,OAAO,CAAC,GAAG,WAAW;YACxB;QAEF,kBAAkB,CAAC,MAAkB;YACnC,QAAQ,GAAG,CAAC;YACZ,QAAQ,GAAG,CAAC,gBAAgB;YAC5B,QAAQ,GAAG,CAAC,YAAY,OAAO;YAC/B,QAAQ,GAAG,CAAC,eAAe;YAC3B,QAAQ,GAAG,CAAC,wBAAwB,KAAK,aAAa;YACtD,QAAQ,GAAG,CAAC,qBAAqB,KAAK,UAAU;YAChD,QAAQ,GAAG,CAAC,wBAAwB,KAAK,aAAa,EAAE;YACxD,QAAQ,GAAG,CAAC,qBAAqB,KAAK,UAAU,EAAE;YAElD,sBAAsB;YACtB,QAAQ,GAAG,CAAC;YACZ,QAAQ,GAAG,CAAC,mBAAmB,OAAO,IAAI,CAAC;YAE3C,gBAAgB;YAChB,MAAM,uBAAuB;gBAAC;gBAAiB;gBAAkB;gBAAc;gBAAe;gBAAqB;aAAsB;YACzI,qBAAqB,OAAO,CAAC,CAAA;gBAC3B,MAAM,aAAa,AAAC,IAAgC,CAAC,MAAM;gBAC3D,IAAI,YAAY;oBACd,QAAQ,GAAG,CAAC,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC,EAAE;oBACjC,QAAQ,GAAG,CAAC,CAAC,GAAG,EAAE,MAAM,IAAI,CAAC,EAAE,MAAM,OAAO,CAAC,cAAc,WAAW,MAAM,GAAG;oBAE/E,uBAAuB;oBACvB,IAAI,MAAM,OAAO,CAAC,eAAe,WAAW,MAAM,GAAG,GAAG;wBACtD,MAAM,YAAY,UAAU,CAAC,EAAE;wBAC/B,QAAQ,GAAG,CAAC,CAAC,GAAG,EAAE,MAAM,UAAU,CAAC,EAAE,OAAO,IAAI,CAAC;wBACjD,QAAQ,GAAG,CAAC,CAAC,GAAG,EAAE,MAAM,WAAW,CAAC,EAAE;wBAEtC,WAAW;wBACX,MAAM,wBAAwB;4BAAC;4BAAY;4BAAqB;4BAAoB;4BAAU;yBAAiB;wBAC/G,sBAAsB,OAAO,CAAC,CAAA;4BAC5B,MAAM,eAAe,SAAS,CAAC,SAAS;4BACxC,IAAI,cAAc;gCAChB,QAAQ,GAAG,CAAC,CAAC,GAAG,EAAE,MAAM,SAAS,EAAE,SAAS,CAAC,CAAC,EAAE;gCAChD,IAAI,MAAM,OAAO,CAAC,iBAAiB,aAAa,MAAM,GAAG,GAAG;oCAC1D,QAAQ,GAAG,CAAC,CAAC,GAAG,EAAE,SAAS,SAAS,CAAC,EAAE,YAAY,CAAC,EAAE;gCACxD;4BACF;wBACF;oBACF;gBACF;YACF;YAEA,MAAM,EAAE,gBAAgB,EAAE,GAAG;YAC7B,IAAI,kBAAkB;gBACpB,aAAa;YACf;YAEA,IAAI,eAAsC;YAC1C,IAAI,CAAC,eAAe;gBAClB,eAAe,WAAW,IAAM,MAAM,WAAW,CAAC,YAAY;YAChE;YAEA,2BAA2B;YAC3B,QAAQ,GAAG,CAAC;YAEZ,eAAe;YACf,MAAM,gBAAgB,KAAK,aAAa,IAAI,KAAK,cAAc,IAAI,EAAE;YACrE,MAAM,oBAAoB,KAAK,UAAU,IAAI,KAAK,mBAAmB,IAAI,EAAE;YAE3E,QAAQ,GAAG,CAAC;YACZ,QAAQ,GAAG,CAAC,uBAAuB,KAAK,aAAa,GAAG,uBAAuB;YAC/E,QAAQ,GAAG,CAAC,2BAA2B,KAAK,UAAU,GAAG,oBAAoB;YAC7E,QAAQ,GAAG,CAAC,uBAAuB,cAAc,MAAM;YACvD,QAAQ,GAAG,CAAC,2BAA2B,kBAAkB,MAAM;YAE/D,MAAM,sBAAsB,cAAc,GAAG,CAAC;YAC9C,MAAM,mBAAmB,kBAAkB,GAAG,CAAC;YAE/C,QAAQ,GAAG,CAAC;YACZ,QAAQ,GAAG,CAAC,iBAAiB,oBAAoB,MAAM;YACvD,QAAQ,GAAG,CAAC,mBAAmB,iBAAiB,MAAM;YAEtD,sBAAsB;YACtB,QAAQ,GAAG,CAAC;YACZ,QAAQ,GAAG,CAAC,aAAa,oBAAoB,MAAM;YACnD,QAAQ,GAAG,CAAC,aAAa,iBAAiB,MAAM;YAEhD,qBAAqB;YACrB,IAAI,oBAAoB,MAAM,GAAG,GAAG;gBAClC,QAAQ,GAAG,CAAC,gBAAgB,mBAAmB,CAAC,EAAE;YACpD;YACA,IAAI,iBAAiB,MAAM,GAAG,GAAG;gBAC/B,QAAQ,GAAG,CAAC,gBAAgB,gBAAgB,CAAC,EAAE;YACjD;YAEA,IAAI;gBACF,eAAe;gBACf,YAAY;gBACZ,aAAa,KAAK,WAAW,IAAI,EAAE;gBACnC,cAAc,gBAAgB,YAAY;gBAC1C,OAAO;gBACP,qBAAqB;gBACrB,kBAAkB,KAAK,GAAG;gBAC1B,kBAAkB;gBAClB,sBAAsB,gBAAgB,MAAM,oBAAoB,GAAG;YACrE;YAEA,QAAQ,GAAG,CAAC;YACZ,QAAQ,GAAG,CAAC,gBAAgB;QAC9B;QAEA,gBAAgB,CAAC;YACf,MAAM,EAAE,gBAAgB,EAAE,qBAAqB,YAAY,EAAE,GAAG;YAChE,IAAI,kBAAkB;gBACpB,aAAa;YACf;YACA,MAAM,kBAAkB,eAAe;YAEvC,IAAI,mBAAmB,0BAA0B;gBAC/C,MAAM,WAAW,CAAC;gBAClB,IAAI;oBACF,cAAc;oBACd,OAAO;oBACP,qBAAqB;oBACrB,kBAAkB;oBAClB,kBAAkB,KAAK,GAAG;gBAC5B;YACF,OAAO;gBACL,2BAA2B;gBAC3B,MAAM,eAAe,WAAW,IAAM,MAAM,WAAW,CAAC,YAAY;gBACpE,IAAI;oBACF,cAAc;oBACd,OAAO;oBACP,qBAAqB;oBACrB,kBAAkB;oBAClB,kBAAkB,KAAK,GAAG;gBAC5B;YACF;QACF;QAEA,aAAa,CAAC;YACZ,MAAM,EAAE,gBAAgB,EAAE,GAAG;YAC7B,IAAI,kBAAkB;gBACpB,aAAa;YACf;YACA,IAAI,eAAmC;YACvC,IAAI,WAAW,WAAW;gBACtB,eAAe;oBAAE,MAAM;oBAAW,SAAS;gBAAqB;YACpE,OAAO,IAAI,WAAW,gBAAgB;gBAClC,eAAe;oBAAE,MAAM;oBAAgB,SAAS;gBAA2B;YAC/E;YAEA,IAAI;gBACF,cAAc;gBACd,sBAAsB;gBACtB,kBAAkB;gBAClB,OAAO;gBACP,kBAAkB,KAAK,GAAG;YAC5B;QACF;QAEA,mBAAmB;YACjB,MAAM,EAAE,gBAAgB,EAAE,GAAG;YAC7B,IAAI,kBAAkB;gBACpB,aAAa;YACf;YACA,IAAI;QACN;IACF,CAAC;AAOM,MAAM,kCAAkC,CAAC;IAC9C,QAAQ,GAAG,CAAC;IACZ,QAAQ,GAAG,CAAC,qBAAqB;IAEjC,iBAAiB;IACjB,QAAQ,GAAG,CAAC;IACZ,QAAQ,GAAG,CAAC,UAAU,aAAa,KAAK,EAAE,OAAO,aAAa,KAAK;IACnE,QAAQ,GAAG,CAAC,aAAa,aAAa,QAAQ,EAAE,OAAO,aAAa,QAAQ;IAC5E,QAAQ,GAAG,CAAC,0BAA0B,aAAa,SAAS;IAE5D,mBAAmB;IACnB,MAAM,WAAW,AAAC,aAAa,QAAQ,IACrB,aAAa,iBAAiB,IAAkC,EAAE;IACpF,QAAQ,GAAG,CAAC,oBAAoB;IAChC,QAAQ,GAAG,CAAC,mBAAmB,aAAa,QAAQ,GAAG,0BAA0B;IAEjF,oBAAoB;IACpB,MAAM,uBAAuB,AAAC,aAAa,oBAAoB,IACjC,aAAa,sBAAsB,IACnC,aAAa,gBAAgB,IAC7B,aAAa,QAAQ,IAAe;IAElE,aAAa;IACb,QAAQ,GAAG,CAAC;IACZ,QAAQ,GAAG,CAAC,yBAAyB,aAAa,oBAAoB,EAAE,OAAO,aAAa,oBAAoB;IAChH,QAAQ,GAAG,CAAC,2BAA2B,aAAa,sBAAsB,EAAE,OAAO,aAAa,sBAAsB;IACtH,QAAQ,GAAG,CAAC,qBAAqB,aAAa,gBAAgB,EAAE,OAAO,aAAa,gBAAgB;IACpG,QAAQ,GAAG,CAAC,aAAa,aAAa,QAAQ,EAAE,OAAO,aAAa,QAAQ;IAC5E,QAAQ,GAAG,CAAC,8BAA8B;IAE1C,MAAM,kBAAkB,CAAC;QACvB,MAAM,QAAQ,KAAK,KAAK,CAAC,uBAAuB;QAChD,MAAM,UAAU,uBAAuB;QACvC,MAAM,UAAU,GAAG,MAAM,EAAE,EAAE,UAAU,IAAI,GAAG,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI;QACpE,QAAQ,GAAG,CAAC,cAAc;QAC1B,OAAO;IACT,CAAC;IAED,YAAY;IACZ,MAAM,iBAAiB,SAAS,MAAM,KAAK;IAE3C,SAAS;IACT,MAAM,gBAAgB,SAAS,MAAM,GAAG,IAAI,SAAS,MAAM,GAAG,IAAI;IAElE,qBAAqB;IACrB,QAAQ,GAAG,CAAC,wBAAwB,aAAa,EAAE;IACnD,QAAQ,GAAG,CAAC,oBAAoB,SAAS,MAAM;IAC/C,QAAQ,GAAG,CAAC,yBAAyB;IACrC,QAAQ,GAAG,CAAC,wBAAwB;IACpC,QAAQ,GAAG,CAAC,oBAAoB,SAAS,GAAG,CAAC,CAAC,KAAK,MAAQ,CAAC;YAC1D,OAAO;YACP,MAAM,GAAG,IAAI,cAAc,IAAI,IAAI,iBAAiB,CAAC,CAAC,EAAE,IAAI,iBAAiB,IAAI,IAAI,oBAAoB,CAAC,CAAC,CAAC;YAC5G,IAAI,GAAG,IAAI,YAAY,IAAI,IAAI,eAAe,CAAC,CAAC,EAAE,IAAI,eAAe,IAAI,IAAI,kBAAkB,CAAC,CAAC,CAAC;YAClG,QAAQ,IAAI,aAAa,IAAI,IAAI,YAAY;QAC/C,CAAC;IAED,SAAS;IACT,MAAM,iBAAiB,SAAS,GAAG,CAAC,CAAC,SAAkC;QACrE,QAAQ,GAAG,CAAC,CAAC,KAAK,EAAE,QAAQ,EAAE,CAAC,CAAC,EAAE;QAElC,8BAA8B;QAC9B,MAAM,UAAU,QAAQ,OAAO,IAA+B,CAAC;QAC/D,MAAM,SAAS,QAAQ,MAAM,IAA+B,CAAC;QAC7D,MAAM,cAAc,QAAQ,WAAW,IAA+B,CAAC;QACvE,MAAM,YAAY,QAAQ,SAAS,IAA+B,CAAC;QACnE,MAAM,UAAU,QAAQ,OAAO,IAA+B,CAAC;QAE/D,MAAM,gBAAgB;YACpB,IAAI,AAAC,QAAQ,EAAE,IAAe,CAAC,QAAQ,EAAE,OAAO;YAChD,0BAA0B;YAC1B,aAAa,AAAC,QAAQ,IAAI,IAAgB,QAAQ,YAAY,IAAgB,QAAQ,WAAW,IAAe;YAChH,aAAa,AAAC,QAAQ,IAAI,IAAgB,QAAQ,YAAY,IAAgB,QAAQ,WAAW,IAAe;YAChH,cAAc,AAAC,QAAQ,aAAa,IAAgB,QAAQ,YAAY,IAAe;YACvF,qCAAqC;YACrC,sBAAsB,AAAC,OAAO,IAAI,IAAgB,QAAQ,iBAAiB,IAAgB,QAAQ,oBAAoB,IAAe;YACtI,sBAAsB,AAAC,OAAO,IAAI,IAAgB,QAAQ,sBAAsB,IAAgB,QAAQ,oBAAoB,IAAe;YAC3I,mBAAmB,AAAC,OAAO,IAAI,IAAgB,QAAQ,cAAc,IAAgB,QAAQ,iBAAiB,IAAe;YAC7H,eAAe,AAAC,UAAU,UAAU,IAAgB,QAAQ,cAAc,IAAgB,QAAQ,aAAa,IAAe;YAC9H,oBAAoB,AAAC,YAAY,IAAI,IAAgB,QAAQ,eAAe,IAAgB,QAAQ,kBAAkB,IAAe;YACrI,oBAAoB,AAAC,YAAY,IAAI,IAAgB,QAAQ,oBAAoB,IAAgB,QAAQ,kBAAkB,IAAe;YAC1I,iBAAiB,AAAC,YAAY,IAAI,IAAgB,QAAQ,YAAY,IAAgB,QAAQ,eAAe,IAAe;YAC5H,aAAa,AAAC,QAAQ,UAAU,IAAgB,QAAQ,YAAY,IAAgB,QAAQ,WAAW,IAAe;YACtH,iBAAiB,AAAC,QAAQ,gBAAgB,IAAgB,QAAQ,eAAe,IAAe;YAChG,YAAY,AAAC,QAAQ,WAAW,IAAgB,QAAQ,UAAU,IAAe;YACjF,gBAAgB,AAAC,QAAQ,cAAc,IAAe,CAAC,AAAC,QAAQ,IAAI,IAAgB,QAAQ,YAAY,IAAgB,QAAQ,WAAW,GACvI,CAAC,8DAA8D,EAAE,AAAC,QAAQ,IAAI,IAAgB,QAAQ,YAAY,IAAgB,QAAQ,WAAW,EAAa,GAClK,SAAS;YACb,sBAAsB,AAAC,OAAO,IAAI,IAAgB,QAAQ,sBAAsB,IAAgB,QAAQ,oBAAoB,GACxH,GAAG,AAAC,OAAO,IAAI,IAAgB,QAAQ,sBAAsB,IAAgB,QAAQ,oBAAoB,CAAY,EAAE,EAAE,AAAC,OAAO,IAAI,IAAgB,QAAQ,iBAAiB,IAAgB,QAAQ,oBAAoB,CAAY,CAAC,CAAC,GACxO,AAAC,OAAO,IAAI,IAAgB,QAAQ,iBAAiB,IAAgB,QAAQ,oBAAoB,IAAe;YACpH,oBAAoB,AAAC,YAAY,IAAI,IAAgB,QAAQ,oBAAoB,IAAgB,QAAQ,kBAAkB,GACvH,GAAG,AAAC,YAAY,IAAI,IAAgB,QAAQ,oBAAoB,IAAgB,QAAQ,kBAAkB,CAAY,EAAE,EAAE,AAAC,YAAY,IAAI,IAAgB,QAAQ,eAAe,IAAgB,QAAQ,kBAAkB,CAAY,CAAC,CAAC,GAC1O,AAAC,YAAY,IAAI,IAAgB,QAAQ,eAAe,IAAgB,QAAQ,kBAAkB,IAAe;YACrH,WAAW,AAAC,QAAQ,QAAQ,IAAgB,QAAQ,SAAS;YAC7D,WAAW,QAAQ;YACnB,sBAAsB,AAAC,QAAQ,sBAAsB,IAAgB,QAAQ,oBAAoB;YACjG,sBAAsB,AAAC,QAAQ,sBAAsB,IAAgB,QAAQ,oBAAoB;QACnG;QAEA,QAAQ,GAAG,CAAC,CAAC,OAAO,EAAE,QAAQ,EAAE,CAAC,CAAC,EAAE;QACpC,OAAO;IACT;IAEA,SAAS;IACT,MAAM,YAAY,SAAS,MAAM,GAAG,IAChC,SAAS,KAAK,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,gBAAyC;QAClE,MAAM,cAAc,QAAQ,CAAC,QAAQ,EAAE;QAEvC,qBAAqB;QACrB,MAAM,qBAAqB,AAAC,eAAe,OAAO,IAAgC,CAAC;QACnF,MAAM,oBAAoB,AAAC,YAAY,SAAS,IAAgC,CAAC;QACjF,MAAM,qBAAqB,AAAC,eAAe,WAAW,IAAgC,CAAC;QACvF,MAAM,aAAa,AAAC,YAAY,MAAM,IAAgC,CAAC;QACvE,MAAM,qBAAqB,AAAC,eAAe,OAAO,IAAgC,CAAC;QACnF,MAAM,kBAAkB,AAAC,YAAY,OAAO,IAAgC,CAAC;QAE7E,SAAS;QACT,MAAM,qBAAqB,AAAC,mBAAmB,UAAU,IAAgB,eAAe,YAAY,IAAgB,eAAe,WAAW;QAC9I,MAAM,oBAAoB,AAAC,kBAAkB,UAAU,IAAgB,YAAY,cAAc,IAAgB,YAAY,aAAa;QAC1I,MAAM,iBAAiB,IAAI,KAAK,oBAAoB,OAAO;QAC3D,MAAM,gBAAgB,IAAI,KAAK,mBAAmB,OAAO;QACzD,MAAM,kBAAkB,KAAK,KAAK,CAAC,CAAC,gBAAgB,cAAc,IAAI,CAAC,OAAO,EAAE;QAEhF,UAAU;QACV,MAAM,QAAQ,KAAK,KAAK,CAAC,kBAAkB;QAC3C,MAAM,UAAU,kBAAkB;QAClC,MAAM,cAAc,GAAG,QAAQ,IAAI,GAAG,MAAM,EAAE,CAAC,GAAG,KAAK,UAAU,IAAI,GAAG,QAAQ,CAAC,CAAC,GAAI,UAAU,IAAI,OAAO,IAAK,CAAC,IAAI;QAErH,YAAY;QACZ,MAAM,wBAAwB,AAAC,mBAAmB,IAAI,IAAgB,eAAe,eAAe,IAAgB,eAAe,kBAAkB;QACrJ,MAAM,uBAAuB,AAAC,WAAW,IAAI,IAAgB,YAAY,iBAAiB,IAAgB,YAAY,oBAAoB;QAC1I,MAAM,qBAAqB,0BAA0B;QAErD,IAAI;QACJ,IAAI,oBAAoB;YACtB,sBAAsB;gBACpB,iBAAiB;gBACjB,eAAe;YACjB;QACF;QAEA,aAAa;QACb,MAAM,iBAAiB,AAAC,mBAAmB,IAAI,IAAgB,eAAe,YAAY,IAAgB,eAAe,WAAW;QACpI,MAAM,cAAc,AAAC,gBAAgB,IAAI,IAAgB,YAAY,YAAY,IAAgB,YAAY,WAAW;QACxH,MAAM,kBAAkB,mBAAmB;QAE3C,OAAO;YACL,MAAM,AAAC,mBAAmB,IAAI,IAAgB,eAAe,YAAY,IAAgB,eAAe,eAAe,IAAe;YACtI;YACA;YACA;YACA;YACA,kBAAkB;YAClB;YACA,aAAa;gBACX,MAAM,kBAAkB;gBACxB,MAAM,AAAC,mBAAmB,IAAI,IAAgB,eAAe,YAAY,IAAgB,eAAe,WAAW,IAAe;YACpI;YACA,WAAW;gBACT,MAAM,eAAe;gBACrB,MAAM,AAAC,gBAAgB,IAAI,IAAgB,YAAY,YAAY,IAAgB,YAAY,WAAW,IAAe;YAC3H;QACF;IACF,KACA;IAEJ,qCAAqC;IACrC,MAAM,WAAW,SAAS,MAAM,CAAC,CAAC,KAA8D;QAC9F,MAAM,UAAU,QAAQ,OAAO,IAA+B,CAAC;QAC/D,MAAM,cAAc,AAAC,QAAQ,IAAI,IAAgB,QAAQ,YAAY,IAAgB,QAAQ,WAAW;QACxG,MAAM,cAAc,AAAC,QAAQ,IAAI,IAAgB,QAAQ,YAAY,IAAgB,QAAQ,WAAW;QAExG,IAAI,eAAe,CAAC,IAAI,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,cAAc;YACzD,IAAI,IAAI,CAAC;gBACP,MAAM;gBACN,MAAM,eAAe;gBACrB,SAAS,CAAC,8DAA8D,EAAE,aAAa;YACzF;QACF;QACA,OAAO;IACT,GAAG,EAAE;IAEL,sCAAsC;IACtC,IAAI;IAEJ,2CAA2C;IAC3C,IAAI,aAAa,iBAAiB,EAAE;QAClC,MAAM,aAAa,aAAa,iBAAiB;QACjD,oBAAoB;YAClB,MAAM,AAAC,WAAW,IAAI,IAAe;YACrC,MAAM,AAAC,WAAW,IAAI,IAAe;YACrC,UAAU,AAAC,WAAW,QAAQ,IAAgB,WAAW,SAAS,IACtD,WAAW,IAAI,EAA8B,QAAmB;YAC5E,aAAa,AAAC,WAAW,WAAW,IAAgB,WAAW,YAAY,IAC5D,WAAW,OAAO,EAA8B,QAAmB;QACpF;QACA,QAAQ,GAAG,CAAC,oBAAoB;IAClC,OAAO;QACL,8BAA8B;QAC9B,KAAK,MAAM,WAAW,SAAU;YAC9B,IAAI,QAAQ,kBAAkB,IAAI,QAAQ,iBAAiB,EAAE;gBAC3D,MAAM,aAAa,QAAQ,kBAAkB,IAAI,QAAQ,iBAAiB;gBAC1E,IAAI,OAAO,eAAe,YAAY,eAAe,MAAM;oBACzD,MAAM,OAAO;oBACb,oBAAoB;wBAClB,MAAM,AAAC,KAAK,IAAI,IAAe;wBAC/B,MAAM,AAAC,KAAK,IAAI,IAAe;wBAC/B,UAAU,AAAC,KAAK,SAAS,IAAgB,KAAK,QAAQ,IAC1C,KAAK,IAAI,EAA8B,QAAmB;wBACtE,aAAa,AAAC,KAAK,YAAY,IAAgB,KAAK,WAAW,IAChD,KAAK,OAAO,EAA8B,QAAmB;oBAC9E;oBACA,QAAQ,GAAG,CAAC,yBAAyB;oBACrC,OAAO,gBAAgB;gBACzB;YACF;QACF;IACF;IAEA,8BAA8B;IAC9B,MAAM,qBAAqB,AAAC,aAAa,cAAc,IAC5B,AAAC,aAAa,WAAW,KAAgB,iBACzC;IAE3B,wBAAwB;IACxB,oCAAoC;IACpC,MAAM,uBAAuB,qBAAqB,QAAQ;IAC1D,MAAM,sBAAsB,qBAAqB,IAAI,eAAe,gBAAgB;IAEpF,QAAQ,GAAG,CAAC;IACZ,QAAQ,GAAG,CAAC,uBAAuB;IACnC,QAAQ,GAAG,CAAC,yBAAyB;IACrC,QAAQ,GAAG,CAAC,+BAA+B;IAC3C,QAAQ,GAAG,CAAC,sBAAsB;IAClC,QAAQ,GAAG,CAAC,8BAA8B;IAE1C,MAAM,SAA0B;QAC9B,IAAI,AAAC,aAAa,EAAE,IAAe;QACnC,UAAU;QACV;QACA;QACA;QACA,OAAO;YACL,QAAQ,CAAC;gBACP,MAAM,aAAa,aAAa,KAAK;gBACrC,IAAI,OAAO,eAAe,UAAU;oBAClC,OAAO;gBACT;gBACA,IAAI,OAAO,eAAe,YAAY,eAAe,MAAM;oBACzD,MAAM,WAAW;oBACjB,MAAM,SAAS,SAAS,MAAM;oBAC9B,IAAI,OAAO,WAAW,UAAU;wBAC9B,OAAO,WAAW,WAAW;oBAC/B;oBACA,IAAI,OAAO,WAAW,UAAU;wBAC9B,OAAO;oBACT;gBACF;gBACA,OAAO;YACT,CAAC;YACD,UAAU,AAAC,aAAa,QAAQ,IAAe;QACjD;QACA;QACA,gBAAgB;QAChB,cAAe,aAAa,aAAa;QACzC,UAAW,aAAa,SAAS;QACjC,eAAe;QACf,mBAAmB,AAAC,aAAa,mBAAmB,IAAgB;QACpE,UAAW,aAAa,SAAS;QACjC,iBAAkB,aAAa,gBAAgB;QAC/C,aAAa;QACb,cAAc;QACd,gBAAgB,AAAC,aAAa,gBAAgB,IAAgB;QAC9D,cAAc;QACd,iBAAiB,AAAC,aAAa,iBAAiB,IAAgB;QAChE,kBAAkB,AAAC,aAAa,mBAAmB,IAAgB;QACnE;IACF;IAEA,QAAQ,GAAG,CAAC;IACZ,QAAQ,GAAG,CAAC,WAAW,OAAO,KAAK;IACnC,QAAQ,GAAG,CAAC,UAAU,OAAO,YAAY,EAAE,OAAO,eAAe;IACjE,QAAQ,GAAG,CAAC,WAAW;IACvB,OAAO;AACT", "debugId": null}}, {"offset": {"line": 1064, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/AeroScout/aeroscout-frontend/src/components/common/Button.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\n\nexport interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'primary' | 'secondary' | 'tertiary' | 'link' | 'danger';\n  size?: 'sm' | 'md' | 'lg';\n  fullWidth?: boolean;\n  isLoading?: boolean;\n  icon?: React.ReactNode;\n  iconPosition?: 'left' | 'right';\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({\n    className = '',\n    variant = 'primary',\n    size = 'md',\n    fullWidth = false,\n    isLoading = false,\n    disabled = false,\n    icon = null,\n    iconPosition = 'left',\n    children,\n    ...props\n  }, ref) => {\n    // 基础样式 - 苹果风格\n    const baseStyles = \"inline-flex items-center justify-center font-medium transition-apple focus:outline-none\";\n\n    // 变体样式 - 苹果风格\n    const variantStyles = {\n      primary: \"bg-[#0071E3] text-white hover:bg-[#0077ED] active:bg-[#0051A2] rounded-full shadow-apple-sm\",\n      secondary: \"bg-[#E8E8ED] text-[#1D1D1F] hover:bg-[#D2D2D7] active:bg-[#AEAEB2] rounded-full\",\n      tertiary: \"bg-white border border-[#D2D2D7] text-[#1D1D1F] hover:bg-[#F5F5F7] rounded-full\",\n      link: \"bg-transparent text-[#0071E3] hover:underline\",\n      danger: \"bg-[#FF3B30] text-white hover:bg-[#FF453A] active:bg-[#D70015] rounded-full\",\n    };\n\n    // 尺寸样式 - 苹果风格\n    const sizeStyles = {\n      sm: \"text-[13px] px-3.5 py-1.5 h-8\",\n      md: \"text-[15px] px-4.5 py-2 h-10\",\n      lg: \"text-[17px] px-6 py-3 h-12\",\n    };\n\n    // 宽度样式\n    const widthStyles = fullWidth ? \"w-full\" : \"\";\n\n    // 禁用样式 - 苹果风格\n    const disabledStyles = (disabled || isLoading) ? \"opacity-30 cursor-not-allowed\" : \"\";\n\n    return (\n      <button\n        ref={ref}\n        className={`\n          ${baseStyles}\n          ${variantStyles[variant]}\n          ${sizeStyles[size]}\n          ${widthStyles}\n          ${disabledStyles}\n          ${className}\n        `}\n        disabled={disabled || isLoading}\n        {...props}\n      >\n        {isLoading && (\n          <svg className=\"animate-spin -ml-1 mr-2 h-4 w-4\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\n            <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n            <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n          </svg>\n        )}\n        {icon && iconPosition === 'left' && !isLoading && <span className=\"mr-2\">{icon}</span>}\n        <span className={variant === 'primary' ? 'font-medium' : ''}>{children}</span>\n        {icon && iconPosition === 'right' && <span className=\"ml-2\">{icon}</span>}\n      </button>\n    );\n  }\n);\n\nButton.displayName = \"Button\";\n\nexport default Button;\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAaA,MAAM,uBAAS,qMAAA,CAAA,UAAK,CAAC,UAAU,CAC7B,CAAC,EACC,YAAY,EAAE,EACd,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,YAAY,KAAK,EACjB,YAAY,KAAK,EACjB,WAAW,KAAK,EAChB,OAAO,IAAI,EACX,eAAe,MAAM,EACrB,QAAQ,EACR,GAAG,OACJ,EAAE;IACD,cAAc;IACd,MAAM,aAAa;IAEnB,cAAc;IACd,MAAM,gBAAgB;QACpB,SAAS;QACT,WAAW;QACX,UAAU;QACV,MAAM;QACN,QAAQ;IACV;IAEA,cAAc;IACd,MAAM,aAAa;QACjB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,OAAO;IACP,MAAM,cAAc,YAAY,WAAW;IAE3C,cAAc;IACd,MAAM,iBAAiB,AAAC,YAAY,YAAa,kCAAkC;IAEnF,qBACE,8OAAC;QACC,KAAK;QACL,WAAW,CAAC;UACV,EAAE,WAAW;UACb,EAAE,aAAa,CAAC,QAAQ,CAAC;UACzB,EAAE,UAAU,CAAC,KAAK,CAAC;UACnB,EAAE,YAAY;UACd,EAAE,eAAe;UACjB,EAAE,UAAU;QACd,CAAC;QACD,UAAU,YAAY;QACrB,GAAG,KAAK;;YAER,2BACC,8OAAC;gBAAI,WAAU;gBAAkC,OAAM;gBAA6B,MAAK;gBAAO,SAAQ;;kCACtG,8OAAC;wBAAO,WAAU;wBAAa,IAAG;wBAAK,IAAG;wBAAK,GAAE;wBAAK,QAAO;wBAAe,aAAY;;;;;;kCACxF,8OAAC;wBAAK,WAAU;wBAAa,MAAK;wBAAe,GAAE;;;;;;;;;;;;YAGtD,QAAQ,iBAAiB,UAAU,CAAC,2BAAa,8OAAC;gBAAK,WAAU;0BAAQ;;;;;;0BAC1E,8OAAC;gBAAK,WAAW,YAAY,YAAY,gBAAgB;0BAAK;;;;;;YAC7D,QAAQ,iBAAiB,yBAAW,8OAAC;gBAAK,WAAU;0BAAQ;;;;;;;;;;;;AAGnE;AAGF,OAAO,WAAW,GAAG;uCAEN", "debugId": null}}, {"offset": {"line": 1178, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/AeroScout/aeroscout-frontend/src/components/common/Input.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\n\nexport interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {\n  label?: string;\n  error?: string;\n  helperText?: string;\n  icon?: React.ReactNode;\n  iconPosition?: 'left' | 'right';\n  variant?: 'filled' | 'outlined' | 'minimal';\n}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({\n    label,\n    error,\n    helperText,\n    className = '',\n    icon,\n    iconPosition = 'right',\n    variant = 'filled',\n    ...props\n  }, ref) => {\n    // 苹果风格的输入框变体\n    const variantStyles = {\n      filled: `\n        w-full px-4 py-2.5 bg-[#F5F5F7] border-none rounded-lg text-[#1D1D1F]\n        placeholder:text-[#8E8E93]\n        focus:outline-none focus:bg-[#E8E8ED] focus:ring-0\n        transition-apple\n      `,\n      outlined: `\n        w-full px-4 py-2.5 bg-white border rounded-lg text-[#1D1D1F]\n        placeholder:text-[#8E8E93]\n        focus:outline-none focus:ring-2 focus:ring-[#0071E3] focus:border-transparent\n        transition-apple\n        ${error ? 'border-[#FF3B30] focus:ring-[#FF3B30]' : 'border-[#D2D2D7]'}\n      `,\n      minimal: `\n        w-full px-1 py-2 bg-transparent border-b text-[#1D1D1F]\n        placeholder:text-[#8E8E93]\n        focus:outline-none focus:border-b-2 focus:border-[#0071E3]\n        transition-apple\n        ${error ? 'border-[#FF3B30] focus:border-[#FF3B30]' : 'border-[#D2D2D7]'}\n      `\n    };\n\n    return (\n      <div className=\"w-full\">\n        {label && (\n          <label\n            htmlFor={props.id}\n            className=\"block text-[13px] font-medium text-[#1D1D1F] mb-1.5\"\n          >\n            {label}\n          </label>\n        )}\n\n        <div className=\"relative\">\n          <input\n            ref={ref}\n            className={`\n              ${variantStyles[variant]}\n              ${props.disabled ? 'opacity-50 cursor-not-allowed' : ''}\n              ${icon && iconPosition === 'left' ? 'pl-10' : ''}\n              ${icon && iconPosition === 'right' ? 'pr-10' : ''}\n              ${className}\n            `}\n            {...props}\n          />\n\n          {icon && (\n            <div\n              className={`absolute top-1/2 transform -translate-y-1/2 text-[#8E8E93] pointer-events-none\n                ${iconPosition === 'left' ? 'left-3' : 'right-3'}\n              `}\n            >\n              {icon}\n            </div>\n          )}\n        </div>\n\n        {error && (\n          <p className=\"mt-1.5 text-[13px] text-[#FF3B30] animate-fadeIn\">{error}</p>\n        )}\n\n        {helperText && !error && (\n          <p className=\"mt-1.5 text-[13px] text-[#8E8E93]\">{helperText}</p>\n        )}\n      </div>\n    );\n  }\n);\n\nInput.displayName = \"Input\";\n\nexport default Input;\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAaA,MAAM,sBAAQ,qMAAA,CAAA,UAAK,CAAC,UAAU,CAC5B,CAAC,EACC,KAAK,EACL,KAAK,EACL,UAAU,EACV,YAAY,EAAE,EACd,IAAI,EACJ,eAAe,OAAO,EACtB,UAAU,QAAQ,EAClB,GAAG,OACJ,EAAE;IACD,aAAa;IACb,MAAM,gBAAgB;QACpB,QAAQ,CAAC;;;;;MAKT,CAAC;QACD,UAAU,CAAC;;;;;QAKT,EAAE,QAAQ,0CAA0C,mBAAmB;MACzE,CAAC;QACD,SAAS,CAAC;;;;;QAKR,EAAE,QAAQ,4CAA4C,mBAAmB;MAC3E,CAAC;IACH;IAEA,qBACE,8OAAC;QAAI,WAAU;;YACZ,uBACC,8OAAC;gBACC,SAAS,MAAM,EAAE;gBACjB,WAAU;0BAET;;;;;;0BAIL,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,KAAK;wBACL,WAAW,CAAC;cACV,EAAE,aAAa,CAAC,QAAQ,CAAC;cACzB,EAAE,MAAM,QAAQ,GAAG,kCAAkC,GAAG;cACxD,EAAE,QAAQ,iBAAiB,SAAS,UAAU,GAAG;cACjD,EAAE,QAAQ,iBAAiB,UAAU,UAAU,GAAG;cAClD,EAAE,UAAU;YACd,CAAC;wBACA,GAAG,KAAK;;;;;;oBAGV,sBACC,8OAAC;wBACC,WAAW,CAAC;gBACV,EAAE,iBAAiB,SAAS,WAAW,UAAU;cACnD,CAAC;kCAEA;;;;;;;;;;;;YAKN,uBACC,8OAAC;gBAAE,WAAU;0BAAoD;;;;;;YAGlE,cAAc,CAAC,uBACd,8OAAC;gBAAE,WAAU;0BAAqC;;;;;;;;;;;;AAI1D;AAGF,MAAM,WAAW,GAAG;uCAEL", "debugId": null}}, {"offset": {"line": 1287, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/AeroScout/aeroscout-frontend/src/components/common/FlightSearchLoader.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useEffect, useState } from 'react';\r\n\r\ninterface FlightSearchLoaderProps {\r\n  isVisible: boolean;\r\n  searchParams?: {\r\n    origin?: string;\r\n    destination?: string;\r\n    departureDate?: string;\r\n  };\r\n}\r\n\r\nconst FlightSearchLoader: React.FC<FlightSearchLoaderProps> = ({ \r\n  isVisible, \r\n  searchParams \r\n}) => {\r\n  const [progress, setProgress] = useState(0);\r\n  const [currentStep, setCurrentStep] = useState(0);\r\n  const [elapsedTime, setElapsedTime] = useState(0);\r\n\r\n  const searchSteps = [\r\n    { text: '正在连接航班搜索引擎...', duration: 1000 },\r\n    { text: '搜索直飞航班...', duration: 2000 },\r\n    { text: '分析隐藏城市航班...', duration: 2500 },\r\n    { text: '优化价格组合...', duration: 1500 },\r\n    { text: '整理搜索结果...', duration: 1000 },\r\n  ];\r\n\r\n  useEffect(() => {\r\n    if (!isVisible) {\r\n      setProgress(0);\r\n      setCurrentStep(0);\r\n      setElapsedTime(0);\r\n      return;\r\n    }\r\n\r\n    // 进度条动画\r\n    const progressInterval = setInterval(() => {\r\n      setProgress(prev => {\r\n        if (prev >= 95) return prev; // 最多到95%，等待实际完成\r\n        return prev + Math.random() * 3 + 1;\r\n      });\r\n    }, 200);\r\n\r\n    // 步骤切换动画\r\n    const stepInterval = setInterval(() => {\r\n      setCurrentStep(prev => {\r\n        if (prev < searchSteps.length - 1) {\r\n          return prev + 1;\r\n        }\r\n        return prev;\r\n      });\r\n    }, 2000);\r\n\r\n    // 计时器\r\n    const timeInterval = setInterval(() => {\r\n      setElapsedTime(prev => prev + 1);\r\n    }, 1000);\r\n\r\n    return () => {\r\n      clearInterval(progressInterval);\r\n      clearInterval(stepInterval);\r\n      clearInterval(timeInterval);\r\n    };\r\n  }, [isVisible]);\r\n\r\n  if (!isVisible) return null;\r\n\r\n  const formatTime = (seconds: number) => {\r\n    const mins = Math.floor(seconds / 60);\r\n    const secs = seconds % 60;\r\n    return `${mins}:${secs.toString().padStart(2, '0')}`;\r\n  };\r\n\r\n  return (\r\n    <div className=\"fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center\">\r\n      <div className=\"bg-white rounded-2xl shadow-2xl p-8 max-w-md w-full mx-4 animate-fadeIn\">\r\n        {/* 头部 */}\r\n        <div className=\"text-center mb-8\">\r\n          <div className=\"w-16 h-16 mx-auto mb-4 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-full flex items-center justify-center shadow-lg\">\r\n            <svg className=\"w-8 h-8 text-white animate-bounce\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 19l9 2-9-18-9 18 9-2zm0 0v-8\" />\r\n            </svg>\r\n          </div>\r\n          <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">正在搜索航班</h3>\r\n          {searchParams && (\r\n            <p className=\"text-sm text-gray-600\">\r\n              {searchParams.origin} → {searchParams.destination}\r\n              {searchParams.departureDate && (\r\n                <span className=\"block mt-1\">{searchParams.departureDate}</span>\r\n              )}\r\n            </p>\r\n          )}\r\n        </div>\r\n\r\n        {/* 进度条 */}\r\n        <div className=\"mb-6\">\r\n          <div className=\"flex justify-between text-sm text-gray-600 mb-2\">\r\n            <span>搜索进度</span>\r\n            <span>{Math.round(progress)}%</span>\r\n          </div>\r\n          <div className=\"w-full bg-gray-200 rounded-full h-2 overflow-hidden\">\r\n            <div \r\n              className=\"h-full bg-gradient-to-r from-blue-500 to-indigo-600 rounded-full transition-all duration-300 ease-out\"\r\n              style={{ width: `${progress}%` }}\r\n            >\r\n              <div className=\"h-full bg-white/30 animate-pulse\"></div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* 当前步骤 */}\r\n        <div className=\"mb-6\">\r\n          <div className=\"flex items-center space-x-3\">\r\n            <div className=\"w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center flex-shrink-0\">\r\n              <svg className=\"w-3 h-3 text-white animate-spin\" fill=\"none\" viewBox=\"0 0 24 24\">\r\n                <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\r\n                <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\r\n              </svg>\r\n            </div>\r\n            <span className=\"text-sm font-medium text-gray-900\">\r\n              {searchSteps[currentStep]?.text || '正在处理...'}\r\n            </span>\r\n          </div>\r\n        </div>\r\n\r\n        {/* 步骤列表 */}\r\n        <div className=\"space-y-2 mb-6\">\r\n          {searchSteps.map((step, index) => (\r\n            <div key={index} className=\"flex items-center space-x-3\">\r\n              <div className={`w-4 h-4 rounded-full flex items-center justify-center ${\r\n                index < currentStep \r\n                  ? 'bg-green-500' \r\n                  : index === currentStep \r\n                    ? 'bg-blue-500 animate-pulse' \r\n                    : 'bg-gray-200'\r\n              }`}>\r\n                {index < currentStep && (\r\n                  <svg className=\"w-2.5 h-2.5 text-white\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={3} d=\"M5 13l4 4L19 7\" />\r\n                  </svg>\r\n                )}\r\n              </div>\r\n              <span className={`text-xs ${\r\n                index <= currentStep ? 'text-gray-900' : 'text-gray-400'\r\n              }`}>\r\n                {step.text}\r\n              </span>\r\n            </div>\r\n          ))}\r\n        </div>\r\n\r\n        {/* 底部信息 */}\r\n        <div className=\"flex justify-between items-center text-xs text-gray-500 pt-4 border-t border-gray-100\">\r\n          <span>搜索时间: {formatTime(elapsedTime)}</span>\r\n          <span className=\"flex items-center space-x-1\">\r\n            <div className=\"w-2 h-2 bg-green-400 rounded-full animate-pulse\"></div>\r\n            <span>AeroScout 引擎</span>\r\n          </span>\r\n        </div>\r\n\r\n        {/* 装饰性动画元素 */}\r\n        <div className=\"absolute -top-2 -right-2 w-4 h-4 bg-blue-400 rounded-full animate-ping opacity-75\"></div>\r\n        <div className=\"absolute -bottom-2 -left-2 w-3 h-3 bg-indigo-400 rounded-full animate-ping opacity-75\" style={{ animationDelay: '1s' }}></div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default FlightSearchLoader;"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAaA,MAAM,qBAAwD,CAAC,EAC7D,SAAS,EACT,YAAY,EACb;IACC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,cAAc;QAClB;YAAE,MAAM;YAAiB,UAAU;QAAK;QACxC;YAAE,MAAM;YAAa,UAAU;QAAK;QACpC;YAAE,MAAM;YAAe,UAAU;QAAK;QACtC;YAAE,MAAM;YAAa,UAAU;QAAK;QACpC;YAAE,MAAM;YAAa,UAAU;QAAK;KACrC;IAED,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,WAAW;YACd,YAAY;YACZ,eAAe;YACf,eAAe;YACf;QACF;QAEA,QAAQ;QACR,MAAM,mBAAmB,YAAY;YACnC,YAAY,CAAA;gBACV,IAAI,QAAQ,IAAI,OAAO,MAAM,gBAAgB;gBAC7C,OAAO,OAAO,KAAK,MAAM,KAAK,IAAI;YACpC;QACF,GAAG;QAEH,SAAS;QACT,MAAM,eAAe,YAAY;YAC/B,eAAe,CAAA;gBACb,IAAI,OAAO,YAAY,MAAM,GAAG,GAAG;oBACjC,OAAO,OAAO;gBAChB;gBACA,OAAO;YACT;QACF,GAAG;QAEH,MAAM;QACN,MAAM,eAAe,YAAY;YAC/B,eAAe,CAAA,OAAQ,OAAO;QAChC,GAAG;QAEH,OAAO;YACL,cAAc;YACd,cAAc;YACd,cAAc;QAChB;IACF,GAAG;QAAC;KAAU;IAEd,IAAI,CAAC,WAAW,OAAO;IAEvB,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,KAAK,KAAK,CAAC,UAAU;QAClC,MAAM,OAAO,UAAU;QACvB,OAAO,GAAG,KAAK,CAAC,EAAE,KAAK,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;IACtD;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;gCAAoC,MAAK;gCAAO,SAAQ;gCAAY,QAAO;0CACxF,cAAA,8OAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;;;;;;sCAGzE,8OAAC;4BAAG,WAAU;sCAA2C;;;;;;wBACxD,8BACC,8OAAC;4BAAE,WAAU;;gCACV,aAAa,MAAM;gCAAC;gCAAI,aAAa,WAAW;gCAChD,aAAa,aAAa,kBACzB,8OAAC;oCAAK,WAAU;8CAAc,aAAa,aAAa;;;;;;;;;;;;;;;;;;8BAOhE,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;8CAAK;;;;;;8CACN,8OAAC;;wCAAM,KAAK,KAAK,CAAC;wCAAU;;;;;;;;;;;;;sCAE9B,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,WAAU;gCACV,OAAO;oCAAE,OAAO,GAAG,SAAS,CAAC,CAAC;gCAAC;0CAE/B,cAAA,8OAAC;oCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;8BAMrB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;oCAAkC,MAAK;oCAAO,SAAQ;;sDACnE,8OAAC;4CAAO,WAAU;4CAAa,IAAG;4CAAK,IAAG;4CAAK,GAAE;4CAAK,QAAO;4CAAe,aAAY;;;;;;sDACxF,8OAAC;4CAAK,WAAU;4CAAa,MAAK;4CAAe,GAAE;;;;;;;;;;;;;;;;;0CAGvD,8OAAC;gCAAK,WAAU;0CACb,WAAW,CAAC,YAAY,EAAE,QAAQ;;;;;;;;;;;;;;;;;8BAMzC,8OAAC;oBAAI,WAAU;8BACZ,YAAY,GAAG,CAAC,CAAC,MAAM,sBACtB,8OAAC;4BAAgB,WAAU;;8CACzB,8OAAC;oCAAI,WAAW,CAAC,sDAAsD,EACrE,QAAQ,cACJ,iBACA,UAAU,cACR,8BACA,eACN;8CACC,QAAQ,6BACP,8OAAC;wCAAI,WAAU;wCAAyB,MAAK;wCAAO,SAAQ;wCAAY,QAAO;kDAC7E,cAAA,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;8CAI3E,8OAAC;oCAAK,WAAW,CAAC,QAAQ,EACxB,SAAS,cAAc,kBAAkB,iBACzC;8CACC,KAAK,IAAI;;;;;;;2BAjBJ;;;;;;;;;;8BAwBd,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;gCAAK;gCAAO,WAAW;;;;;;;sCACxB,8OAAC;4BAAK,WAAU;;8CACd,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;8CAAK;;;;;;;;;;;;;;;;;;8BAKV,8OAAC;oBAAI,WAAU;;;;;;8BACf,8OAAC;oBAAI,WAAU;oBAAwF,OAAO;wBAAE,gBAAgB;oBAAK;;;;;;;;;;;;;;;;;AAI7I;uCAEe", "debugId": null}}, {"offset": {"line": 1682, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/AeroScout/aeroscout-frontend/src/lib/simplifiedApiAdapter.ts"], "sourcesContent": ["/**\r\n * 简化航班搜索API适配器\r\n * 将简化API响应转换为前端组件所需的格式\r\n */\r\n\r\nimport {\r\n  SimplifiedFlightSearchResponse,\r\n  SimplifiedFlightItinerary,\r\n  SimplifiedFlightSegment\r\n} from '@/types/api';\r\nimport { FlightData, FlightSegment } from '@/store/flightResultsStore';\r\nimport { ApiFlightItinerary } from '@/lib/apiService';\r\n\r\n/**\r\n * 将简化API的航班段转换为前端格式\r\n */\r\nfunction adaptSimplifiedSegment(segment: SimplifiedFlightSegment): FlightSegment {\r\n  return {\r\n    id: segment.id,\r\n    departureAirportCode: segment.origin.code,\r\n    departureAirportName: segment.origin.name,\r\n    departureCityName: segment.origin.city,\r\n    arrivalAirportCode: segment.destination.code,\r\n    arrivalAirportName: segment.destination.name,\r\n    arrivalCityName: segment.destination.city,\r\n    departureTime: segment.departure.local_time,\r\n    arrivalTime: segment.arrival.local_time,\r\n    durationMinutes: segment.duration_minutes,\r\n    airlineCode: segment.carrier.code,\r\n    airlineName: segment.carrier.name,\r\n    flightNumber: segment.flight_number,\r\n    cabinClass: segment.cabin_class,\r\n    // 构建航空公司logo URL\r\n    airlineLogoUrl: `https://images.kiwi.com/airlines/64x64/${segment.carrier.code}.png`,\r\n  };\r\n}\r\n\r\n/**\r\n * 将简化API的航班行程转换为ApiFlightItinerary格式（兼容现有FlightData）\r\n */\r\nfunction adaptSimplifiedItinerary(itinerary: SimplifiedFlightItinerary): ApiFlightItinerary {\r\n  const segments = itinerary.segments.map(adaptSimplifiedSegment);\r\n  \r\n  console.log('🔄 [DEBUG] adaptSimplifiedItinerary - 输入数据:', itinerary);\r\n  console.log('🔄 [DEBUG] 顶层隐藏目的地字段:', itinerary.hidden_destination);\r\n  console.log('🔄 [DEBUG] 甩尾目的地字段:', itinerary.throwaway_destination);\r\n  console.log('🔄 [DEBUG] 隐藏目的地数组:', itinerary.hidden_destinations);\r\n  console.log('🔄 [DEBUG] 价格信息:', itinerary.price);\r\n  console.log('🔄 [DEBUG] 是否隐藏城市:', itinerary.is_hidden_city);\r\n  console.log('🔄 [DEBUG] 航班类型:', itinerary.flight_type);\r\n  console.log('🔄 [DEBUG] travel_hack信息:', itinerary.travel_hack);\r\n  \r\n  // 处理隐藏目的地信息 - 修复：从多个可能的数据源提取\r\n  let hiddenDestination;\r\n  \r\n  // 方法1：检查顶层hidden_destination字段\r\n  if (itinerary.hidden_destination) {\r\n    hiddenDestination = {\r\n      code: itinerary.hidden_destination.code,\r\n      name: itinerary.hidden_destination.name,\r\n      cityName: itinerary.hidden_destination.city,\r\n      countryName: '', // V2 API可能不提供国家信息\r\n    };\r\n    console.log('🎯 [DEBUG] 从hidden_destination找到隐藏目的地:', hiddenDestination);\r\n  }\r\n  // 方法2：检查甩尾目的地字段\r\n  else if (itinerary.throwaway_destination) {\r\n    hiddenDestination = {\r\n      code: itinerary.throwaway_destination.code,\r\n      name: itinerary.throwaway_destination.name,\r\n      cityName: itinerary.throwaway_destination.city,\r\n      countryName: '', // V2 API可能不提供国家信息\r\n    };\r\n    console.log('🎯 [DEBUG] 从throwaway_destination找到隐藏目的地:', hiddenDestination);\r\n  }\r\n  // 方法3：从hidden_destinations数组中提取（后端实际数据结构）\r\n  else if (itinerary.hidden_destinations && Array.isArray(itinerary.hidden_destinations)) {\r\n    const hiddenDests = itinerary.hidden_destinations;\r\n    if (hiddenDests.length > 0) {\r\n      const firstHidden = hiddenDests[0];\r\n      hiddenDestination = {\r\n        code: firstHidden.code || '',\r\n        name: firstHidden.name || '',\r\n        cityName: firstHidden.city || '',\r\n        countryName: firstHidden.country || '',\r\n      };\r\n      console.log('🎯 [DEBUG] 从hidden_destinations数组找到隐藏目的地:', hiddenDestination);\r\n    }\r\n  }\r\n  \r\n  if (!hiddenDestination && (itinerary.is_hidden_city || itinerary.travel_hack?.is_true_hidden_city)) {\r\n    console.warn('⚠️ [DEBUG] 标记为隐藏城市但未找到隐藏目的地信息');\r\n    console.warn('⚠️ [DEBUG] 完整itinerary对象:', JSON.stringify(itinerary, null, 2));\r\n  }\r\n  \r\n  const result = {\r\n    id: itinerary.id,\r\n    price: itinerary.price.amount, // 修复：设置price字段为数字\r\n    priceEur: itinerary.price.price_eur,\r\n    priceCurrency: itinerary.price.currency,\r\n    currency: itinerary.price.currency, // 添加currency字段\r\n    bookingToken: '', // 简化API不提供booking token\r\n    deepLink: itinerary.booking_url,\r\n    segments: segments,\r\n    totalDurationMinutes: itinerary.duration_minutes,\r\n    isSelfTransfer: itinerary.travel_hack.is_virtual_interlining,\r\n    isHiddenCity: itinerary.is_hidden_city,\r\n    dataSource: 'simplified_kiwi_graphql',\r\n    \r\n    // 计算是否为直飞\r\n    isDirectFlight: itinerary.stops_count === 0,\r\n    numberOfStops: itinerary.stops_count,\r\n    \r\n    // 隐藏城市相关信息\r\n    isThrowawayDeal: itinerary.travel_hack.is_throwaway_ticket,\r\n    isTrueHiddenCity: itinerary.travel_hack.is_true_hidden_city,\r\n    \r\n    // 添加隐藏目的地信息\r\n    hiddenDestination: hiddenDestination,\r\n    \r\n    // 航空公司信息\r\n    airlines: segments.map(seg => ({\r\n      code: seg.airlineCode,\r\n      name: seg.airlineName || seg.airlineCode,\r\n      logoUrl: seg.airlineLogoUrl,\r\n    })).filter((airline, index, self) =>\r\n      index === self.findIndex(a => a.code === airline.code)\r\n    ),\r\n  };\r\n  \r\n  console.log('✅ [DEBUG] adaptSimplifiedItinerary - 输出结果:', result);\r\n  return result;\r\n}\r\n\r\n/**\r\n * 将简化API响应转换为前端FlightData格式\r\n */\r\nexport function adaptSimplifiedFlightResponse(response: SimplifiedFlightSearchResponse): FlightData {\r\n  const directFlights = response.direct_flights.map(adaptSimplifiedItinerary);\r\n  const hiddenCityFlights = response.hidden_city_flights.map(adaptSimplifiedItinerary);\r\n  \r\n  return {\r\n    directFlights: directFlights,\r\n    comboDeals: hiddenCityFlights, // 将隐藏城市航班作为组合推荐显示\r\n    disclaimers: response.disclaimers,\r\n  };\r\n}\r\n\r\n/**\r\n * 格式化持续时间（分钟转为小时分钟格式）\r\n */\r\nexport function formatDuration(minutes: number): string {\r\n  if (!minutes || minutes <= 0) return 'N/A';\r\n  \r\n  const hours = Math.floor(minutes / 60);\r\n  const remainingMinutes = minutes % 60;\r\n  \r\n  if (hours === 0) {\r\n    return `${remainingMinutes}分钟`;\r\n  } else if (remainingMinutes === 0) {\r\n    return `${hours}小时`;\r\n  } else {\r\n    return `${hours}小时${remainingMinutes}分钟`;\r\n  }\r\n}\r\n\r\n/**\r\n * 检查航班是否为隐藏城市航班\r\n */\r\nexport function isHiddenCityFlight(itinerary: SimplifiedFlightItinerary): boolean {\r\n  return itinerary.is_hidden_city || itinerary.travel_hack.is_true_hidden_city;\r\n}\r\n\r\n/**\r\n * 检查航班是否为甩尾票\r\n */\r\nexport function isThrowawayTicket(itinerary: SimplifiedFlightItinerary): boolean {\r\n  return itinerary.travel_hack.is_throwaway_ticket;\r\n}\r\n\r\n/**\r\n * 获取航班风险等级\r\n */\r\nexport function getFlightRiskLevel(itinerary: SimplifiedFlightItinerary): 'low' | 'medium' | 'high' {\r\n  if (isHiddenCityFlight(itinerary) || isThrowawayTicket(itinerary)) {\r\n    return 'high';\r\n  }\r\n  \r\n  if (itinerary.travel_hack.is_virtual_interlining) {\r\n    return 'medium';\r\n  }\r\n  \r\n  return 'low';\r\n}"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;AAUD;;CAEC,GACD,SAAS,uBAAuB,OAAgC;IAC9D,OAAO;QACL,IAAI,QAAQ,EAAE;QACd,sBAAsB,QAAQ,MAAM,CAAC,IAAI;QACzC,sBAAsB,QAAQ,MAAM,CAAC,IAAI;QACzC,mBAAmB,QAAQ,MAAM,CAAC,IAAI;QACtC,oBAAoB,QAAQ,WAAW,CAAC,IAAI;QAC5C,oBAAoB,QAAQ,WAAW,CAAC,IAAI;QAC5C,iBAAiB,QAAQ,WAAW,CAAC,IAAI;QACzC,eAAe,QAAQ,SAAS,CAAC,UAAU;QAC3C,aAAa,QAAQ,OAAO,CAAC,UAAU;QACvC,iBAAiB,QAAQ,gBAAgB;QACzC,aAAa,QAAQ,OAAO,CAAC,IAAI;QACjC,aAAa,QAAQ,OAAO,CAAC,IAAI;QACjC,cAAc,QAAQ,aAAa;QACnC,YAAY,QAAQ,WAAW;QAC/B,iBAAiB;QACjB,gBAAgB,CAAC,uCAAuC,EAAE,QAAQ,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC;IACtF;AACF;AAEA;;CAEC,GACD,SAAS,yBAAyB,SAAoC;IACpE,MAAM,WAAW,UAAU,QAAQ,CAAC,GAAG,CAAC;IAExC,QAAQ,GAAG,CAAC,+CAA+C;IAC3D,QAAQ,GAAG,CAAC,yBAAyB,UAAU,kBAAkB;IACjE,QAAQ,GAAG,CAAC,uBAAuB,UAAU,qBAAqB;IAClE,QAAQ,GAAG,CAAC,uBAAuB,UAAU,mBAAmB;IAChE,QAAQ,GAAG,CAAC,oBAAoB,UAAU,KAAK;IAC/C,QAAQ,GAAG,CAAC,sBAAsB,UAAU,cAAc;IAC1D,QAAQ,GAAG,CAAC,oBAAoB,UAAU,WAAW;IACrD,QAAQ,GAAG,CAAC,6BAA6B,UAAU,WAAW;IAE9D,6BAA6B;IAC7B,IAAI;IAEJ,+BAA+B;IAC/B,IAAI,UAAU,kBAAkB,EAAE;QAChC,oBAAoB;YAClB,MAAM,UAAU,kBAAkB,CAAC,IAAI;YACvC,MAAM,UAAU,kBAAkB,CAAC,IAAI;YACvC,UAAU,UAAU,kBAAkB,CAAC,IAAI;YAC3C,aAAa;QACf;QACA,QAAQ,GAAG,CAAC,0CAA0C;IACxD,OAEK,IAAI,UAAU,qBAAqB,EAAE;QACxC,oBAAoB;YAClB,MAAM,UAAU,qBAAqB,CAAC,IAAI;YAC1C,MAAM,UAAU,qBAAqB,CAAC,IAAI;YAC1C,UAAU,UAAU,qBAAqB,CAAC,IAAI;YAC9C,aAAa;QACf;QACA,QAAQ,GAAG,CAAC,6CAA6C;IAC3D,OAEK,IAAI,UAAU,mBAAmB,IAAI,MAAM,OAAO,CAAC,UAAU,mBAAmB,GAAG;QACtF,MAAM,cAAc,UAAU,mBAAmB;QACjD,IAAI,YAAY,MAAM,GAAG,GAAG;YAC1B,MAAM,cAAc,WAAW,CAAC,EAAE;YAClC,oBAAoB;gBAClB,MAAM,YAAY,IAAI,IAAI;gBAC1B,MAAM,YAAY,IAAI,IAAI;gBAC1B,UAAU,YAAY,IAAI,IAAI;gBAC9B,aAAa,YAAY,OAAO,IAAI;YACtC;YACA,QAAQ,GAAG,CAAC,6CAA6C;QAC3D;IACF;IAEA,IAAI,CAAC,qBAAqB,CAAC,UAAU,cAAc,IAAI,UAAU,WAAW,EAAE,mBAAmB,GAAG;QAClG,QAAQ,IAAI,CAAC;QACb,QAAQ,IAAI,CAAC,6BAA6B,KAAK,SAAS,CAAC,WAAW,MAAM;IAC5E;IAEA,MAAM,SAAS;QACb,IAAI,UAAU,EAAE;QAChB,OAAO,UAAU,KAAK,CAAC,MAAM;QAC7B,UAAU,UAAU,KAAK,CAAC,SAAS;QACnC,eAAe,UAAU,KAAK,CAAC,QAAQ;QACvC,UAAU,UAAU,KAAK,CAAC,QAAQ;QAClC,cAAc;QACd,UAAU,UAAU,WAAW;QAC/B,UAAU;QACV,sBAAsB,UAAU,gBAAgB;QAChD,gBAAgB,UAAU,WAAW,CAAC,sBAAsB;QAC5D,cAAc,UAAU,cAAc;QACtC,YAAY;QAEZ,UAAU;QACV,gBAAgB,UAAU,WAAW,KAAK;QAC1C,eAAe,UAAU,WAAW;QAEpC,WAAW;QACX,iBAAiB,UAAU,WAAW,CAAC,mBAAmB;QAC1D,kBAAkB,UAAU,WAAW,CAAC,mBAAmB;QAE3D,YAAY;QACZ,mBAAmB;QAEnB,SAAS;QACT,UAAU,SAAS,GAAG,CAAC,CAAA,MAAO,CAAC;gBAC7B,MAAM,IAAI,WAAW;gBACrB,MAAM,IAAI,WAAW,IAAI,IAAI,WAAW;gBACxC,SAAS,IAAI,cAAc;YAC7B,CAAC,GAAG,MAAM,CAAC,CAAC,SAAS,OAAO,OAC1B,UAAU,KAAK,SAAS,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,QAAQ,IAAI;IAEzD;IAEA,QAAQ,GAAG,CAAC,8CAA8C;IAC1D,OAAO;AACT;AAKO,SAAS,8BAA8B,QAAwC;IACpF,MAAM,gBAAgB,SAAS,cAAc,CAAC,GAAG,CAAC;IAClD,MAAM,oBAAoB,SAAS,mBAAmB,CAAC,GAAG,CAAC;IAE3D,OAAO;QACL,eAAe;QACf,YAAY;QACZ,aAAa,SAAS,WAAW;IACnC;AACF;AAKO,SAAS,eAAe,OAAe;IAC5C,IAAI,CAAC,WAAW,WAAW,GAAG,OAAO;IAErC,MAAM,QAAQ,KAAK,KAAK,CAAC,UAAU;IACnC,MAAM,mBAAmB,UAAU;IAEnC,IAAI,UAAU,GAAG;QACf,OAAO,GAAG,iBAAiB,EAAE,CAAC;IAChC,OAAO,IAAI,qBAAqB,GAAG;QACjC,OAAO,GAAG,MAAM,EAAE,CAAC;IACrB,OAAO;QACL,OAAO,GAAG,MAAM,EAAE,EAAE,iBAAiB,EAAE,CAAC;IAC1C;AACF;AAKO,SAAS,mBAAmB,SAAoC;IACrE,OAAO,UAAU,cAAc,IAAI,UAAU,WAAW,CAAC,mBAAmB;AAC9E;AAKO,SAAS,kBAAkB,SAAoC;IACpE,OAAO,UAAU,WAAW,CAAC,mBAAmB;AAClD;AAKO,SAAS,mBAAmB,SAAoC;IACrE,IAAI,mBAAmB,cAAc,kBAAkB,YAAY;QACjE,OAAO;IACT;IAEA,IAAI,UAAU,WAAW,CAAC,sBAAsB,EAAE;QAChD,OAAO;IACT;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 1835, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/AeroScout/aeroscout-frontend/src/components/flight/FlightSearchForm.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { use<PERSON><PERSON>, Controller, SubmitHandler } from 'react-hook-form';\nimport { useRouter } from 'next/navigation';\nimport AirportSelector from '@/components/airport/AirportSelector';\nimport { searchSimplifiedFlights, SimplifiedFlightSearchRequest } from '@/lib/apiService';\nimport { useFlightResultsStore, FlightData } from '@/store/flightResultsStore';\n// 导入自定义组件\nimport Button from '@/components/common/Button';\nimport Input from '@/components/common/Input';\nimport FlightSearchLoader from '@/components/common/FlightSearchLoader';\nimport { adaptSimplifiedFlightResponse } from '@/lib/simplifiedApiAdapter';\n\n// 定义简化的表单数据类型\ninterface SimplifiedFlightSearchFormData {\n  originIata: string;\n  destinationIata: string;\n  departureDate: string;\n  returnDate?: string;\n  tripType: 'one-way' | 'round-trip';\n  adults: number;\n  children: number;\n  infants: number;\n  cabinClass: 'ECONOMY' | 'PREMIUM_ECONOMY' | 'BUSINESS' | 'FIRST';\n  directFlightsOnly: boolean;\n  enableHubProbe: boolean;\n}\n\n// 定义机场信息接口（兼容AirportSelector）\ninterface AirportInfo {\n  code: string;\n  name: string;\n  city: string;\n  country: string;\n  type: string; // 必需字段以兼容apiService的AirportInfo\n}\n\nconst FlightSearchForm: React.FC = () => {\n  // 获取当前日期和一年后的日期，用于日期选择器的限制\n  const today = new Date().toISOString().split('T')[0];\n  const oneYearLater = new Date();\n  oneYearLater.setFullYear(oneYearLater.getFullYear() + 1);\n  const maxDate = oneYearLater.toISOString().split('T')[0];\n\n  // 使用react-hook-form管理表单状态\n  const {\n    control,\n    handleSubmit,\n    setValue,\n    watch,\n    clearErrors,\n    formState: { errors },\n  } = useForm<SimplifiedFlightSearchFormData>({\n    defaultValues: {\n      originIata: '',\n      destinationIata: '',\n      departureDate: today,\n      returnDate: '',\n      tripType: 'one-way',\n      adults: 1,\n      children: 0,\n      infants: 0,\n      cabinClass: 'ECONOMY',\n      directFlightsOnly: false,\n      enableHubProbe: true,\n    },\n  });\n\n  const [showAdvancedOptions, setShowAdvancedOptions] = useState(false);\n  const tripType = watch('tripType');\n  const departureDate = watch('departureDate');\n  const originIata = watch('originIata');\n  const destinationIata = watch('destinationIata');\n\n  // 从 Zustand store 获取状态和 actions\n  const {\n    searchStatus,\n    setSearchSuccess,\n    setSearchError,\n    setSearchLoading,\n  } = useFlightResultsStore();\n\n  const router = useRouter();\n  const [formSubmitError, setFormSubmitError] = useState<string | null>(null);\n\n  // 使用useState来存储完整的机场信息对象\n  const [selectedOriginAirportInfo, setSelectedOriginAirportInfo] = useState<AirportInfo | null>(null);\n  const [selectedDestinationAirportInfo, setSelectedDestinationAirportInfo] = useState<AirportInfo | null>(null);\n\n  // 监听表单渲染，记录状态变化，用于调试\n  useEffect(() => {\n    console.log(\"FlightSearchForm RENDER: originIata =\", originIata, \"selectedOriginAirportInfo =\", selectedOriginAirportInfo);\n    console.log(\"FlightSearchForm RENDER: destinationIata =\", destinationIata, \"selectedDestinationAirportInfo =\", selectedDestinationAirportInfo);\n  });\n\n  // 出发地机场选择处理函数\n  const handleOriginAirportSelected = useCallback((airport: AirportInfo | null) => {\n    console.log('[FlightSearchForm] handleOriginAirportSelected - airport:', airport);\n    console.log(\"出发地机场选择:\", airport);\n\n    setSelectedOriginAirportInfo(airport);\n\n    if (airport && airport.code) {\n      console.log(\"设置出发地IATA:\", airport.code);\n      setValue('originIata', airport.code, {\n        shouldValidate: true,\n        shouldDirty: true,\n        shouldTouch: true\n      });\n      clearErrors('originIata');\n    } else {\n      console.log(\"清空出发地IATA\");\n      setValue('originIata', '', { shouldValidate: true });\n    }\n  }, [setValue, clearErrors]);\n\n  // 目的地机场选择处理函数\n  const handleDestinationAirportSelected = useCallback((airport: AirportInfo | null) => {\n    console.log('[FlightSearchForm] handleDestinationAirportSelected - airport:', airport);\n    console.log(\"目的地机场选择:\", airport);\n\n    setSelectedDestinationAirportInfo(airport);\n\n    if (airport && airport.code) {\n      console.log(\"设置目的地IATA:\", airport.code);\n      setValue('destinationIata', airport.code, {\n        shouldValidate: true,\n        shouldDirty: true,\n        shouldTouch: true\n      });\n      clearErrors('destinationIata');\n    } else {\n      console.log(\"清空目的地IATA\");\n      setValue('destinationIata', '', { shouldValidate: true });\n    }\n  }, [setValue, clearErrors]);\n\n  const onSubmit: SubmitHandler<SimplifiedFlightSearchFormData> = async (data) => {\n    console.log('🚀 [DEBUG] 开始搜索 - 立即设置loading状态');\n    setFormSubmitError(null);\n\n    // 立即设置loading状态，确保加载动画立即显示\n    setSearchLoading();\n\n    // 映射 cabinClass 到 GraphQL API 期望的值\n    let apiCabinClass = data.cabinClass;\n    switch (data.cabinClass) {\n      case 'BUSINESS':\n        apiCabinClass = 'BUSINESS';  // API期望BUSINESS而不是BUSINESS_CLASS\n        break;\n      case 'FIRST':\n        apiCabinClass = 'FIRST';  // API期望FIRST而不是FIRST_CLASS\n        break;\n      // ECONOMY 和 PREMIUM_ECONOMY 通常保持不变，但需根据API确认\n      // case 'PREMIUM_ECONOMY':\n      //   apiCabinClass = 'PREMIUM_ECONOMY'; // 假设API使用此值\n      //   break;\n      default:\n        apiCabinClass = data.cabinClass; // 保持原样或默认为 ECONOMY\n    }\n    console.log(`[FlightSearchForm] Mapped cabinClass from ${data.cabinClass} to ${apiCabinClass}`);\n\n    const payload: SimplifiedFlightSearchRequest = {\n      origin_iata: data.originIata,\n      destination_iata: data.destinationIata,\n      departure_date_from: data.departureDate,\n      departure_date_to: data.departureDate,\n      return_date_from: data.returnDate || undefined,\n      return_date_to: data.returnDate || undefined,\n      adults: data.adults,\n      cabin_class: apiCabinClass, // 使用映射后的值\n      preferred_currency: 'CNY',\n      max_results_per_type: 10,\n      max_pages_per_search: 1,\n      direct_flights_only_for_primary: data.directFlightsOnly,\n    };\n\n    try {\n      console.log('🚀 提交简化航班搜索请求:', payload);\n      console.log('🔄 [DEBUG] 当前搜索状态:', searchStatus);\n\n      // 调用简化航班搜索API\n      const response = await searchSimplifiedFlights(\n        payload,\n        true, // 包含直飞航班\n        data.enableHubProbe // 根据用户设置决定是否包含隐藏城市航班\n      );\n\n      console.log('✅ 简化API响应:', response);\n      console.log('🔍 [DEBUG] 隐藏城市航班原始数据:', response.hidden_city_flights);\n\n      if (response) {\n        // 使用适配器转换响应数据\n        const flightData: FlightData = adaptSimplifiedFlightResponse(response);\n\n        console.log('=== 🎯 简化航班搜索成功 ===');\n        console.log('📋 原始响应:', response);\n        console.log('✈️ 处理后数据:', flightData);\n        console.log('🔢 直飞航班数量:', flightData.directFlights?.length || 0);\n        console.log('🔢 隐藏城市航班数量:', flightData.comboDeals?.length || 0);\n        console.log('📝 免责声明数量:', flightData.disclaimers?.length || 0);\n        console.log('⏱️ 搜索耗时:', response.search_time_ms, 'ms');\n\n        // 详细检查隐藏城市航班数据\n        if (response.hidden_city_flights && response.hidden_city_flights.length > 0) {\n          console.log('🎯 [DEBUG] 第一个隐藏城市航班详情:');\n          console.log('- ID:', response.hidden_city_flights[0].id);\n          console.log('- 价格:', response.hidden_city_flights[0].price);\n          console.log('- 隐藏目的地:', response.hidden_city_flights[0].hidden_destination);\n          console.log('- 是否隐藏城市:', response.hidden_city_flights[0].is_hidden_city);\n        }\n\n        // 检查适配后的数据\n        if (flightData.comboDeals && flightData.comboDeals.length > 0) {\n          console.log('🎯 [DEBUG] 适配后第一个组合航班详情:');\n          console.log('- ID:', flightData.comboDeals[0].id);\n          console.log('- 价格:', flightData.comboDeals[0].price);\n          console.log('- 隐藏目的地:', flightData.comboDeals[0].hiddenDestination);\n          console.log('- 是否隐藏城市:', flightData.comboDeals[0].isHiddenCity);\n        }\n\n        setSearchSuccess(flightData, true);\n\n        console.log('🧭 导航到结果页面:', flightData);\n        router.push('/search/results');\n      } else {\n        setFormSubmitError('未能获取搜索结果，响应格式不正确。');\n        setSearchError('未能获取搜索结果，响应格式不正确。');\n      }\n    } catch (error) {\n      console.error('❌ 简化航班搜索错误:', error);\n      console.log('🔄 [DEBUG] 错误后的搜索状态:', searchStatus);\n      setFormSubmitError(`搜索失败: ${error instanceof Error ? error.message : '未知错误'}`);\n      setSearchError(`搜索失败: ${error instanceof Error ? error.message : '未知错误'}`);\n    }\n  };\n\n  // 记录组件挂载和卸载\n  useEffect(() => {\n    console.log('🚀 FlightSearchForm组件挂载 (简化搜索版本)');\n\n    return () => {\n      console.log('👋 FlightSearchForm组件卸载');\n    };\n  }, []);\n\n  return (\n    <div className=\"w-full bg-white rounded-lg shadow-lg overflow-visible animate-fadeIn\">\n      {/* 顶部简化搜索提示 */}\n      <div className=\"bg-gradient-to-r from-blue-50 to-indigo-50 px-6 py-3\">\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center space-x-2\">\n            <span className=\"text-blue-600 text-sm font-medium\">\n              🚀 AeroScout 简化搜索\n            </span>\n            <span className=\"bg-green-100 text-green-700 text-xs px-2 py-1 rounded-full\">\n              直飞 + 隐藏城市\n            </span>\n          </div>\n          <div className=\"text-xs text-blue-600\">\n            快速搜索 • 智能推荐 • 风险提示\n          </div>\n        </div>\n      </div>\n\n      {/* 单程/往返选择 */}\n      <div className=\"bg-gradient-to-r from-orange-50 to-white px-6 py-3\">\n        <div className=\"flex space-x-4\">\n          <Controller\n            name=\"tripType\"\n            control={control}\n            render={({ field }) => (\n              <div className=\"flex space-x-8\">\n                <label className=\"flex items-center cursor-pointer\">\n                  <input\n                    type=\"radio\"\n                    className=\"sr-only\"\n                    checked={field.value === 'one-way'}\n                    onChange={() => field.onChange('one-way')}\n                  />\n                  <div className={`text-sm font-medium pb-2 border-b-2 transition-all ${field.value === 'one-way' ? 'text-orange-500 border-orange-500' : 'text-gray-400 border-transparent'}`}>\n                    单程\n                  </div>\n                </label>\n                <label className=\"flex items-center cursor-pointer\">\n                  <input\n                    type=\"radio\"\n                    className=\"sr-only\"\n                    checked={field.value === 'round-trip'}\n                    onChange={() => field.onChange('round-trip')}\n                  />\n                  <div className={`text-sm font-medium pb-2 border-b-2 transition-all ${field.value === 'round-trip' ? 'text-orange-500 border-orange-500' : 'text-gray-400 border-transparent'}`}>\n                    往返\n                  </div>\n                </label>\n              </div>\n            )}\n          />\n        </div>\n      </div>\n\n      {/* 主搜索表单 */}\n      <form onSubmit={handleSubmit(onSubmit)} className=\"p-6 overflow-visible\">\n        {formSubmitError && (\n          <div className=\"mb-4 p-3 bg-red-50 border border-red-200 rounded-lg text-red-700 text-sm\">\n            <strong>错误：</strong> {formSubmitError}\n          </div>\n        )}\n\n        <div className=\"flex flex-col lg:flex-row lg:items-end lg:space-x-4 overflow-visible\">\n          {/* 出发地/目的地 */}\n          <div className=\"flex-1 mb-4 lg:mb-0 overflow-visible\">\n            <Controller\n              name=\"originIata\"\n              control={control}\n              rules={{ required: '出发地不能为空' }}\n              render={({ fieldState }) => (\n                <AirportSelector\n                  label=\"出发地\"\n                  placeholder=\"搜索出发机场或城市...\"\n                  value={selectedOriginAirportInfo}\n                  onAirportSelected={handleOriginAirportSelected}\n                  error={fieldState.error?.message}\n                />\n              )}\n            />\n          </div>\n\n          <div className=\"hidden lg:block flex-none\">\n            <div className=\"w-8 h-8 flex items-center justify-center mb-1.5\">\n              <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\" className=\"w-5 h-5 text-gray-400\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M14 5l7 7m0 0l-7 7m7-7H3\" />\n              </svg>\n            </div>\n          </div>\n\n          <div className=\"flex-1 mb-4 lg:mb-0 overflow-visible\">\n            <Controller\n              name=\"destinationIata\"\n              control={control}\n              rules={{ required: '目的地不能为空' }}\n              render={({ fieldState }) => (\n                <AirportSelector\n                  label=\"目的地\"\n                  placeholder=\"搜索目的地机场或城市...\"\n                  value={selectedDestinationAirportInfo}\n                  onAirportSelected={handleDestinationAirportSelected}\n                  error={fieldState.error?.message}\n                />\n              )}\n            />\n          </div>\n\n          {/* 日期选择 */}\n          <div className=\"flex-1 mb-4 lg:mb-0\">\n            <Controller\n              name=\"departureDate\"\n              control={control}\n              render={({ field }) => (\n                <Input\n                  type=\"date\"\n                  id=\"departureDate\"\n                  label=\"出发日期\"\n                  variant=\"filled\"\n                  min={today}\n                  max={maxDate}\n                  {...field}\n                  error={errors.departureDate?.message}\n                  className=\"rounded-lg\"\n                  onChange={(e) => {\n                    field.onChange(e);\n                    const returnDate = watch('returnDate');\n                    if (tripType === 'round-trip' && returnDate && new Date(e.target.value) > new Date(returnDate)) {\n                      setValue('returnDate', e.target.value, { shouldValidate: true });\n                    }\n                  }}\n                />\n              )}\n            />\n          </div>\n\n          {tripType === 'round-trip' && (\n            <div className=\"flex-1 mb-4 lg:mb-0 animate-fadeIn\">\n              <Controller\n                name=\"returnDate\"\n                control={control}\n                render={({ field }) => (\n                  <Input\n                    type=\"date\"\n                    id=\"returnDate\"\n                    label=\"返程日期\"\n                    variant=\"filled\"\n                    min={departureDate || today}\n                    max={maxDate}\n                    {...field}\n                    error={errors.returnDate?.message}\n                    className=\"rounded-lg\"\n                  />\n                )}\n              />\n            </div>\n          )}\n\n          {/* 乘客数量选择 */}\n          <div className=\"flex-1 mb-4 lg:mb-0\">\n            <Controller\n              name=\"adults\"\n              control={control}\n              render={({ field }) => (\n                <div>\n                  <label htmlFor=\"adults\" className=\"block text-sm font-medium text-gray-700 mb-1.5\">\n                    乘客\n                  </label>\n                  <div className=\"flex items-center px-4 py-2.5 bg-gray-50 rounded-lg\">\n                    <button\n                      type=\"button\"\n                      onClick={() => field.value > 1 && field.onChange(field.value - 1)}\n                      className=\"w-8 h-8 flex items-center justify-center text-gray-600 hover:bg-gray-200 rounded-full transition-colors\"\n                      disabled={field.value <= 1}\n                      aria-label=\"减少乘客数量\"\n                    >\n                      <svg className=\"w-5 h-5\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M20 12H4\" />\n                      </svg>\n                    </button>\n                    <span className=\"flex-1 text-center text-sm text-gray-900 font-medium mx-2\">\n                      {field.value}位成人\n                    </span>\n                    <button\n                      type=\"button\"\n                      onClick={() => field.value < 9 && field.onChange(field.value + 1)}\n                      className=\"w-8 h-8 flex items-center justify-center text-gray-600 hover:bg-gray-200 rounded-full transition-colors\"\n                      disabled={field.value >= 9}\n                      aria-label=\"增加乘客数量\"\n                    >\n                      <svg className=\"w-5 h-5\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 4v16m8-8H4\" />\n                      </svg>\n                    </button>\n                    <input\n                      type=\"hidden\"\n                      id=\"adults\"\n                      {...field}\n                    />\n                  </div>\n                  {errors.adults && <p className=\"text-red-500 text-xs mt-1\">{errors.adults.message}</p>}\n                </div>\n              )}\n            />\n          </div>\n\n          {/* 搜索按钮 */}\n          <div className=\"flex-none\">\n            <Button\n              type=\"submit\"\n              disabled={searchStatus === 'loading'}\n              isLoading={searchStatus === 'loading'}\n              size=\"lg\"\n              className=\"w-full lg:w-auto mt-6 lg:mt-0 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg px-8 shadow-lg\"\n            >\n              {searchStatus === 'loading' ? '🔍 搜索中...' : '🚀 开始搜索'}\n            </Button>\n          </div>\n        </div>\n\n        {/* 高级选项 */}\n        <div className=\"mt-6 border-t border-gray-200 pt-4\">\n          <div className=\"flex justify-between items-center\">\n            <div className=\"flex space-x-6\">\n              <Controller\n                name=\"cabinClass\"\n                control={control}\n                render={({ field }) => (\n                  <div className=\"flex items-center space-x-2\">\n                    <label htmlFor=\"cabinClass\" className=\"text-sm font-medium text-gray-700\">\n                      舱位等级\n                    </label>\n                    <select\n                      id=\"cabinClass\"\n                      {...field}\n                      className=\"text-sm border border-gray-300 rounded-md px-3 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    >\n                      <option value=\"ECONOMY\">经济舱</option>\n                      <option value=\"PREMIUM_ECONOMY\">超级经济舱</option>\n                      <option value=\"BUSINESS\">商务舱</option>\n                      <option value=\"FIRST\">头等舱</option>\n                    </select>\n                  </div>\n                )}\n              />\n              <label className=\"flex items-center space-x-2 cursor-pointer\">\n                <Controller\n                  name=\"directFlightsOnly\"\n                  control={control}\n                  render={({ field: { onChange, value } }) => (\n                    <input\n                      type=\"checkbox\"\n                      checked={value}\n                      onChange={onChange}\n                      className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n                    />\n                  )}\n                />\n                <span className=\"text-sm text-gray-700\">仅直飞</span>\n              </label>\n            </div>\n            <Button\n              type=\"button\"\n              variant=\"link\"\n              size=\"sm\"\n              onClick={() => setShowAdvancedOptions(!showAdvancedOptions)}\n              className=\"text-blue-600 hover:text-blue-700\"\n            >\n              {showAdvancedOptions ? '收起选项' : '更多选项'}\n            </Button>\n          </div>\n\n          {showAdvancedOptions && (\n            <div className=\"mt-4 p-4 bg-blue-50 rounded-lg animate-fadeIn\">\n              <label className=\"flex items-center space-x-3 cursor-pointer\">\n                <Controller\n                  name=\"enableHubProbe\"\n                  control={control}\n                  render={({ field: { onChange, value } }) => (\n                    <input\n                      type=\"checkbox\"\n                      checked={value}\n                      onChange={onChange}\n                      className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n                    />\n                  )}\n                />\n                <div>\n                  <span className=\"text-sm font-medium text-gray-900\">启用隐藏城市航班搜索</span>\n                  <p className=\"text-xs text-gray-600 mt-1\">\n                    搜索可能更便宜的隐藏城市航班（甩尾票），但存在一定风险\n                  </p>\n                </div>\n              </label>\n            </div>\n          )}\n        </div>\n\n        {/* 搜索状态指示器 */}\n        {searchStatus === 'loading' && (\n          <div className=\"mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200 animate-fadeIn\">\n            <div className=\"flex items-center\">\n              <div className=\"w-10 h-10 mr-4 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full flex items-center justify-center shadow-sm\">\n                <svg className=\"animate-spin w-5 h-5 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\n                  <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n                  <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                </svg>\n              </div>\n              <div>\n                <p className=\"text-blue-800 font-medium\">正在搜索航班...</p>\n                <p className=\"text-blue-600 text-sm\">使用简化搜索引擎，为您快速查找最优航班</p>\n              </div>\n            </div>\n          </div>\n        )}\n      </form>\n\n      {/* 全屏加载遮罩 */}\n      <FlightSearchLoader\n        isVisible={searchStatus === 'loading'}\n        searchParams={{\n          origin: selectedOriginAirportInfo?.name || originIata,\n          destination: selectedDestinationAirportInfo?.name || destinationIata,\n          departureDate: departureDate,\n        }}\n      />\n    </div>\n  );\n};\n\nexport default FlightSearchForm;"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AAZA;;;;;;;;;;;;AAsCA,MAAM,mBAA6B;IACjC,2BAA2B;IAC3B,MAAM,QAAQ,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;IACpD,MAAM,eAAe,IAAI;IACzB,aAAa,WAAW,CAAC,aAAa,WAAW,KAAK;IACtD,MAAM,UAAU,aAAa,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;IAExD,0BAA0B;IAC1B,MAAM,EACJ,OAAO,EACP,YAAY,EACZ,QAAQ,EACR,KAAK,EACL,WAAW,EACX,WAAW,EAAE,MAAM,EAAE,EACtB,GAAG,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAkC;QAC1C,eAAe;YACb,YAAY;YACZ,iBAAiB;YACjB,eAAe;YACf,YAAY;YACZ,UAAU;YACV,QAAQ;YACR,UAAU;YACV,SAAS;YACT,YAAY;YACZ,mBAAmB;YACnB,gBAAgB;QAClB;IACF;IAEA,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,WAAW,MAAM;IACvB,MAAM,gBAAgB,MAAM;IAC5B,MAAM,aAAa,MAAM;IACzB,MAAM,kBAAkB,MAAM;IAE9B,gCAAgC;IAChC,MAAM,EACJ,YAAY,EACZ,gBAAgB,EAChB,cAAc,EACd,gBAAgB,EACjB,GAAG,CAAA,GAAA,kIAAA,CAAA,wBAAqB,AAAD;IAExB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAEtE,yBAAyB;IACzB,MAAM,CAAC,2BAA2B,6BAA6B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB;IAC/F,MAAM,CAAC,gCAAgC,kCAAkC,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB;IAEzG,qBAAqB;IACrB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,QAAQ,GAAG,CAAC,yCAAyC,YAAY,+BAA+B;QAChG,QAAQ,GAAG,CAAC,8CAA8C,iBAAiB,oCAAoC;IACjH;IAEA,cAAc;IACd,MAAM,8BAA8B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC/C,QAAQ,GAAG,CAAC,6DAA6D;QACzE,QAAQ,GAAG,CAAC,YAAY;QAExB,6BAA6B;QAE7B,IAAI,WAAW,QAAQ,IAAI,EAAE;YAC3B,QAAQ,GAAG,CAAC,cAAc,QAAQ,IAAI;YACtC,SAAS,cAAc,QAAQ,IAAI,EAAE;gBACnC,gBAAgB;gBAChB,aAAa;gBACb,aAAa;YACf;YACA,YAAY;QACd,OAAO;YACL,QAAQ,GAAG,CAAC;YACZ,SAAS,cAAc,IAAI;gBAAE,gBAAgB;YAAK;QACpD;IACF,GAAG;QAAC;QAAU;KAAY;IAE1B,cAAc;IACd,MAAM,mCAAmC,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACpD,QAAQ,GAAG,CAAC,kEAAkE;QAC9E,QAAQ,GAAG,CAAC,YAAY;QAExB,kCAAkC;QAElC,IAAI,WAAW,QAAQ,IAAI,EAAE;YAC3B,QAAQ,GAAG,CAAC,cAAc,QAAQ,IAAI;YACtC,SAAS,mBAAmB,QAAQ,IAAI,EAAE;gBACxC,gBAAgB;gBAChB,aAAa;gBACb,aAAa;YACf;YACA,YAAY;QACd,OAAO;YACL,QAAQ,GAAG,CAAC;YACZ,SAAS,mBAAmB,IAAI;gBAAE,gBAAgB;YAAK;QACzD;IACF,GAAG;QAAC;QAAU;KAAY;IAE1B,MAAM,WAA0D,OAAO;QACrE,QAAQ,GAAG,CAAC;QACZ,mBAAmB;QAEnB,2BAA2B;QAC3B;QAEA,mCAAmC;QACnC,IAAI,gBAAgB,KAAK,UAAU;QACnC,OAAQ,KAAK,UAAU;YACrB,KAAK;gBACH,gBAAgB,YAAa,iCAAiC;gBAC9D;YACF,KAAK;gBACH,gBAAgB,SAAU,2BAA2B;gBACrD;YACF,6CAA6C;YAC7C,0BAA0B;YAC1B,oDAAoD;YACpD,WAAW;YACX;gBACE,gBAAgB,KAAK,UAAU,EAAE,mBAAmB;QACxD;QACA,QAAQ,GAAG,CAAC,CAAC,0CAA0C,EAAE,KAAK,UAAU,CAAC,IAAI,EAAE,eAAe;QAE9F,MAAM,UAAyC;YAC7C,aAAa,KAAK,UAAU;YAC5B,kBAAkB,KAAK,eAAe;YACtC,qBAAqB,KAAK,aAAa;YACvC,mBAAmB,KAAK,aAAa;YACrC,kBAAkB,KAAK,UAAU,IAAI;YACrC,gBAAgB,KAAK,UAAU,IAAI;YACnC,QAAQ,KAAK,MAAM;YACnB,aAAa;YACb,oBAAoB;YACpB,sBAAsB;YACtB,sBAAsB;YACtB,iCAAiC,KAAK,iBAAiB;QACzD;QAEA,IAAI;YACF,QAAQ,GAAG,CAAC,kBAAkB;YAC9B,QAAQ,GAAG,CAAC,sBAAsB;YAElC,cAAc;YACd,MAAM,WAAW,MAAM,CAAA,GAAA,wHAAA,CAAA,0BAAuB,AAAD,EAC3C,SACA,MACA,KAAK,cAAc,CAAC,qBAAqB;;YAG3C,QAAQ,GAAG,CAAC,cAAc;YAC1B,QAAQ,GAAG,CAAC,0BAA0B,SAAS,mBAAmB;YAElE,IAAI,UAAU;gBACZ,cAAc;gBACd,MAAM,aAAyB,CAAA,GAAA,kIAAA,CAAA,gCAA6B,AAAD,EAAE;gBAE7D,QAAQ,GAAG,CAAC;gBACZ,QAAQ,GAAG,CAAC,YAAY;gBACxB,QAAQ,GAAG,CAAC,aAAa;gBACzB,QAAQ,GAAG,CAAC,cAAc,WAAW,aAAa,EAAE,UAAU;gBAC9D,QAAQ,GAAG,CAAC,gBAAgB,WAAW,UAAU,EAAE,UAAU;gBAC7D,QAAQ,GAAG,CAAC,cAAc,WAAW,WAAW,EAAE,UAAU;gBAC5D,QAAQ,GAAG,CAAC,YAAY,SAAS,cAAc,EAAE;gBAEjD,eAAe;gBACf,IAAI,SAAS,mBAAmB,IAAI,SAAS,mBAAmB,CAAC,MAAM,GAAG,GAAG;oBAC3E,QAAQ,GAAG,CAAC;oBACZ,QAAQ,GAAG,CAAC,SAAS,SAAS,mBAAmB,CAAC,EAAE,CAAC,EAAE;oBACvD,QAAQ,GAAG,CAAC,SAAS,SAAS,mBAAmB,CAAC,EAAE,CAAC,KAAK;oBAC1D,QAAQ,GAAG,CAAC,YAAY,SAAS,mBAAmB,CAAC,EAAE,CAAC,kBAAkB;oBAC1E,QAAQ,GAAG,CAAC,aAAa,SAAS,mBAAmB,CAAC,EAAE,CAAC,cAAc;gBACzE;gBAEA,WAAW;gBACX,IAAI,WAAW,UAAU,IAAI,WAAW,UAAU,CAAC,MAAM,GAAG,GAAG;oBAC7D,QAAQ,GAAG,CAAC;oBACZ,QAAQ,GAAG,CAAC,SAAS,WAAW,UAAU,CAAC,EAAE,CAAC,EAAE;oBAChD,QAAQ,GAAG,CAAC,SAAS,WAAW,UAAU,CAAC,EAAE,CAAC,KAAK;oBACnD,QAAQ,GAAG,CAAC,YAAY,WAAW,UAAU,CAAC,EAAE,CAAC,iBAAiB;oBAClE,QAAQ,GAAG,CAAC,aAAa,WAAW,UAAU,CAAC,EAAE,CAAC,YAAY;gBAChE;gBAEA,iBAAiB,YAAY;gBAE7B,QAAQ,GAAG,CAAC,eAAe;gBAC3B,OAAO,IAAI,CAAC;YACd,OAAO;gBACL,mBAAmB;gBACnB,eAAe;YACjB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,eAAe;YAC7B,QAAQ,GAAG,CAAC,wBAAwB;YACpC,mBAAmB,CAAC,MAAM,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ;YAC7E,eAAe,CAAC,MAAM,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ;QAC3E;IACF;IAEA,YAAY;IACZ,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,QAAQ,GAAG,CAAC;QAEZ,OAAO;YACL,QAAQ,GAAG,CAAC;QACd;IACF,GAAG,EAAE;IAEL,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAU;8CAAoC;;;;;;8CAGpD,8OAAC;oCAAK,WAAU;8CAA6D;;;;;;;;;;;;sCAI/E,8OAAC;4BAAI,WAAU;sCAAwB;;;;;;;;;;;;;;;;;0BAO3C,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,8JAAA,CAAA,aAAU;wBACT,MAAK;wBACL,SAAS;wBACT,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;;0DACf,8OAAC;gDACC,MAAK;gDACL,WAAU;gDACV,SAAS,MAAM,KAAK,KAAK;gDACzB,UAAU,IAAM,MAAM,QAAQ,CAAC;;;;;;0DAEjC,8OAAC;gDAAI,WAAW,CAAC,mDAAmD,EAAE,MAAM,KAAK,KAAK,YAAY,sCAAsC,oCAAoC;0DAAE;;;;;;;;;;;;kDAIhL,8OAAC;wCAAM,WAAU;;0DACf,8OAAC;gDACC,MAAK;gDACL,WAAU;gDACV,SAAS,MAAM,KAAK,KAAK;gDACzB,UAAU,IAAM,MAAM,QAAQ,CAAC;;;;;;0DAEjC,8OAAC;gDAAI,WAAW,CAAC,mDAAmD,EAAE,MAAM,KAAK,KAAK,eAAe,sCAAsC,oCAAoC;0DAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAW7L,8OAAC;gBAAK,UAAU,aAAa;gBAAW,WAAU;;oBAC/C,iCACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;0CAAO;;;;;;4BAAY;4BAAE;;;;;;;kCAI1B,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,8JAAA,CAAA,aAAU;oCACT,MAAK;oCACL,SAAS;oCACT,OAAO;wCAAE,UAAU;oCAAU;oCAC7B,QAAQ,CAAC,EAAE,UAAU,EAAE,iBACrB,8OAAC,gJAAA,CAAA,UAAe;4CACd,OAAM;4CACN,aAAY;4CACZ,OAAO;4CACP,mBAAmB;4CACnB,OAAO,WAAW,KAAK,EAAE;;;;;;;;;;;;;;;;0CAMjC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,OAAM;wCAA6B,MAAK;wCAAO,SAAQ;wCAAY,QAAO;wCAAe,WAAU;kDACtG,cAAA,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;;;;;;0CAK3E,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,8JAAA,CAAA,aAAU;oCACT,MAAK;oCACL,SAAS;oCACT,OAAO;wCAAE,UAAU;oCAAU;oCAC7B,QAAQ,CAAC,EAAE,UAAU,EAAE,iBACrB,8OAAC,gJAAA,CAAA,UAAe;4CACd,OAAM;4CACN,aAAY;4CACZ,OAAO;4CACP,mBAAmB;4CACnB,OAAO,WAAW,KAAK,EAAE;;;;;;;;;;;;;;;;0CAOjC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,8JAAA,CAAA,aAAU;oCACT,MAAK;oCACL,SAAS;oCACT,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,qIAAA,CAAA,UAAK;4CACJ,MAAK;4CACL,IAAG;4CACH,OAAM;4CACN,SAAQ;4CACR,KAAK;4CACL,KAAK;4CACJ,GAAG,KAAK;4CACT,OAAO,OAAO,aAAa,EAAE;4CAC7B,WAAU;4CACV,UAAU,CAAC;gDACT,MAAM,QAAQ,CAAC;gDACf,MAAM,aAAa,MAAM;gDACzB,IAAI,aAAa,gBAAgB,cAAc,IAAI,KAAK,EAAE,MAAM,CAAC,KAAK,IAAI,IAAI,KAAK,aAAa;oDAC9F,SAAS,cAAc,EAAE,MAAM,CAAC,KAAK,EAAE;wDAAE,gBAAgB;oDAAK;gDAChE;4CACF;;;;;;;;;;;;;;;;4BAMP,aAAa,8BACZ,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,8JAAA,CAAA,aAAU;oCACT,MAAK;oCACL,SAAS;oCACT,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,qIAAA,CAAA,UAAK;4CACJ,MAAK;4CACL,IAAG;4CACH,OAAM;4CACN,SAAQ;4CACR,KAAK,iBAAiB;4CACtB,KAAK;4CACJ,GAAG,KAAK;4CACT,OAAO,OAAO,UAAU,EAAE;4CAC1B,WAAU;;;;;;;;;;;;;;;;0CAQpB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,8JAAA,CAAA,aAAU;oCACT,MAAK;oCACL,SAAS;oCACT,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC;;8DACC,8OAAC;oDAAM,SAAQ;oDAAS,WAAU;8DAAiD;;;;;;8DAGnF,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DACC,MAAK;4DACL,SAAS,IAAM,MAAM,KAAK,GAAG,KAAK,MAAM,QAAQ,CAAC,MAAM,KAAK,GAAG;4DAC/D,WAAU;4DACV,UAAU,MAAM,KAAK,IAAI;4DACzB,cAAW;sEAEX,cAAA,8OAAC;gEAAI,WAAU;gEAAU,OAAM;gEAA6B,MAAK;gEAAO,SAAQ;gEAAY,QAAO;0EACjG,cAAA,8OAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAa;oEAAG,GAAE;;;;;;;;;;;;;;;;sEAGzE,8OAAC;4DAAK,WAAU;;gEACb,MAAM,KAAK;gEAAC;;;;;;;sEAEf,8OAAC;4DACC,MAAK;4DACL,SAAS,IAAM,MAAM,KAAK,GAAG,KAAK,MAAM,QAAQ,CAAC,MAAM,KAAK,GAAG;4DAC/D,WAAU;4DACV,UAAU,MAAM,KAAK,IAAI;4DACzB,cAAW;sEAEX,cAAA,8OAAC;gEAAI,WAAU;gEAAU,OAAM;gEAA6B,MAAK;gEAAO,SAAQ;gEAAY,QAAO;0EACjG,cAAA,8OAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAa;oEAAG,GAAE;;;;;;;;;;;;;;;;sEAGzE,8OAAC;4DACC,MAAK;4DACL,IAAG;4DACF,GAAG,KAAK;;;;;;;;;;;;gDAGZ,OAAO,MAAM,kBAAI,8OAAC;oDAAE,WAAU;8DAA6B,OAAO,MAAM,CAAC,OAAO;;;;;;;;;;;;;;;;;;;;;;0CAOzF,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,sIAAA,CAAA,UAAM;oCACL,MAAK;oCACL,UAAU,iBAAiB;oCAC3B,WAAW,iBAAiB;oCAC5B,MAAK;oCACL,WAAU;8CAET,iBAAiB,YAAY,cAAc;;;;;;;;;;;;;;;;;kCAMlD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,8JAAA,CAAA,aAAU;gDACT,MAAK;gDACL,SAAS;gDACT,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAM,SAAQ;gEAAa,WAAU;0EAAoC;;;;;;0EAG1E,8OAAC;gEACC,IAAG;gEACF,GAAG,KAAK;gEACT,WAAU;;kFAEV,8OAAC;wEAAO,OAAM;kFAAU;;;;;;kFACxB,8OAAC;wEAAO,OAAM;kFAAkB;;;;;;kFAChC,8OAAC;wEAAO,OAAM;kFAAW;;;;;;kFACzB,8OAAC;wEAAO,OAAM;kFAAQ;;;;;;;;;;;;;;;;;;;;;;;0DAK9B,8OAAC;gDAAM,WAAU;;kEACf,8OAAC,8JAAA,CAAA,aAAU;wDACT,MAAK;wDACL,SAAS;wDACT,QAAQ,CAAC,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,EAAE,iBACrC,8OAAC;gEACC,MAAK;gEACL,SAAS;gEACT,UAAU;gEACV,WAAU;;;;;;;;;;;kEAIhB,8OAAC;wDAAK,WAAU;kEAAwB;;;;;;;;;;;;;;;;;;kDAG5C,8OAAC,sIAAA,CAAA,UAAM;wCACL,MAAK;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,uBAAuB,CAAC;wCACvC,WAAU;kDAET,sBAAsB,SAAS;;;;;;;;;;;;4BAInC,qCACC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAM,WAAU;;sDACf,8OAAC,8JAAA,CAAA,aAAU;4CACT,MAAK;4CACL,SAAS;4CACT,QAAQ,CAAC,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,EAAE,iBACrC,8OAAC;oDACC,MAAK;oDACL,SAAS;oDACT,UAAU;oDACV,WAAU;;;;;;;;;;;sDAIhB,8OAAC;;8DACC,8OAAC;oDAAK,WAAU;8DAAoC;;;;;;8DACpD,8OAAC;oDAAE,WAAU;8DAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAUnD,iBAAiB,2BAChB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;wCAAkC,OAAM;wCAA6B,MAAK;wCAAO,SAAQ;;0DACtG,8OAAC;gDAAO,WAAU;gDAAa,IAAG;gDAAK,IAAG;gDAAK,GAAE;gDAAK,QAAO;gDAAe,aAAY;;;;;;0DACxF,8OAAC;gDAAK,WAAU;gDAAa,MAAK;gDAAe,GAAE;;;;;;;;;;;;;;;;;8CAGvD,8OAAC;;sDACC,8OAAC;4CAAE,WAAU;sDAA4B;;;;;;sDACzC,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ/C,8OAAC,kJAAA,CAAA,UAAkB;gBACjB,WAAW,iBAAiB;gBAC5B,cAAc;oBACZ,QAAQ,2BAA2B,QAAQ;oBAC3C,aAAa,gCAAgC,QAAQ;oBACrD,eAAe;gBACjB;;;;;;;;;;;;AAIR;uCAEe", "debugId": null}}, {"offset": {"line": 2833, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/AeroScout/aeroscout-frontend/src/components/Earth3D.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useRef, useEffect, useState } from 'react';\nimport dynamic from 'next/dynamic';\n\n// 动态导入Globe组件，避免SSR问题\nconst Globe = dynamic(() => import('react-globe.gl'), {\n  ssr: false,\n  loading: () => (\n    <div className=\"w-96 h-96 rounded-full bg-[#1A4275] flex items-center justify-center\">\n      <div className=\"text-white\">加载中...</div>\n    </div>\n  )\n});\n\n// 主要航空枢纽城市坐标 - 用于生成更真实的航线\nconst MAJOR_AIRPORTS = [\n  { name: 'Beijing', lat: 40.0799, lng: 116.6031 },\n  { name: 'Shanghai', lat: 31.1443, lng: 121.8083 },\n  { name: 'Tokyo', lat: 35.6895, lng: 139.6917 },\n  { name: 'Seoul', lat: 37.5665, lng: 126.9780 },\n  { name: 'Singapore', lat: 1.3521, lng: 103.8198 },\n  { name: 'Dubai', lat: 25.2532, lng: 55.3657 },\n  { name: 'London', lat: 51.5074, lng: -0.1278 },\n  { name: 'Paris', lat: 48.8566, lng: 2.3522 },\n  { name: 'Frankfurt', lat: 50.1109, lng: 8.6821 },\n  { name: 'New York', lat: 40.7128, lng: -74.0060 },\n  { name: 'Los Angeles', lat: 34.0522, lng: -118.2437 },\n  { name: 'Sydney', lat: -33.8688, lng: 151.2093 },\n  { name: 'Bangkok', lat: 13.7563, lng: 100.5018 },\n  { name: 'Hong Kong', lat: 22.3193, lng: 114.1694 },\n  { name: 'Istanbul', lat: 41.0082, lng: 28.9784 },\n  { name: 'Delhi', lat: 28.6139, lng: 77.2090 },\n  { name: 'Moscow', lat: 55.7558, lng: 37.6173 },\n  { name: 'Amsterdam', lat: 52.3676, lng: 4.9041 },\n  { name: 'Toronto', lat: 43.6532, lng: -79.3832 },\n  { name: 'Sao Paulo', lat: -23.5505, lng: -46.6333 }\n];\n\nconst Earth3D = () => {\n  const globeEl = useRef<any>();\n  const [arcsData, setArcsData] = useState<any[]>([]);\n\n  // 生成更真实的航线数据（基于主要航空枢纽）\n  useEffect(() => {\n    // 生成30条航线，从主要枢纽出发或到达\n    const N = 30;\n    const newArcsData = [...Array(N).keys()].map(() => {\n      // 随机选择起点和终点\n      const useHubAsStart = Math.random() > 0.5;\n      const hubIndex = Math.floor(Math.random() * MAJOR_AIRPORTS.length);\n      const hub = MAJOR_AIRPORTS[hubIndex];\n\n      // 为非枢纽端生成随机坐标，但确保在陆地附近（避免海洋中间的点）\n      const randomLat = (Math.random() * 140 - 70) * 0.9; // 避免极地区域\n      const randomLng = Math.random() * 360 - 180;\n\n      // 确定航线颜色 - 使用更多样化的颜色\n      const colors = [\n        'rgba(255,255,255,0.6)', // 白色\n        'rgba(255,165,0,0.6)',   // 橙色\n        'rgba(135,206,250,0.6)', // 天蓝色\n        'rgba(152,251,152,0.5)'  // 淡绿色\n      ];\n      const color = colors[Math.floor(Math.random() * colors.length)];\n\n      // 创建航线数据\n      return {\n        startLat: useHubAsStart ? hub.lat : randomLat,\n        startLng: useHubAsStart ? hub.lng : randomLng,\n        endLat: useHubAsStart ? randomLat : hub.lat,\n        endLng: useHubAsStart ? randomLng : hub.lng,\n        color,\n        // 添加动画时间变化，使航线动画不同步\n        animateTime: 1000 + Math.random() * 2000\n      };\n    });\n\n    setArcsData(newArcsData);\n  }, []);\n\n  // 设置地球自转\n  useEffect(() => {\n    // 确保组件已挂载且globeEl.current已存在\n    if (!globeEl.current) return;\n\n    // 启用自动旋转\n    const controls = globeEl.current.controls();\n    controls.autoRotate = true;\n\n    // 设置旋转轴为Y轴（水平旋转）- 调整速度更慢一些，更自然\n    controls.autoRotateSpeed = -0.6; // 负值使其按照我们期望的方向旋转（从西向东）\n\n    // 禁用缩放和平移，保持地球在固定位置\n    controls.enableZoom = false;\n    controls.enablePan = false;\n\n    // 调整初始视角 - 略微倾斜以展示更多北半球（大多数航线）\n    globeEl.current.pointOfView({\n      lat: 25,\n      lng: 10,\n      altitude: 1.8  // 调整高度，使地球看起来更大但不会太大\n    });\n\n    // 确保控制器更新\n    const animate = () => {\n      controls.update();\n      requestAnimationFrame(animate);\n    };\n\n    const frameId = requestAnimationFrame(animate);\n\n    return () => {\n      cancelAnimationFrame(frameId);\n    };\n  }, []);\n\n  return (\n    <div className=\"w-96 h-96 relative\">\n      <Globe\n        ref={globeEl}\n        // 使用更高质量的地球贴图，包含真实的海洋、陆地、森林和云层\n        globeImageUrl=\"//unpkg.com/three-globe/example/img/earth-blue-marble.jpg\"\n        // 使用更详细的地形图\n        bumpImageUrl=\"//unpkg.com/three-globe/example/img/earth-topology.png\"\n        // 添加云层\n        cloudsImageUrl=\"//unpkg.com/three-globe/example/img/earth-clouds.png\"\n        // 调整大气层 - 更亮、更蓝的色调\n        atmosphereColor=\"rgba(65,145,255,0.3)\"\n        atmosphereAltitude={0.18}\n        // 航线设置\n        arcsData={arcsData}\n        arcColor={'color'}\n        arcDashLength={0.4}\n        arcDashGap={0.2}\n        arcDashAnimateTime={d => d.animateTime}\n        arcStroke={0.5} // 更细的航线\n        arcAltitude={0.2} // 航线高度\n        // 背景透明\n        backgroundColor=\"rgba(0,0,0,0)\"\n        // 调整尺寸\n        width={380}\n        height={380}\n        // 增加地球比例\n        globeScale={1.8}\n      />\n    </div>\n  );\n};\n\nexport default Earth3D;\n"], "names": [], "mappings": ";;;;AAEA;AACA;;AAHA;;;;AAKA,sBAAsB;AACtB,MAAM,QAAQ,CAAA,GAAA,+JAAA,CAAA,UAAO,AAAD;;;;;;IAClB,KAAK;IACL,SAAS,kBACP,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BAAa;;;;;;;;;;;;AAKlC,0BAA0B;AAC1B,MAAM,iBAAiB;IACrB;QAAE,MAAM;QAAW,KAAK;QAAS,KAAK;IAAS;IAC/C;QAAE,MAAM;QAAY,KAAK;QAAS,KAAK;IAAS;IAChD;QAAE,MAAM;QAAS,KAAK;QAAS,KAAK;IAAS;IAC7C;QAAE,MAAM;QAAS,KAAK;QAAS,KAAK;IAAS;IAC7C;QAAE,MAAM;QAAa,KAAK;QAAQ,KAAK;IAAS;IAChD;QAAE,MAAM;QAAS,KAAK;QAAS,KAAK;IAAQ;IAC5C;QAAE,MAAM;QAAU,KAAK;QAAS,KAAK,CAAC;IAAO;IAC7C;QAAE,MAAM;QAAS,KAAK;QAAS,KAAK;IAAO;IAC3C;QAAE,MAAM;QAAa,KAAK;QAAS,KAAK;IAAO;IAC/C;QAAE,MAAM;QAAY,KAAK;QAAS,KAAK,CAAC;IAAQ;IAChD;QAAE,MAAM;QAAe,KAAK;QAAS,KAAK,CAAC;IAAS;IACpD;QAAE,MAAM;QAAU,KAAK,CAAC;QAAS,KAAK;IAAS;IAC/C;QAAE,MAAM;QAAW,KAAK;QAAS,KAAK;IAAS;IAC/C;QAAE,MAAM;QAAa,KAAK;QAAS,KAAK;IAAS;IACjD;QAAE,MAAM;QAAY,KAAK;QAAS,KAAK;IAAQ;IAC/C;QAAE,MAAM;QAAS,KAAK;QAAS,KAAK;IAAQ;IAC5C;QAAE,MAAM;QAAU,KAAK;QAAS,KAAK;IAAQ;IAC7C;QAAE,MAAM;QAAa,KAAK;QAAS,KAAK;IAAO;IAC/C;QAAE,MAAM;QAAW,KAAK;QAAS,KAAK,CAAC;IAAQ;IAC/C;QAAE,MAAM;QAAa,KAAK,CAAC;QAAS,KAAK,CAAC;IAAQ;CACnD;AAED,MAAM,UAAU;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD;IACrB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IAElD,uBAAuB;IACvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,qBAAqB;QACrB,MAAM,IAAI;QACV,MAAM,cAAc;eAAI,MAAM,GAAG,IAAI;SAAG,CAAC,GAAG,CAAC;YAC3C,YAAY;YACZ,MAAM,gBAAgB,KAAK,MAAM,KAAK;YACtC,MAAM,WAAW,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,eAAe,MAAM;YACjE,MAAM,MAAM,cAAc,CAAC,SAAS;YAEpC,iCAAiC;YACjC,MAAM,YAAY,CAAC,KAAK,MAAM,KAAK,MAAM,EAAE,IAAI,KAAK,SAAS;YAC7D,MAAM,YAAY,KAAK,MAAM,KAAK,MAAM;YAExC,qBAAqB;YACrB,MAAM,SAAS;gBACb;gBACA;gBACA;gBACA,wBAAyB,MAAM;aAChC;YACD,MAAM,QAAQ,MAAM,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,OAAO,MAAM,EAAE;YAE/D,SAAS;YACT,OAAO;gBACL,UAAU,gBAAgB,IAAI,GAAG,GAAG;gBACpC,UAAU,gBAAgB,IAAI,GAAG,GAAG;gBACpC,QAAQ,gBAAgB,YAAY,IAAI,GAAG;gBAC3C,QAAQ,gBAAgB,YAAY,IAAI,GAAG;gBAC3C;gBACA,oBAAoB;gBACpB,aAAa,OAAO,KAAK,MAAM,KAAK;YACtC;QACF;QAEA,YAAY;IACd,GAAG,EAAE;IAEL,SAAS;IACT,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,6BAA6B;QAC7B,IAAI,CAAC,QAAQ,OAAO,EAAE;QAEtB,SAAS;QACT,MAAM,WAAW,QAAQ,OAAO,CAAC,QAAQ;QACzC,SAAS,UAAU,GAAG;QAEtB,+BAA+B;QAC/B,SAAS,eAAe,GAAG,CAAC,KAAK,wBAAwB;QAEzD,oBAAoB;QACpB,SAAS,UAAU,GAAG;QACtB,SAAS,SAAS,GAAG;QAErB,+BAA+B;QAC/B,QAAQ,OAAO,CAAC,WAAW,CAAC;YAC1B,KAAK;YACL,KAAK;YACL,UAAU,IAAK,qBAAqB;QACtC;QAEA,UAAU;QACV,MAAM,UAAU;YACd,SAAS,MAAM;YACf,sBAAsB;QACxB;QAEA,MAAM,UAAU,sBAAsB;QAEtC,OAAO;YACL,qBAAqB;QACvB;IACF,GAAG,EAAE;IAEL,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YACC,KAAK;YACL,+BAA+B;YAC/B,eAAc;YACd,YAAY;YACZ,cAAa;YACb,OAAO;YACP,gBAAe;YACf,mBAAmB;YACnB,iBAAgB;YAChB,oBAAoB;YACpB,OAAO;YACP,UAAU;YACV,UAAU;YACV,eAAe;YACf,YAAY;YACZ,oBAAoB,CAAA,IAAK,EAAE,WAAW;YACtC,WAAW;YACX,aAAa;YACb,OAAO;YACP,iBAAgB;YAChB,OAAO;YACP,OAAO;YACP,QAAQ;YACR,SAAS;YACT,YAAY;;;;;;;;;;;AAIpB;uCAEe", "debugId": null}}, {"offset": {"line": 3083, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/AeroScout/aeroscout-frontend/src/app/search/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\nimport FlightSearchForm from '@/components/flight/FlightSearchForm';\nimport Button from '@/components/common/Button';\nimport Earth3D from '@/components/Earth3D';\nimport Link from 'next/link';\nimport { useAuthStore } from '@/store/authStore';\n\nexport default function SearchPage() {\n  const { isAuthenticated, currentUser, logout } = useAuthStore();\n  const [showUserMenu, setShowUserMenu] = useState(false);\n\n  // 用于客户端渲染的状态\n  const [mounted, setMounted] = useState(false);\n\n  useEffect(() => {\n    setMounted(true);\n  }, []);\n\n  return (\n    <div className=\"min-h-screen bg-white\">\n      {/* 顶部导航 - 苹果官网风格 */}\n      <nav className=\"bg-white/90 backdrop-blur-md sticky top-0 z-50 border-b border-[#E5E5EA]\">\n        <div className=\"max-w-[980px] mx-auto px-5 py-3 flex justify-between items-center\">\n          <div>\n            <Link href=\"/\" className=\"text-[21px] font-medium text-[#1D1D1F] bg-gradient-to-r from-[#0066CC] to-[#5AC8FA] bg-clip-text text-transparent transition-apple hover:opacity-80\">AeroScout</Link>\n          </div>\n\n          {/* 移动端菜单按钮 */}\n          <div className=\"md:hidden\">\n            <button className=\"p-2 rounded-full hover:bg-[#F5F5F7] transition-apple\">\n              <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 text-[#1D1D1F]\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\n              </svg>\n            </button>\n          </div>\n\n          {/* 桌面端导航 - 根据登录状态显示不同内容 */}\n          {mounted && (\n            <div className=\"hidden md:flex items-center space-x-4\">\n              {isAuthenticated ? (\n                <>\n                  <Link\n                    href=\"/dashboard\"\n                    className=\"text-[14px] font-medium text-[#1D1D1F] hover:text-[#0071E3] px-3 py-1.5 rounded-full transition-apple\"\n                  >\n                    控制面板\n                  </Link>\n                  <div className=\"relative\">\n                    <button\n                      onClick={() => setShowUserMenu(!showUserMenu)}\n                      className=\"flex items-center space-x-2 text-[14px] font-medium text-[#1D1D1F] hover:text-[#0071E3] px-3 py-1.5 rounded-full transition-apple\"\n                    >\n                      <span>{currentUser?.email || '用户'}</span>\n                      <svg\n                        xmlns=\"http://www.w3.org/2000/svg\"\n                        className={`h-4 w-4 transition-transform ${showUserMenu ? 'rotate-180' : ''}`}\n                        fill=\"none\"\n                        viewBox=\"0 0 24 24\"\n                        stroke=\"currentColor\"\n                      >\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 9l-7 7-7-7\" />\n                      </svg>\n                    </button>\n\n                    {showUserMenu && (\n                      <div className=\"absolute right-0 mt-2 w-48 bg-white rounded-apple shadow-apple-sm border border-[#E8E8ED] py-1 z-20\">\n                        <Link\n                          href=\"/dashboard\"\n                          className=\"block px-4 py-2 text-sm text-[#1D1D1F] hover:bg-[#F5F5F7] transition-apple\"\n                        >\n                          控制面板\n                        </Link>\n                        <Link\n                          href=\"/settings\"\n                          className=\"block px-4 py-2 text-sm text-[#1D1D1F] hover:bg-[#F5F5F7] transition-apple\"\n                        >\n                          账户设置\n                        </Link>\n                        <button\n                          onClick={() => {\n                            logout();\n                            window.location.href = '/auth/login';\n                          }}\n                          className=\"block w-full text-left px-4 py-2 text-sm text-[#FF3B30] hover:bg-[#F5F5F7] transition-apple\"\n                        >\n                          登出\n                        </button>\n                      </div>\n                    )}\n                  </div>\n                </>\n              ) : (\n                <>\n                  <Link\n                    href=\"/auth/login\"\n                    className=\"text-[14px] font-medium text-[#1D1D1F] hover:text-[#0071E3] px-3 py-1.5 rounded-full transition-apple\"\n                  >\n                    登录\n                  </Link>\n                  <Link\n                    href=\"/auth/register\"\n                    className=\"text-[14px] font-medium text-white bg-[#0071E3] hover:bg-[#0077ED] px-4 py-1.5 rounded-full shadow-apple-sm transition-apple\"\n                  >\n                    注册\n                  </Link>\n                </>\n              )}\n            </div>\n          )}\n        </div>\n      </nav>\n\n      {/* 主标题区 - 活力旅行风格 */}\n      <section className=\"bg-white pt-8 pb-16 mb-8\">\n        <div className=\"max-w-[980px] mx-auto px-5\">\n          <div className=\"flex flex-col md:flex-row items-center justify-between\">\n            <div className=\"text-left mb-8 md:mb-0 md:max-w-md\">\n              <div className=\"flex items-center mb-4\">\n                <div className=\"w-8 h-8 rounded-full bg-[#FF9500] flex items-center justify-center mr-3\">\n                  <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"white\" className=\"w-4 h-4\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 19l9 2-9-18-9 18 9-2zm0 0v-8\" />\n                  </svg>\n                </div>\n                <span className=\"text-[16px] font-medium text-[#FF9500]\">旅行从未如此简单</span>\n              </div>\n              <h1 className=\"text-[44px] font-bold text-[#1D1D1F] tracking-tight leading-tight mb-4\">发现您的<br/><span className=\"text-[#FF9500]\">下一站</span>精彩</h1>\n              <p className=\"text-[18px] text-[#86868B] mb-6\">智能搜索全球航班，为您找到最优惠的价格和最佳的旅行组合</p>\n              <div className=\"flex items-center\">\n                <div className=\"flex -space-x-2 mr-4\">\n                  <div className=\"w-8 h-8 rounded-full bg-[#0066CC] flex items-center justify-center border-2 border-white\">\n                    <span className=\"text-[10px] text-white font-bold\">500+</span>\n                  </div>\n                  <div className=\"w-8 h-8 rounded-full bg-[#34C759] flex items-center justify-center border-2 border-white\">\n                    <span className=\"text-[10px] text-white font-bold\">30%</span>\n                  </div>\n                  <div className=\"w-8 h-8 rounded-full bg-[#FF9500] flex items-center justify-center border-2 border-white\">\n                    <span className=\"text-[10px] text-white font-bold\">24/7</span>\n                  </div>\n                </div>\n                <span className=\"text-[14px] text-[#86868B]\">已为超过100万用户提供服务</span>\n              </div>\n            </div>\n            <div className=\"relative -mt-16\">\n              {/* 3D地球组件 */}\n              <div className=\"w-96 h-96 relative\">\n                {/* 3D地球组件 */}\n                <Earth3D />\n\n                {/* 轨道环 - 装饰效果 */}\n                <div className=\"absolute inset-[-15px] border-2 border-white/10 rounded-full pointer-events-none\"></div>\n                <div className=\"absolute inset-[-30px] border border-white/5 rounded-full pointer-events-none\"></div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* 搜索表单区域 - 苹果风格 */}\n      <section className=\"max-w-[980px] mx-auto px-5 -mt-28 mb-8 relative z-10 overflow-visible\">\n        <FlightSearchForm />\n      </section>\n\n      {/* 特性介绍 - 苹果风格标题 */}\n      <section className=\"max-w-[980px] mx-auto px-5 py-16 text-center\">\n        <h2 className=\"text-[32px] font-semibold text-[#1D1D1F] mb-2\">智能航班搜索</h2>\n        <p className=\"text-[19px] text-[#86868B] max-w-2xl mx-auto mb-16\">\n          我们的算法可以发现传统搜索引擎无法找到的航班组合，平均为您节省 30% 的旅行成本。\n        </p>\n\n        {/* 特性卡片 - 苹果风格网格 */}\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n          <div className=\"bg-gradient-to-b from-[#E6F2FF] to-white rounded-2xl p-8 shadow-md flex flex-col items-center text-center\">\n            <div className=\"w-16 h-16 bg-gradient-to-r from-[#0066CC] to-[#5AC8FA] rounded-full flex items-center justify-center mb-6 shadow-lg\">\n              <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"white\" className=\"w-8 h-8\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n              </svg>\n            </div>\n            <h3 className=\"text-[21px] font-semibold text-[#1D1D1F] mb-3\">多航司组合</h3>\n            <p className=\"text-[15px] text-[#86868B] mb-6\">\n              智能组合不同航空公司的航班，发现隐藏的价格优势。\n            </p>\n            <Button variant=\"link\" size=\"sm\" className=\"mt-auto text-[#0066CC]\">查看示例</Button>\n          </div>\n\n          <div className=\"bg-gradient-to-b from-[#E6FFF2] to-white rounded-2xl p-8 shadow-md flex flex-col items-center text-center\">\n            <div className=\"w-16 h-16 bg-gradient-to-r from-[#34C759] to-[#30D158] rounded-full flex items-center justify-center mb-6 shadow-lg\">\n              <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"white\" className=\"w-8 h-8\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z\" />\n              </svg>\n            </div>\n            <h3 className=\"text-[21px] font-semibold text-[#1D1D1F] mb-3\">价格保障</h3>\n            <p className=\"text-[15px] text-[#86868B] mb-6\">\n              透明定价，显示所有税费和附加费用，无隐藏收费。\n            </p>\n            <Button variant=\"link\" size=\"sm\" className=\"mt-auto text-[#34C759]\">了解详情</Button>\n          </div>\n\n          <div className=\"bg-gradient-to-b from-[#FFF8E6] to-white rounded-2xl p-8 shadow-md flex flex-col items-center text-center\">\n            <div className=\"w-16 h-16 bg-gradient-to-r from-[#FF9500] to-[#FFCC00] rounded-full flex items-center justify-center mb-6 shadow-lg\">\n              <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"white\" className=\"w-8 h-8\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\n              </svg>\n            </div>\n            <h3 className=\"text-[21px] font-semibold text-[#1D1D1F] mb-3\">价格追踪</h3>\n            <p className=\"text-[15px] text-[#86868B] mb-6\">\n              设置价格提醒，当您关注的航线价格下降时获得通知。\n            </p>\n            <Button variant=\"link\" size=\"sm\" className=\"mt-auto text-[#FF9500]\">开始追踪</Button>\n          </div>\n        </div>\n      </section>\n\n      {/* 会员服务 - 渐变背景 */}\n      <section className=\"bg-gradient-to-r from-[#000428] to-[#004e92] py-20 my-16 rounded-3xl mx-5 overflow-hidden relative\">\n        <div className=\"absolute inset-0 bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPjxkZWZzPjxwYXR0ZXJuIGlkPSJwYXR0ZXJuIiB4PSIwIiB5PSIwIiB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHBhdHRlcm5Vbml0cz0idXNlclNwYWNlT25Vc2UiIHBhdHRlcm5UcmFuc2Zvcm09InJvdGF0ZSgzMCkiPjxyZWN0IHg9IjAiIHk9IjAiIHdpZHRoPSIyIiBoZWlnaHQ9IjIiIGZpbGw9IiNmZmZmZmYiIG9wYWNpdHk9IjAuMDUiLz48L3BhdHRlcm4+PC9kZWZzPjxyZWN0IHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiIGZpbGw9InVybCgjcGF0dGVybikiLz48L3N2Zz4=')] opacity-50\"></div>\n        <div className=\"max-w-[980px] mx-auto px-5 text-center relative z-10\">\n          <div className=\"inline-block mb-6 bg-white/10 backdrop-blur-md px-6 py-2 rounded-full\">\n            <span className=\"text-[15px] font-medium text-white\">全新上线</span>\n          </div>\n          <h2 className=\"text-[40px] font-semibold text-white mb-4\">AeroScout+</h2>\n          <p className=\"text-[21px] text-white/80 max-w-2xl mx-auto mb-10\">\n            订阅会员服务，享受独家优惠和高级功能。\n          </p>\n          <Button variant=\"primary\" size=\"lg\" className=\"bg-gradient-to-r from-[#5AC8FA] to-[#0066CC] text-white hover:opacity-90 px-8 py-3 rounded-full shadow-lg font-medium\">了解更多</Button>\n        </div>\n      </section>\n\n      {/* 全球覆盖 - 现代卡片设计 */}\n      <section className=\"py-16 my-12\">\n        <div className=\"max-w-[980px] mx-auto px-5\">\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-12 items-center\">\n            <div>\n              <h2 className=\"text-[32px] font-semibold text-[#1D1D1F] mb-4\">全球覆盖</h2>\n              <p className=\"text-[17px] text-[#86868B] mb-8\">\n                搜索全球数百家航空公司，覆盖所有主要航线。无论您想去哪里，我们都能帮您找到最佳航班。\n              </p>\n              <ul className=\"space-y-5\">\n                <li className=\"flex items-center\">\n                  <div className=\"w-10 h-10 rounded-full bg-gradient-to-r from-[#0066CC]/10 to-[#5AC8FA]/10 flex items-center justify-center mr-4\">\n                    <svg className=\"w-5 h-5 text-[#0066CC]\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 13l4 4L19 7\" />\n                    </svg>\n                  </div>\n                  <span className=\"text-[16px] text-[#1D1D1F]\">覆盖全球 200+ 个国家和地区</span>\n                </li>\n                <li className=\"flex items-center\">\n                  <div className=\"w-10 h-10 rounded-full bg-gradient-to-r from-[#0066CC]/10 to-[#5AC8FA]/10 flex items-center justify-center mr-4\">\n                    <svg className=\"w-5 h-5 text-[#0066CC]\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 13l4 4L19 7\" />\n                    </svg>\n                  </div>\n                  <span className=\"text-[16px] text-[#1D1D1F]\">支持 500+ 家航空公司搜索</span>\n                </li>\n                <li className=\"flex items-center\">\n                  <div className=\"w-10 h-10 rounded-full bg-gradient-to-r from-[#0066CC]/10 to-[#5AC8FA]/10 flex items-center justify-center mr-4\">\n                    <svg className=\"w-5 h-5 text-[#0066CC]\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 13l4 4L19 7\" />\n                    </svg>\n                  </div>\n                  <span className=\"text-[16px] text-[#1D1D1F]\">每月为超过 100 万用户提供服务</span>\n                </li>\n              </ul>\n            </div>\n            <div className=\"bg-gradient-to-br from-[#E6F2FF] to-white rounded-2xl p-10 shadow-lg flex items-center justify-center\">\n              <div className=\"text-center\">\n                <div className=\"text-[64px] font-bold bg-gradient-to-r from-[#0066CC] to-[#5AC8FA] bg-clip-text text-transparent\">30%</div>\n                <p className=\"text-[19px] text-[#1D1D1F] mt-2\">平均节省的旅行成本</p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* 页脚 - 现代简约风格 */}\n      <footer className=\"border-t border-[#E5E5EA] py-16\">\n        <div className=\"max-w-[980px] mx-auto px-5\">\n          <div className=\"flex flex-col md:flex-row justify-between mb-12\">\n            <div className=\"mb-8 md:mb-0\">\n              <span className=\"text-[24px] font-medium bg-gradient-to-r from-[#0066CC] to-[#5AC8FA] bg-clip-text text-transparent\">AeroScout</span>\n              <p className=\"text-[14px] text-[#86868B] mt-3 max-w-xs\">\n                智能航班搜索，发现最佳组合，节省您的旅行成本。\n              </p>\n            </div>\n            <div className=\"grid grid-cols-2 md:grid-cols-3 gap-8\">\n              <div>\n                <h4 className=\"text-[14px] font-semibold text-[#1D1D1F] mb-4\">关于我们</h4>\n                <ul className=\"space-y-3\">\n                  <li><a href=\"#\" className=\"text-[14px] text-[#86868B] hover:text-[#0066CC] transition-colors\">公司介绍</a></li>\n                  <li><a href=\"#\" className=\"text-[14px] text-[#86868B] hover:text-[#0066CC] transition-colors\">新闻中心</a></li>\n                  <li><a href=\"#\" className=\"text-[14px] text-[#86868B] hover:text-[#0066CC] transition-colors\">联系我们</a></li>\n                </ul>\n              </div>\n              <div>\n                <h4 className=\"text-[14px] font-semibold text-[#1D1D1F] mb-4\">服务支持</h4>\n                <ul className=\"space-y-3\">\n                  <li><a href=\"#\" className=\"text-[14px] text-[#86868B] hover:text-[#0066CC] transition-colors\">帮助中心</a></li>\n                  <li><a href=\"#\" className=\"text-[14px] text-[#86868B] hover:text-[#0066CC] transition-colors\">联系客服</a></li>\n                  <li><a href=\"#\" className=\"text-[14px] text-[#86868B] hover:text-[#0066CC] transition-colors\">常见问题</a></li>\n                </ul>\n              </div>\n              <div>\n                <h4 className=\"text-[14px] font-semibold text-[#1D1D1F] mb-4\">法律信息</h4>\n                <ul className=\"space-y-3\">\n                  <li><a href=\"#\" className=\"text-[14px] text-[#86868B] hover:text-[#0066CC] transition-colors\">隐私政策</a></li>\n                  <li><a href=\"#\" className=\"text-[14px] text-[#86868B] hover:text-[#0066CC] transition-colors\">使用条款</a></li>\n                  <li><a href=\"#\" className=\"text-[14px] text-[#86868B] hover:text-[#0066CC] transition-colors\">销售政策</a></li>\n                </ul>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"border-t border-[#E5E5EA] pt-8 flex flex-col md:flex-row justify-between items-center\">\n            <p className=\"text-[14px] text-[#86868B] mb-4 md:mb-0\">\n              &copy; {new Date().getFullYear()} AeroScout. 保留所有权利.\n            </p>\n            <div className=\"flex space-x-6\">\n              <a href=\"#\" className=\"text-[#86868B] hover:text-[#0066CC]\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\" fill=\"currentColor\" viewBox=\"0 0 16 16\">\n                  <path d=\"M16 8.049c0-4.446-3.582-8.05-8-8.05C3.58 0-.002 3.603-.002 8.05c0 4.017 2.926 7.347 6.75 7.951v-5.625h-2.03V8.05H6.75V6.275c0-2.017 1.195-3.131 3.022-3.131.876 0 1.791.157 1.791.157v1.98h-1.009c-.993 0-1.303.621-1.303 1.258v1.51h2.218l-.354 2.326H9.25V16c3.824-.604 6.75-3.934 6.75-7.951z\"/>\n                </svg>\n              </a>\n              <a href=\"#\" className=\"text-[#86868B] hover:text-[#0066CC]\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\" fill=\"currentColor\" viewBox=\"0 0 16 16\">\n                  <path d=\"M5.026 15c6.038 0 9.341-5.003 9.341-9.334 0-.14 0-.282-.006-.422A6.685 6.685 0 0 0 16 3.542a6.658 6.658 0 0 1-1.889.518 3.301 3.301 0 0 0 1.447-1.817 6.533 6.533 0 0 1-2.087.793A3.286 3.286 0 0 0 7.875 6.03a9.325 9.325 0 0 1-6.767-3.429 3.289 3.289 0 0 0 1.018 4.382A3.323 3.323 0 0 1 .64 6.575v.045a3.288 3.288 0 0 0 2.632 3.218 3.203 3.203 0 0 1-.865.115 3.23 3.23 0 0 1-.614-.057 3.283 3.283 0 0 0 3.067 2.277A6.588 6.588 0 0 1 .78 13.58a6.32 6.32 0 0 1-.78-.045A9.344 9.344 0 0 0 5.026 15z\"/>\n                </svg>\n              </a>\n              <a href=\"#\" className=\"text-[#86868B] hover:text-[#0066CC]\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\" fill=\"currentColor\" viewBox=\"0 0 16 16\">\n                  <path d=\"M8 0C5.829 0 5.556.01 4.703.048 3.85.088 3.269.222 2.76.42a3.917 3.917 0 0 0-1.417.923A3.927 3.927 0 0 0 .42 2.76C.222 3.268.087 3.85.048 4.7.01 5.555 0 5.827 0 8.001c0 2.172.01 2.444.048 3.297.04.852.174 1.433.372 1.942.205.526.478.972.923 1.417.444.445.89.719 1.416.923.51.198 1.09.333 1.942.372C5.555 15.99 5.827 16 8 16s2.444-.01 3.298-.048c.851-.04 1.434-.174 1.943-.372a3.916 3.916 0 0 0 1.416-.923c.445-.445.718-.891.923-1.417.197-.509.332-1.09.372-1.942C15.99 10.445 16 10.173 16 8s-.01-2.445-.048-3.299c-.04-.851-.175-1.433-.372-1.941a3.926 3.926 0 0 0-.923-1.417A3.911 3.911 0 0 0 13.24.42c-.51-.198-1.092-.333-1.943-.372C10.443.01 10.172 0 7.998 0h.003zm-.717 1.442h.718c2.136 0 2.389.007 3.232.046.78.035 1.204.166 1.486.275.373.145.64.319.92.599.28.28.453.546.598.92.11.281.24.705.275 1.485.039.843.047 1.096.047 3.231s-.008 2.389-.047 3.232c-.035.78-.166 1.203-.275 1.485a2.47 2.47 0 0 1-.599.919c-.28.28-.546.453-.92.598-.28.11-.704.24-1.485.276-.843.038-1.096.047-3.232.047s-2.39-.009-3.233-.047c-.78-.036-1.203-.166-1.485-.276a2.478 2.478 0 0 1-.92-.598 2.48 2.48 0 0 1-.6-.92c-.109-.281-.24-.705-.275-1.485-.038-.843-.046-1.096-.046-3.233 0-2.136.008-2.388.046-3.231.036-.78.166-1.204.276-1.486.145-.373.319-.64.599-.92.28-.28.546-.453.92-.598.282-.11.705-.24 1.485-.276.738-.034 1.024-.044 2.515-.045v.002zm4.988 1.328a.96.96 0 1 0 0 1.92.96.96 0 0 0 0-1.92zm-4.27 1.122a4.109 4.109 0 1 0 0 8.217 4.109 4.109 0 0 0 0-8.217zm0 1.441a2.667 2.667 0 1 1 0 5.334 2.667 2.667 0 0 1 0-5.334z\"/>\n                </svg>\n              </a>\n            </div>\n          </div>\n        </div>\n      </footer>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;AASe,SAAS;IACtB,MAAM,EAAE,eAAe,EAAE,WAAW,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,yHAAA,CAAA,eAAY,AAAD;IAC5D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,aAAa;IACb,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW;IACb,GAAG,EAAE;IAEL,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;sCACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;0CAAsJ;;;;;;;;;;;sCAIjL,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAO,WAAU;0CAChB,cAAA,8OAAC;oCAAI,OAAM;oCAA6B,WAAU;oCAAyB,MAAK;oCAAO,SAAQ;oCAAY,QAAO;8CAChH,cAAA,8OAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;wBAM1E,yBACC,8OAAC;4BAAI,WAAU;sCACZ,gCACC;;kDACE,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,SAAS,IAAM,gBAAgB,CAAC;gDAChC,WAAU;;kEAEV,8OAAC;kEAAM,aAAa,SAAS;;;;;;kEAC7B,8OAAC;wDACC,OAAM;wDACN,WAAW,CAAC,6BAA6B,EAAE,eAAe,eAAe,IAAI;wDAC7E,MAAK;wDACL,SAAQ;wDACR,QAAO;kEAEP,cAAA,8OAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;;;;;;;;;;;;;4CAIxE,8BACC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,4JAAA,CAAA,UAAI;wDACH,MAAK;wDACL,WAAU;kEACX;;;;;;kEAGD,8OAAC,4JAAA,CAAA,UAAI;wDACH,MAAK;wDACL,WAAU;kEACX;;;;;;kEAGD,8OAAC;wDACC,SAAS;4DACP;4DACA,OAAO,QAAQ,CAAC,IAAI,GAAG;wDACzB;wDACA,WAAU;kEACX;;;;;;;;;;;;;;;;;;;6DAQT;;kDACE,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;0BAWb,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,OAAM;oDAA6B,MAAK;oDAAO,SAAQ;oDAAY,QAAO;oDAAQ,WAAU;8DAC/F,cAAA,8OAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;0DAGzE,8OAAC;gDAAK,WAAU;0DAAyC;;;;;;;;;;;;kDAE3D,8OAAC;wCAAG,WAAU;;4CAAyE;0DAAI,8OAAC;;;;;0DAAI,8OAAC;gDAAK,WAAU;0DAAiB;;;;;;4CAAU;;;;;;;kDAC3I,8OAAC;wCAAE,WAAU;kDAAkC;;;;;;kDAC/C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAK,WAAU;sEAAmC;;;;;;;;;;;kEAErD,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAK,WAAU;sEAAmC;;;;;;;;;;;kEAErD,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAK,WAAU;sEAAmC;;;;;;;;;;;;;;;;;0DAGvD,8OAAC;gDAAK,WAAU;0DAA6B;;;;;;;;;;;;;;;;;;0CAGjD,8OAAC;gCAAI,WAAU;0CAEb,cAAA,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC,6HAAA,CAAA,UAAO;;;;;sDAGR,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQzB,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC,gJAAA,CAAA,UAAgB;;;;;;;;;;0BAInB,8OAAC;gBAAQ,WAAU;;kCACjB,8OAAC;wBAAG,WAAU;kCAAgD;;;;;;kCAC9D,8OAAC;wBAAE,WAAU;kCAAqD;;;;;;kCAKlE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,OAAM;4CAA6B,MAAK;4CAAO,SAAQ;4CAAY,QAAO;4CAAQ,WAAU;sDAC/F,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;;;;;;kDAGzE,8OAAC;wCAAG,WAAU;kDAAgD;;;;;;kDAC9D,8OAAC;wCAAE,WAAU;kDAAkC;;;;;;kDAG/C,8OAAC,sIAAA,CAAA,UAAM;wCAAC,SAAQ;wCAAO,MAAK;wCAAK,WAAU;kDAAyB;;;;;;;;;;;;0CAGtE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,OAAM;4CAA6B,MAAK;4CAAO,SAAQ;4CAAY,QAAO;4CAAQ,WAAU;sDAC/F,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;;;;;;kDAGzE,8OAAC;wCAAG,WAAU;kDAAgD;;;;;;kDAC9D,8OAAC;wCAAE,WAAU;kDAAkC;;;;;;kDAG/C,8OAAC,sIAAA,CAAA,UAAM;wCAAC,SAAQ;wCAAO,MAAK;wCAAK,WAAU;kDAAyB;;;;;;;;;;;;0CAGtE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,OAAM;4CAA6B,MAAK;4CAAO,SAAQ;4CAAY,QAAO;4CAAQ,WAAU;sDAC/F,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;;;;;;kDAGzE,8OAAC;wCAAG,WAAU;kDAAgD;;;;;;kDAC9D,8OAAC;wCAAE,WAAU;kDAAkC;;;;;;kDAG/C,8OAAC,sIAAA,CAAA,UAAM;wCAAC,SAAQ;wCAAO,MAAK;wCAAK,WAAU;kDAAyB;;;;;;;;;;;;;;;;;;;;;;;;0BAM1E,8OAAC;gBAAQ,WAAU;;kCACjB,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAK,WAAU;8CAAqC;;;;;;;;;;;0CAEvD,8OAAC;gCAAG,WAAU;0CAA4C;;;;;;0CAC1D,8OAAC;gCAAE,WAAU;0CAAoD;;;;;;0CAGjE,8OAAC,sIAAA,CAAA,UAAM;gCAAC,SAAQ;gCAAU,MAAK;gCAAK,WAAU;0CAAwH;;;;;;;;;;;;;;;;;;0BAK1K,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAgD;;;;;;kDAC9D,8OAAC;wCAAE,WAAU;kDAAkC;;;;;;kDAG/C,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;4DAAyB,MAAK;4DAAO,SAAQ;4DAAY,QAAO;sEAC7E,cAAA,8OAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;;;;;;kEAGzE,8OAAC;wDAAK,WAAU;kEAA6B;;;;;;;;;;;;0DAE/C,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;4DAAyB,MAAK;4DAAO,SAAQ;4DAAY,QAAO;sEAC7E,cAAA,8OAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;;;;;;kEAGzE,8OAAC;wDAAK,WAAU;kEAA6B;;;;;;;;;;;;0DAE/C,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;4DAAyB,MAAK;4DAAO,SAAQ;4DAAY,QAAO;sEAC7E,cAAA,8OAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;;;;;;kEAGzE,8OAAC;wDAAK,WAAU;kEAA6B;;;;;;;;;;;;;;;;;;;;;;;;0CAInD,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAmG;;;;;;sDAClH,8OAAC;4CAAE,WAAU;sDAAkC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQzD,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAAqG;;;;;;sDACrH,8OAAC;4CAAE,WAAU;sDAA2C;;;;;;;;;;;;8CAI1D,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAgD;;;;;;8DAC9D,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;sEAAG,cAAA,8OAAC;gEAAE,MAAK;gEAAI,WAAU;0EAAoE;;;;;;;;;;;sEAC9F,8OAAC;sEAAG,cAAA,8OAAC;gEAAE,MAAK;gEAAI,WAAU;0EAAoE;;;;;;;;;;;sEAC9F,8OAAC;sEAAG,cAAA,8OAAC;gEAAE,MAAK;gEAAI,WAAU;0EAAoE;;;;;;;;;;;;;;;;;;;;;;;sDAGlG,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAgD;;;;;;8DAC9D,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;sEAAG,cAAA,8OAAC;gEAAE,MAAK;gEAAI,WAAU;0EAAoE;;;;;;;;;;;sEAC9F,8OAAC;sEAAG,cAAA,8OAAC;gEAAE,MAAK;gEAAI,WAAU;0EAAoE;;;;;;;;;;;sEAC9F,8OAAC;sEAAG,cAAA,8OAAC;gEAAE,MAAK;gEAAI,WAAU;0EAAoE;;;;;;;;;;;;;;;;;;;;;;;sDAGlG,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAgD;;;;;;8DAC9D,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;sEAAG,cAAA,8OAAC;gEAAE,MAAK;gEAAI,WAAU;0EAAoE;;;;;;;;;;;sEAC9F,8OAAC;sEAAG,cAAA,8OAAC;gEAAE,MAAK;gEAAI,WAAU;0EAAoE;;;;;;;;;;;sEAC9F,8OAAC;sEAAG,cAAA,8OAAC;gEAAE,MAAK;gEAAI,WAAU;0EAAoE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAMtG,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;;wCAA0C;wCAC7C,IAAI,OAAO,WAAW;wCAAG;;;;;;;8CAEnC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,MAAK;4CAAI,WAAU;sDACpB,cAAA,8OAAC;gDAAI,OAAM;gDAA6B,OAAM;gDAAK,QAAO;gDAAK,MAAK;gDAAe,SAAQ;0DACzF,cAAA,8OAAC;oDAAK,GAAE;;;;;;;;;;;;;;;;sDAGZ,8OAAC;4CAAE,MAAK;4CAAI,WAAU;sDACpB,cAAA,8OAAC;gDAAI,OAAM;gDAA6B,OAAM;gDAAK,QAAO;gDAAK,MAAK;gDAAe,SAAQ;0DACzF,cAAA,8OAAC;oDAAK,GAAE;;;;;;;;;;;;;;;;sDAGZ,8OAAC;4CAAE,MAAK;4CAAI,WAAU;sDACpB,cAAA,8OAAC;gDAAI,OAAM;gDAA6B,OAAM;gDAAK,QAAO;gDAAK,MAAK;gDAAe,SAAQ;0DACzF,cAAA,8OAAC;oDAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS1B", "debugId": null}}]}