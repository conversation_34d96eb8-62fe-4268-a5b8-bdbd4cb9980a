{"data": {"onewayItineraries": {"__typename": "Itineraries", "server": {"requestId": "3442e11aadb64019a7da8b1ee085ed0d", "environment": "PROD", "packageVersion": "git-bff8ac9486769348e980126f223dbeddabd34ddc", "serverToken": "****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"}, "metadata": {"eligibilityInformation": {"baggageEligibilityInformation": {"topFiveResultsBaggageEligibleForPrompt": null, "numberOfBags": null}, "guaranteeAndRedirectsEligibilityInformation": {"redirect": {"anywhere": true, "top10": true, "isKiwiAvailable": true}, "guarantee": {"anywhere": false, "top10": false}, "combination": {"anywhere": false, "top10": false}}, "kiwiBasicEligibility": {"anywhere": true, "top10": true}, "topThreeResortingOccurred": false, "carriersDeeplinkEligibility": null, "responseContainsKayakItinerary": false, "paretoABTestEligible": null}, "carriers": [{"code": "NH", "id": "Carrier:99", "name": "All Nippon Airways"}, {"code": "VN", "id": "Carrier:611", "name": "Vietnam Airlines"}, {"code": "WY", "id": "Carrier:473", "name": "Oman Air"}, {"code": "FZ", "id": "Carrier:27", "name": "Fly Dubai"}, {"code": "CA", "id": "Carrier:151", "name": "Air China"}, {"code": "FR", "id": "Carrier:11", "name": "<PERSON><PERSON>"}, {"code": "BJ", "id": "Carrier:42", "name": "NouvelAir"}, {"code": "LM", "id": "Carrier:416", "name": "LoganAir"}, {"code": "TR", "id": "Carrier:577", "name": "<PERSON>oot"}, {"code": "AI", "id": "Carrier:82", "name": "Air India Limited"}, {"code": "BT", "id": "Carrier:22", "name": "airBaltic"}, {"code": "JD", "id": "Carrier:648", "name": "Beijing Capital Airlines"}, {"code": "KE", "id": "Carrier:399", "name": "Korean Air"}, {"code": "AF", "id": "Carrier:70", "name": "Air France"}, {"code": "QV", "id": "Carrier:412", "name": "Lao Airlines"}, {"code": "X1", "id": "Carrier:2388", "name": "Hahn Air Technologies"}, {"code": "EY", "id": "Carrier:296", "name": "Etihad Airways"}, {"code": "LX", "id": "Carrier:541", "name": "Swiss International Air Lines"}, {"code": "PR", "id": "Carrier:492", "name": "Philippine Airlines"}, {"code": "ZH", "id": "Carrier:550", "name": "Shenzhen Airlines"}, {"code": "W4", "id": "Carrier:820", "name": "Wizz Air Malta"}, {"code": "PG", "id": "Carrier:211", "name": "Bangkok Airways"}, {"code": "UA", "id": "Carrier:601", "name": "United Airlines"}, {"code": "6H", "id": "Carrier:371", "name": "<PERSON><PERSON><PERSON>"}, {"code": "NS", "id": "Carrier:793", "name": "Hebei Airlines"}, {"code": "TK", "id": "Carrier:581", "name": "Turkish Airlines"}, {"code": "7C", "id": "Carrier:380", "name": "Je<PERSON>"}, {"code": "9C", "id": "Carrier:785", "name": "Spring Airlines"}, {"code": "LA", "id": "Carrier:405", "name": "LATAM Airlines"}, {"code": "G9", "id": "Carrier:26", "name": "Air Arabia"}, {"code": "XY", "id": "Carrier:468", "name": "flynas"}, {"code": "LG", "id": "Carrier:422", "name": "Luxair"}, {"code": "GQ", "id": "Carrier:1157", "name": "SKY express"}, {"code": "BG", "id": "Carrier:208", "name": "Biman Bangladesh Airlines"}, {"code": "TW", "id": "Carrier:816", "name": "Tway Airlines"}, {"code": "D8", "id": "Carrier:276", "name": "Norwegian Air Sweden"}, {"code": "EK", "id": "Carrier:291", "name": "Emirates"}, {"code": "KN", "id": "Carrier:694", "name": "China United"}, {"code": "HY", "id": "Carrier:606", "name": "Uzbekistan Airways"}, {"code": "QF", "id": "Carrier:501", "name": "Qantas"}, {"code": "AZ", "id": "Carrier:138", "name": "ITA Airways"}, {"code": "BA", "id": "Carrier:207", "name": "British Airways"}, {"code": "IX", "id": "Carrier:134", "name": "Air India Express"}, {"code": "ME", "id": "Carrier:445", "name": "Middle East Airlines"}, {"code": "GJ", "id": "Carrier:299", "name": "Loong Air"}, {"code": "FI", "id": "Carrier:355", "name": "Icelandair"}, {"code": "SN", "id": "Carrier:223", "name": "Brussels Airlines"}, {"code": "TO", "id": "Carrier:654", "name": "Transavia France"}, {"code": "RO", "id": "Carrier:597", "name": "<PERSON><PERSON>"}, {"code": "SV", "id": "Carrier:538", "name": "Saudi Arabian Airlines"}, {"code": "LY", "id": "Carrier:289", "name": "El Al Israel Airlines"}, {"code": "F3", "id": "Carrier:1018", "name": "Flyadeal"}, {"code": "KQ", "id": "Carrier:395", "name": "Kenya Airways"}, {"code": "J2", "id": "Carrier:79", "name": "Azerbaijan Airlines"}, {"code": "5J", "id": "Carrier:237", "name": "Cebu Pacific"}, {"code": "FB", "id": "Carrier:225", "name": "Bulgaria Air"}, {"code": "UO", "id": "Carrier:349", "name": "Hong Kong Express Airways"}, {"code": "OM", "id": "Carrier:426", "name": "MIAT Mongolian Airlines"}, {"code": "VF", "id": "Carrier:610", "name": "AJet"}, {"code": "FM", "id": "Carrier:549", "name": "Shanghai Airlines"}, {"code": "DE", "id": "Carrier:28", "name": "Condor"}, {"code": "AH", "id": "Carrier:156", "name": "Air Algerie"}, {"code": "CX", "id": "Carrier:235", "name": "Cathay Pacific"}, {"code": "HV", "id": "Carrier:4", "name": "Transavia"}, {"code": "EW", "id": "Carrier:301", "name": "Eurowings"}, {"code": "MU", "id": "Carrier:241", "name": "China Eastern Airlines"}, {"code": "DY", "id": "Carrier:17", "name": "Norwegian Air Shuttle"}, {"code": "W6", "id": "Carrier:12", "name": "Wizz Air"}, {"code": "WB", "id": "Carrier:513", "name": "Rwandair"}, {"code": "TU", "id": "Carrier:573", "name": "<PERSON><PERSON><PERSON>"}, {"code": "AM", "id": "Carrier:98", "name": "AeroMéxico"}, {"code": "A3", "id": "Carrier:39", "name": "Aegean"}, {"code": "VY", "id": "Carrier:9", "name": "<PERSON><PERSON>ing"}, {"code": "MH", "id": "Carrier:430", "name": "Malaysia Airlines"}, {"code": "SK", "id": "Carrier:519", "name": "SAS"}, {"code": "M0", "id": "Carrier:1040", "name": "Aero Mongolia"}, {"code": "EI", "id": "Carrier:10", "name": "<PERSON><PERSON>"}, {"code": "TP", "id": "Carrier:572", "name": "TAP Portugal"}, {"code": "ZE", "id": "Carrier:670", "name": "Eastar Jet"}, {"code": "EN", "id": "Carrier:158", "name": "Air Dolomiti"}, {"code": "IB", "id": "Carrier:352", "name": "Iberia Airlines"}, {"code": "AY", "id": "Carrier:307", "name": "<PERSON><PERSON>"}, {"code": "UX", "id": "Carrier:60", "name": "Air Europa"}, {"code": "HB", "id": "Carrier:2721", "name": "Greater Bay Airlines"}, {"code": "JU", "id": "Carrier:378", "name": "Air Serbia"}, {"code": "NX", "id": "Carrier:96", "name": "Air Macau"}, {"code": "AT", "id": "Carrier:510", "name": "Royal Air Maroc"}, {"code": "LO", "id": "Carrier:409", "name": "LOT Polish Airlines"}, {"code": "MS", "id": "Carrier:288", "name": "Egyptair"}, {"code": "VJ", "id": "Carrier:642", "name": "VietJet Air"}, {"code": "CZ", "id": "Carrier:242", "name": "China Southern Airlines"}, {"code": "LH", "id": "Carrier:419", "name": "Lufthansa"}, {"code": "FD", "id": "Carrier:580", "name": "Thai AirAsia"}, {"code": "TG", "id": "Carrier:579", "name": "Thai Airways"}, {"code": "OZ", "id": "Carrier:51", "name": "Asiana Airlines"}, {"code": "XJ", "id": "Carrier:443", "name": "Thai AirAsia X"}, {"code": "OU", "id": "Carrier:265", "name": "Croatia Airlines"}, {"code": "KU", "id": "Carrier:402", "name": "Kuwait Airways"}, {"code": "ET", "id": "Carrier:295", "name": "Ethiopian Airlines"}, {"code": "SG", "id": "Carrier:524", "name": "Spicejet"}, {"code": "HX", "id": "Carrier:348", "name": "Hong Kong Airlines"}, {"code": "GF", "id": "Carrier:333", "name": "Gulf Air Bahrain"}, {"code": "HO", "id": "Carrier:388", "name": "Juneyao Airlines"}, {"code": "3K", "id": "Carrier:383", "name": "Jetstar Asia Airways"}, {"code": "SM", "id": "Carrier:737", "name": "Air Cairo"}, {"code": "RJ", "id": "Carrier:512", "name": "Royal Jordanian"}, {"code": "VS", "id": "Carrier:618", "name": "Virgin Atlantic Airways"}, {"code": "HU", "id": "Carrier:335", "name": "Hainan Airlines"}, {"code": "QR", "id": "Carrier:502", "name": "Qatar Airways"}, {"code": "MR", "id": "Carrier:756", "name": "<PERSON><PERSON><PERSON> Air"}, {"code": "S4", "id": "Carrier:516", "name": "SATA Azores Airlines"}, {"code": "PC", "id": "Carrier:16", "name": "Pegasus"}, {"code": "OV", "id": "Carrier:294", "name": "SalamAir"}, {"code": "3U", "id": "Carrier:548", "name": "Sichuan Airlines"}, {"code": "UL", "id": "Carrier:522", "name": "SriLankan Airlines"}, {"code": "CI", "id": "Carrier:240", "name": "China Airlines"}, {"code": "OD", "id": "Carrier:888", "name": "Batik Air Malaysia"}, {"code": "KL", "id": "Carrier:391", "name": "KLM Royal Dutch Airlines"}, {"code": "DV", "id": "Carrier:569", "name": "Scat Airlines"}, {"code": "KM", "id": "Carrier:87", "name": "KM Malta Airlines"}, {"code": "VZ", "id": "Carrier:454", "name": "Thai Vietjet"}, {"code": "MF", "id": "Carrier:638", "name": "Xiamen Airlines"}, {"code": "FJ", "id": "Carrier:162", "name": "Fiji Airways"}, {"code": "A9", "id": "Carrier:325", "name": "Georgian Airways"}, {"code": "BR", "id": "Carrier:282", "name": "EVA Air"}, {"code": "6E", "id": "Carrier:357", "name": "IndiGo Airlines"}, {"code": "U2", "id": "Carrier:3", "name": "easyJet"}, {"code": "D7", "id": "Carrier:314", "name": "AirAsia X"}, {"code": "HH", "id": "Carrier:788", "name": "Qanot Shark"}, {"code": "OS", "id": "Carrier:121", "name": "Austrian Airlines"}, {"code": "SQ", "id": "Carrier:528", "name": "Singapore Airlines"}, {"code": "JX", "id": "Carrier:818", "name": "Starlux Airlines"}, {"code": "KC", "id": "Carrier:174", "name": "Air Astana"}], "stopoverCountries": [], "inboundDays": [], "outboundDays": ["THURSDAY"], "travelTips": [], "topResults": {"best": {"__typename": "ItineraryOneWay", "duration": 36900, "price": {"amount": "3805"}, "id": "ItineraryOneWay:****************************************************************************************************************************************************************"}, "cheapest": {"__typename": "ItineraryOneWay", "duration": 36900, "price": {"amount": "3805"}, "id": "ItineraryOneWay:****************************************************************************************************************************************************************"}, "fastest": {"__typename": "ItineraryOneWay", "duration": 36900, "price": {"amount": "3805"}, "id": "ItineraryOneWay:****************************************************************************************************************************************************************"}, "sourceTakeoffAsc": {"__typename": "ItineraryOneWay", "duration": 36900, "price": {"amount": "3805"}, "id": "ItineraryOneWay:****************************************************************************************************************************************************************"}, "destinationLandingAsc": {"__typename": "ItineraryOneWay", "duration": 36900, "price": {"amount": "3805"}, "id": "ItineraryOneWay:****************************************************************************************************************************************************************"}}, "priceAlertExists": null, "existingPriceAlert": null, "searchFingerprint": "************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "hasMorePending": false, "priceAlertsTopResults": {"best": {"price": {"amount": "4209"}}, "cheapest": {"price": {"amount": "4209"}}, "fastest": {"price": {"amount": "4209"}}, "sourceTakeoffAsc": {"price": {"amount": "4209"}}, "destinationLandingAsc": {"price": {"amount": "4209"}}}, "itinerariesCount": 2, "missingProviders": [], "statusPerProvider": [{"provider": {"id": "ContentProvider:KIWI"}, "errorHappened": false, "errorMessage": null}], "hasTier1MarketItineraries": false, "sharedItinerary": null, "kayakEligibilityTest": {"containsKayakWithNewRules": null, "containsKayakWithCurrentRules": false, "containsKayakAirlinesWithNewRules": null}}, "itineraries": [{"__typename": "ItineraryOneWay", "__isItinerary": "ItineraryOneWay", "id": "ItineraryOneWay:****************************************************************************************************************************************************************", "shareId": "********************************************************************************************************************************************************************************", "price": {"amount": "3805", "priceBeforeDiscount": "3805"}, "priceEur": {"amount": "466.059307"}, "provider": {"name": "Kiwi.com", "code": "KIWI-BASIC", "hasHighProbabilityOfPriceChange": false, "contentProvider": null, "id": "ItineraryProvider:KIWI-BASIC"}, "bagsInfo": {"includedCheckedBags": 0, "includedHandBags": 0, "hasNoBaggageSupported": true, "hasNoCheckedBaggage": false, "checkedBagTiers": [], "handBagTiers": [], "includedPersonalItem": 1, "personalItemTiers": [{"tierPrice": {"amount": "0"}, "bags": [{"weight": {"value": 8}, "height": {"value": 30}, "width": {"value": 15}, "length": {"value": 40}}]}]}, "bookingOptions": {"edges": [{"node": {"token": "H923yrM4tMlTmdo-4a3WdtynEwwmW-nY8rca7XmPzqUF38pESqisp4ACmFX89r66LpNkblHi0l1scDtY92VQVx_jKQgHvlfn_Ow4jwRoN3q0R78kXrIW9uhn3__vixg170hPYyZZ7ZRxUJcWWG3kptvHOkxtweuFPhydx-rYggB-nAxrf-aIAqVtAufWxOz2O6Iw-c3-GkFk5W1uc4xZwT-5TRBgK8zHMXc5dQHDR7mc_HpwfrY1q7wH1UlrYQfhFwzY4AdZdfWUSYaND1Mb70PjEzYj29Gpd-jYNxvtHyVPqkZGpj7emKl0xz4dAZsQAlLOoKAXJtkcJCGc0SXsWkp9EvSoET27dYQrvajp27gHPK-UwMR1eIF3-tj4S2LwIiWU4FgZZNpAZNzYrkwBeuhzX9DPXP_sORLz3pTonVJ6MHPt68Er_wtmlp2dkIaCQaqsgQnzjhfZIEDECfff86stoblzC0hGZTSr0gymmvv4N_kRhnocm6pIv7vqSHpcFNI3ZIFB1rxK9Or2PYKfiMYDfd1Z5iZxQ6G0vwQyFB-KBcN2Awqhl2NE0EPzafpGQ84QKdhFeGOby1nDU-56K2_UqgJN-KAe3kcVA7nS2Yiw-lJCiH7fIVvYBv3YOngOQmPgJ-9Q8dwVRPGinWNp3H2QFtD35GgsC1ZIgIN7_-vKdHeGof6vXyYdg6UKBOFy_x56kil7ly9gbP_dtnl3_NUk02tz0H_miecR2z51zSX7sQ8WWa54uSFNWPIgOZIQ3o6OPQvTrWftg0Hh2RYpRpYtDtzqkc48kA2-CQZJWFw8_0IKPKMIDTTgtbVUtTEyjGjabHaR4GuhLShEs0mLdOdFlBMqDMpBKMNi9vzwj5ItzuKe1H1m7EM98QI989LciOFw-C15baGr-AeVV_W64BoqUFLj3wcmP-c33fC-XgHMwPC_y7xaHojtMGPaqSOy11i0gqx92OsxsZa9He241sqi7IHfXvrcrnkMTSD0HvbgbwScRi6AdYz-zckGtiZ9F9-wnYHOCcQ5zuuv35pTMeY1302rBxpyToeXlTVxeWZmPPIh7sUSXO-hMa81HgudS", "bookingUrl": "/cn/booking/?direct=true&locale=cn&currency=cny&passengers=1-0-0&token=H923yrM4tMlTmdo-4a3WdtynEwwmW-nY8rca7XmPzqUF38pESqisp4ACmFX89r66LpNkblHi0l1scDtY92VQVx_jKQgHvlfn_Ow4jwRoN3q0R78kXrIW9uhn3__vixg170hPYyZZ7ZRxUJcWWG3kptvHOkxtweuFPhydx-rYggB-nAxrf-aIAqVtAufWxOz2O6Iw-c3-GkFk5W1uc4xZwT-5TRBgK8zHMXc5dQHDR7mc_HpwfrY1q7wH1UlrYQfhFwzY4AdZdfWUSYaND1Mb70PjEzYj29Gpd-jYNxvtHyVPqkZGpj7emKl0xz4dAZsQAlLOoKAXJtkcJCGc0SXsWkp9EvSoET27dYQrvajp27gHPK-UwMR1eIF3-tj4S2LwIiWU4FgZZNpAZNzYrkwBeuhzX9DPXP_sORLz3pTonVJ6MHPt68Er_wtmlp2dkIaCQaqsgQnzjhfZIEDECfff86stoblzC0hGZTSr0gymmvv4N_kRhnocm6pIv7vqSHpcFNI3ZIFB1rxK9Or2PYKfiMYDfd1Z5iZxQ6G0vwQyFB-KBcN2Awqhl2NE0EPzafpGQ84QKdhFeGOby1nDU-56K2_UqgJN-KAe3kcVA7nS2Yiw-lJCiH7fIVvYBv3YOngOQmPgJ-9Q8dwVRPGinWNp3H2QFtD35GgsC1ZIgIN7_-vKdHeGof6vXyYdg6UKBOFy_x56kil7ly9gbP_dtnl3_NUk02tz0H_miecR2z51zSX7sQ8WWa54uSFNWPIgOZIQ3o6OPQvTrWftg0Hh2RYpRpYtDtzqkc48kA2-CQZJWFw8_0IKPKMIDTTgtbVUtTEyjGjabHaR4GuhLShEs0mLdOdFlBMqDMpBKMNi9vzwj5ItzuKe1H1m7EM98QI989LciOFw-C15baGr-AeVV_W64BoqUFLj3wcmP-c33fC-XgHMwPC_y7xaHojtMGPaqSOy11i0gqx92OsxsZa9He241sqi7IHfXvrcrnkMTSD0HvbgbwScRi6AdYz-zckGtiZ9F9-wnYHOCcQ5zuuv35pTMeY1302rBxpyToeXlTVxeWZmPPIh7sUSXO-hMa81HgudS&searchType=oneWay&searchBags=0.0", "trackingPixel": "https://www.kiwi.com/pixel?affilid=cj&currency=CNY&flightsId=0f6417874f1b00008e11ea39_0%7C0f6417874f1b00008e11ea39_1&from=LHR&lang=cn&passengers=1&searchBags=0.0&to=PKX&booking_token=H923yrM4tMlTmdo-4a3WdtynEwwmW-nY8rca7XmPzqUF38pESqisp4ACmFX89r66LpNkblHi0l1scDtY92VQVx_jKQgHvlfn_Ow4jwRoN3q0R78kXrIW9uhn3__vixg170hPYyZZ7ZRxUJcWWG3kptvHOkxtweuFPhydx-rYggB-nAxrf-aIAqVtAufWxOz2O6Iw-c3-GkFk5W1uc4xZwT-5TRBgK8zHMXc5dQHDR7mc_HpwfrY1q7wH1UlrYQfhFwzY4AdZdfWUSYaND1Mb70PjEzYj29Gpd-jYNxvtHyVPqkZGpj7emKl0xz4dAZsQAlLOoKAXJtkcJCGc0SXsWkp9EvSoET27dYQrvajp27gHPK-UwMR1eIF3-tj4S2LwIiWU4FgZZNpAZNzYrkwBeuhzX9DPXP_sORLz3pTonVJ6MHPt68Er_wtmlp2dkIaCQaqsgQnzjhfZIEDECfff86stoblzC0hGZTSr0gymmvv4N_kRhnocm6pIv7vqSHpcFNI3ZIFB1rxK9Or2PYKfiMYDfd1Z5iZxQ6G0vwQyFB-KBcN2Awqhl2NE0EPzafpGQ84QKdhFeGOby1nDU-56K2_UqgJN-KAe3kcVA7nS2Yiw-lJCiH7fIVvYBv3YOngOQmPgJ-9Q8dwVRPGinWNp3H2QFtD35GgsC1ZIgIN7_-vKdHeGof6vXyYdg6UKBOFy_x56kil7ly9gbP_dtnl3_NUk02tz0H_miecR2z51zSX7sQ8WWa54uSFNWPIgOZIQ3o6OPQvTrWftg0Hh2RYpRpYtDtzqkc48kA2-CQZJWFw8_0IKPKMIDTTgtbVUtTEyjGjabHaR4GuhLShEs0mLdOdFlBMqDMpBKMNi9vzwj5ItzuKe1H1m7EM98QI989LciOFw-C15baGr-AeVV_W64BoqUFLj3wcmP-c33fC-XgHMwPC_y7xaHojtMGPaqSOy11i0gqx92OsxsZa9He241sqi7IHfXvrcrnkMTSD0HvbgbwScRi6AdYz-zckGtiZ9F9-wnYHOCcQ5zuuv35pTMeY1302rBxpyToeXlTVxeWZmPPIh7sUSXO-hMa81HgudS", "itineraryProvider": {"code": "KIWI-BASIC", "name": "Kiwi.com", "subprovider": null, "hasHighProbabilityOfPriceChange": false, "contentProvider": null, "providerCategory": null, "id": "ItineraryProvider:KIWI-BASIC"}, "price": {"amount": "3805"}, "priceEur": {"amount": "466.06"}, "priceLocks": {"priceLocksCurr": [{"default": false, "price": {"amount": "190.25", "roundedFormattedValue": "¥190"}}, {"default": true, "price": {"amount": "380.5", "roundedFormattedValue": "¥381"}}, {"default": false, "price": {"amount": "951.25", "roundedFormattedValue": "¥951"}}], "priceLocksEur": [{"default": false, "price": {"amount": "23.3", "roundedFormattedValue": "23 €"}}, {"default": true, "price": {"amount": "46.61", "roundedFormattedValue": "47 €"}}, {"default": false, "price": {"amount": "116.51", "roundedFormattedValue": "117 €"}}]}, "kiwiProduct": "KIWI_BASIC", "disruptionTreatment": "NO_PROTECTION", "usRulesApply": false}}, {"node": {"token": "Hg9_dUtG1p6mRqPJF_D5pTV65ch7fCq3t41VVd5chhuHzYNaOKdi9BvvevghiMZCbEVxfcMHGFPEr8c0zfLo-3sC1uKTG3mJH3TOIJXJh_0-YKhb_htHPXfLWrnKLQxXZ8eKElW_heORJnDAh7yXjasSdQYARcxkXDoxSViyqCIwoGpUBzWOtP-TpjOiTb9dIwHDTiUHQZpNfFDJTrJj1wZv1dTxmRBKgkdl4RDqExNFlXSukNa9Kz-vbPwdKBQ11e1Zi1Lh7BD7sQDbtY9U06BSsssyEWAD0VR5LTMW4pbxqp_j5AAVlKLZKHPPoC6m9UCes_q0EGgolqpP1J_HL2W11SD-0kqpPJg6r5xfeBuCVdhKp8kC1rQDE-Z1jYd3YWiiwKYynrV-EMbRnnmOkLA3Lusou4Wx1_UIIqqqk07ZTaY2e7MYJ7v3Syxci_MoAeUdapnaxQz0_9dmz9K_kxtg50fHLs9T0G3GgLEtQeJmwjcHV0H1lIOtid1-fWR87jRu1t2Qa06ZgeLQigMZyVh8_HL136X9yEkp53LxJcYvfU75LpeJfdxVzJL04kmrl62dBH6Odg3vDOmlSDh3vn6ZR_ARjs8HuGur9JVjdE7PG_yYSPlJ3zrMAzoOjZdB7pkORJrtWfb4rlmdBAYB_urH2dtRsf2XinLCidGWR_FfB7lse-R3DLMLxj_2cTXv9u2yLBEoRCUFEX0ka_C86ayxKctTVjhhtGxYOkoJIxwNgsywaALnMlNl5Q5G3i3zFG4eHGKMhgyj19P075ZUMXHX1jhujh69rQnOVjSMOuK6O0HgTEUVIka51o7c_Sj70PwFJhbZx9OJmcUB0KD3f9S0Exydv_gCugGcP1E5xVFKGCVK7gsypH_CDNNQ3l81r8dg2E4-F2y0KRlZGBZXlgJC4gTMu8bnlsm5t-r-IfDvQxXBolGg9RzrZInZeRbPL9h-_JxggMpH1GVFOBT0zgmTvwrParxzRI70FEz8dHAgREjXwRKZuhwqr6yIVWTHKQAAi3Jk513axLBng4uUIW9ZTOA-BcD3obS6tKzM9NI2Ks_Gkozh2W4UoMeRlKOOqKI3tZilhnNHM1oz5_LWL60dN5_jJ0a850muH9SMb_vjYpp_O5-jefUS69DvZaDa2", "bookingUrl": "/cn/booking/?direct=true&locale=cn&currency=cny&passengers=1-0-0&token=Hg9_dUtG1p6mRqPJF_D5pTV65ch7fCq3t41VVd5chhuHzYNaOKdi9BvvevghiMZCbEVxfcMHGFPEr8c0zfLo-3sC1uKTG3mJH3TOIJXJh_0-YKhb_htHPXfLWrnKLQxXZ8eKElW_heORJnDAh7yXjasSdQYARcxkXDoxSViyqCIwoGpUBzWOtP-TpjOiTb9dIwHDTiUHQZpNfFDJTrJj1wZv1dTxmRBKgkdl4RDqExNFlXSukNa9Kz-vbPwdKBQ11e1Zi1Lh7BD7sQDbtY9U06BSsssyEWAD0VR5LTMW4pbxqp_j5AAVlKLZKHPPoC6m9UCes_q0EGgolqpP1J_HL2W11SD-0kqpPJg6r5xfeBuCVdhKp8kC1rQDE-Z1jYd3YWiiwKYynrV-EMbRnnmOkLA3Lusou4Wx1_UIIqqqk07ZTaY2e7MYJ7v3Syxci_MoAeUdapnaxQz0_9dmz9K_kxtg50fHLs9T0G3GgLEtQeJmwjcHV0H1lIOtid1-fWR87jRu1t2Qa06ZgeLQigMZyVh8_HL136X9yEkp53LxJcYvfU75LpeJfdxVzJL04kmrl62dBH6Odg3vDOmlSDh3vn6ZR_ARjs8HuGur9JVjdE7PG_yYSPlJ3zrMAzoOjZdB7pkORJrtWfb4rlmdBAYB_urH2dtRsf2XinLCidGWR_FfB7lse-R3DLMLxj_2cTXv9u2yLBEoRCUFEX0ka_C86ayxKctTVjhhtGxYOkoJIxwNgsywaALnMlNl5Q5G3i3zFG4eHGKMhgyj19P075ZUMXHX1jhujh69rQnOVjSMOuK6O0HgTEUVIka51o7c_Sj70PwFJhbZx9OJmcUB0KD3f9S0Exydv_gCugGcP1E5xVFKGCVK7gsypH_CDNNQ3l81r8dg2E4-F2y0KRlZGBZXlgJC4gTMu8bnlsm5t-r-IfDvQxXBolGg9RzrZInZeRbPL9h-_JxggMpH1GVFOBT0zgmTvwrParxzRI70FEz8dHAgREjXwRKZuhwqr6yIVWTHKQAAi3Jk513axLBng4uUIW9ZTOA-BcD3obS6tKzM9NI2Ks_Gkozh2W4UoMeRlKOOqKI3tZilhnNHM1oz5_LWL60dN5_jJ0a850muH9SMb_vjYpp_O5-jefUS69DvZaDa2&searchType=oneWay&searchBags=0.0", "trackingPixel": "https://www.kiwi.com/pixel?affilid=cj&currency=CNY&flightsId=0f6417874f1b00008e11ea39_0%7C0f6417874f1b00008e11ea39_1&from=LHR&lang=cn&passengers=1&searchBags=0.0&to=PKX&booking_token=Hg9_dUtG1p6mRqPJF_D5pTV65ch7fCq3t41VVd5chhuHzYNaOKdi9BvvevghiMZCbEVxfcMHGFPEr8c0zfLo-3sC1uKTG3mJH3TOIJXJh_0-YKhb_htHPXfLWrnKLQxXZ8eKElW_heORJnDAh7yXjasSdQYARcxkXDoxSViyqCIwoGpUBzWOtP-TpjOiTb9dIwHDTiUHQZpNfFDJTrJj1wZv1dTxmRBKgkdl4RDqExNFlXSukNa9Kz-vbPwdKBQ11e1Zi1Lh7BD7sQDbtY9U06BSsssyEWAD0VR5LTMW4pbxqp_j5AAVlKLZKHPPoC6m9UCes_q0EGgolqpP1J_HL2W11SD-0kqpPJg6r5xfeBuCVdhKp8kC1rQDE-Z1jYd3YWiiwKYynrV-EMbRnnmOkLA3Lusou4Wx1_UIIqqqk07ZTaY2e7MYJ7v3Syxci_MoAeUdapnaxQz0_9dmz9K_kxtg50fHLs9T0G3GgLEtQeJmwjcHV0H1lIOtid1-fWR87jRu1t2Qa06ZgeLQigMZyVh8_HL136X9yEkp53LxJcYvfU75LpeJfdxVzJL04kmrl62dBH6Odg3vDOmlSDh3vn6ZR_ARjs8HuGur9JVjdE7PG_yYSPlJ3zrMAzoOjZdB7pkORJrtWfb4rlmdBAYB_urH2dtRsf2XinLCidGWR_FfB7lse-R3DLMLxj_2cTXv9u2yLBEoRCUFEX0ka_C86ayxKctTVjhhtGxYOkoJIxwNgsywaALnMlNl5Q5G3i3zFG4eHGKMhgyj19P075ZUMXHX1jhujh69rQnOVjSMOuK6O0HgTEUVIka51o7c_Sj70PwFJhbZx9OJmcUB0KD3f9S0Exydv_gCugGcP1E5xVFKGCVK7gsypH_CDNNQ3l81r8dg2E4-F2y0KRlZGBZXlgJC4gTMu8bnlsm5t-r-IfDvQxXBolGg9RzrZInZeRbPL9h-_JxggMpH1GVFOBT0zgmTvwrParxzRI70FEz8dHAgREjXwRKZuhwqr6yIVWTHKQAAi3Jk513axLBng4uUIW9ZTOA-BcD3obS6tKzM9NI2Ks_Gkozh2W4UoMeRlKOOqKI3tZilhnNHM1oz5_LWL60dN5_jJ0a850muH9SMb_vjYpp_O5-jefUS69DvZaDa2", "itineraryProvider": {"code": "KIWI", "name": "Kiwi.com", "subprovider": null, "hasHighProbabilityOfPriceChange": false, "contentProvider": {"code": "KIWI"}, "providerCategory": null, "id": "ItineraryProvider:KIWI"}, "price": {"amount": "4209"}, "priceEur": {"amount": "515.54"}, "priceLocks": {"priceLocksCurr": [{"default": false, "price": {"amount": "210.45", "roundedFormattedValue": "¥210"}}, {"default": true, "price": {"amount": "420.9", "roundedFormattedValue": "¥421"}}, {"default": false, "price": {"amount": "1052.25", "roundedFormattedValue": "¥1,052"}}], "priceLocksEur": [{"default": false, "price": {"amount": "25.78", "roundedFormattedValue": "26 €"}}, {"default": true, "price": {"amount": "51.55", "roundedFormattedValue": "52 €"}}, {"default": false, "price": {"amount": "128.89", "roundedFormattedValue": "129 €"}}]}, "kiwiProduct": "KIWI_BENEFITS", "disruptionTreatment": "DISRUPTION_PROTECTION", "usRulesApply": null}}]}, "travelHack": {"isTrueHiddenCity": true, "isVirtualInterlining": false, "isThrowawayTicket": false}, "duration": 36900, "pnrCount": 1, "sector": {"id": "Sector:adcfbe213ec4e887f1b4d0174d49824958a2cc45~adcfbe213ec4e887f1b4d0174d49824958a2cc45", "sectorSegments": [{"segment": {"source": {"station": {"code": "LHR", "type": "AIRPORT", "city": {"name": "伦敦", "id": "City:london_gb", "legacyId": "london_gb"}, "id": "Station:airport:LHR", "legacyId": "LHR", "name": "伦敦希思罗机场", "gps": {"lat": 51.4775, "lng": -0.4613889}, "country": {"code": "GB", "id": "Country:GB"}}, "localTime": "2025-06-12T21:00:00", "utcTimeIso": "2025-06-12T20:00:00Z"}, "destination": {"station": {"code": "PKX", "type": "AIRPORT", "city": {"name": "北京市", "id": "City:beijing_cn", "legacyId": "beijing_cn"}, "id": "Station:airport:PKX", "legacyId": "PKX", "name": "北京大兴国际机场", "gps": {"lat": 39.509167, "lng": 116.410556}, "country": {"code": "CN", "id": "Country:CN"}}, "localTime": "2025-06-13T14:15:00", "utcTimeIso": "2025-06-13T06:15:00Z"}, "id": "Segment:adcfbe213ec4e887f1b4d0174d49824958a2cc45", "duration": 36900, "type": "FLIGHT", "code": "674", "carrier": {"id": "Carrier:242", "name": "China Southern Airlines", "code": "CZ"}, "operatingCarrier": {"id": "Carrier:242", "name": "China Southern Airlines", "code": "CZ"}, "cabinClass": "ECONOMY", "hiddenDestination": {"code": "HAK", "name": "海口美兰国际机场", "city": {"name": "海口市", "id": "City:haikou_cn"}, "country": {"name": "中国", "id": "Country:CN"}, "id": "Station:airport:HAK"}, "throwawayDestination": null}, "guarantee": null, "layover": null}], "duration": 36900}, "legacyId": "0f6417874f1b00008e11ea39_0", "lastAvailable": {"seatsLeft": 7}, "isRyanair": false, "benefitsData": {"automaticCheckinAvailable": false, "instantChatSupportAvailable": true, "disruptionProtectionAvailable": true, "guaranteeAvailable": false, "guaranteeFee": null, "guaranteeFeeEur": null, "searchReferencePrice": {"roundedAmount": "3805"}}, "isAirBaggageBundleEligible": null, "testEligibilityInformation": {"paretoABTestNewItinerary": null}}, {"__typename": "ItineraryOneWay", "__isItinerary": "ItineraryOneWay", "id": "ItineraryOneWay:************************************************************************************************************************************************************", "shareId": "****************************************************************************************************************************************************************************", "price": {"amount": "4319", "priceBeforeDiscount": "4319"}, "priceEur": {"amount": "529.017122"}, "provider": {"name": "Kiwi.com", "code": "KIWI-BASIC", "hasHighProbabilityOfPriceChange": false, "contentProvider": null, "id": "ItineraryProvider:KIWI-BASIC"}, "bagsInfo": {"includedCheckedBags": 1, "includedHandBags": 1, "hasNoBaggageSupported": false, "hasNoCheckedBaggage": false, "checkedBagTiers": [{"tierPrice": {"amount": "0"}, "bags": [{"weight": {"value": 23}}]}], "handBagTiers": [{"tierPrice": {"amount": "0"}, "bags": [{"weight": {"value": 8}}]}], "includedPersonalItem": 1, "personalItemTiers": [{"tierPrice": {"amount": "0"}, "bags": [{"weight": {"value": 8}, "height": {"value": 30}, "width": {"value": 15}, "length": {"value": 40}}]}]}, "bookingOptions": {"edges": [{"node": {"token": "HPejXCcqy1yYkUnSavSHqig1Epgz53YKjfK07SGo_at4ziI36jJBOMjyydoHRFuH6q8tYU8IGIWb-5B5Fz54QSqIiiTDhZD1H6MwRvFbI5VlUnOqU79GSJSdJZbb7VZfsWxDjujcx4eulscVDYA-ujdXbtNK8Rd530w8rLkdsZnju76EEYlsPqJaCOjNITI_31jQlq1q_L6ZaRwMaJzhg94qHVUY-Z1bYH1FhdbaZYnHiatBntidGoqM7Cu5YteqWEP4qPvAnLLRmkGLOF1jw7XRypLKTJnThPaTy_1sUX-hgbbkScGchp35RYqJSqMWP58vqcc8ELPWu-nnpSwNG6HIBLqX53HrJXLDkSXrn5c-LwuD8-4YXzUah9b2Ewy7SGIwQNykJfoWK9WujOdj71LS9LpG5CyDv2tmvgGUO9DsdamrJ2AXMroYNHOFAIwehQkTO18RqOsQF8HIj-3hOEVqQ_swyhjFJ4trZkiDUNd6fpnrAFbrjK28Ry5vBs486atzZlNXAsZwpEC0KYO6-l3NFH8LlrjqaEUjwOW-Oq7XtrN_U_PtwvthLSmwXRZw-XY_kGgh2aD1E_wk1ULAi2OKzueoCoQzRLLmhYOOHeQJVtu0md7-YZQykhh8M9vKrHUx98eoxk9PFwUPQsABFI3Dv-ozf6OykzYI2PhI0yiFgyb83Pl3TIfgxOvtdXl-l2UnHsBc8bOjMdAqc--smYvehDKEsqRuxrK5jLJErSOuuUnJkB8dgYaNT2MHJJB1NVUwglLvLdRueFUuSLkfOIey6tHL9Bu73jlmj9v5MAkFR8mKweB1I_4apyJNg7611R2GtjUwWPDF1bBae39itmjYjbtPlbiifTo82Bxv33UQRRXmNwxQQTs8J63Vuqs8prc4QImo2e15y8SF42uj6p_B7kVN0wjX6N8TYBu_DhKJezyRH8-ZZ0FNoUGDGBvadyZPLqvoXdHdSA1VL9FFU9F6ZPGdbUhIfotLvErPcDeEDeqEmJvlisBhruXMKF-Xk3b9lF38sfEhAiz-J6C4p3w==", "bookingUrl": "/cn/booking/?direct=true&locale=cn&currency=cny&passengers=1-0-0&token=HPejXCcqy1yYkUnSavSHqig1Epgz53YKjfK07SGo_at4ziI36jJBOMjyydoHRFuH6q8tYU8IGIWb-5B5Fz54QSqIiiTDhZD1H6MwRvFbI5VlUnOqU79GSJSdJZbb7VZfsWxDjujcx4eulscVDYA-ujdXbtNK8Rd530w8rLkdsZnju76EEYlsPqJaCOjNITI_31jQlq1q_L6ZaRwMaJzhg94qHVUY-Z1bYH1FhdbaZYnHiatBntidGoqM7Cu5YteqWEP4qPvAnLLRmkGLOF1jw7XRypLKTJnThPaTy_1sUX-hgbbkScGchp35RYqJSqMWP58vqcc8ELPWu-nnpSwNG6HIBLqX53HrJXLDkSXrn5c-LwuD8-4YXzUah9b2Ewy7SGIwQNykJfoWK9WujOdj71LS9LpG5CyDv2tmvgGUO9DsdamrJ2AXMroYNHOFAIwehQkTO18RqOsQF8HIj-3hOEVqQ_swyhjFJ4trZkiDUNd6fpnrAFbrjK28Ry5vBs486atzZlNXAsZwpEC0KYO6-l3NFH8LlrjqaEUjwOW-Oq7XtrN_U_PtwvthLSmwXRZw-XY_kGgh2aD1E_wk1ULAi2OKzueoCoQzRLLmhYOOHeQJVtu0md7-YZQykhh8M9vKrHUx98eoxk9PFwUPQsABFI3Dv-ozf6OykzYI2PhI0yiFgyb83Pl3TIfgxOvtdXl-l2UnHsBc8bOjMdAqc--smYvehDKEsqRuxrK5jLJErSOuuUnJkB8dgYaNT2MHJJB1NVUwglLvLdRueFUuSLkfOIey6tHL9Bu73jlmj9v5MAkFR8mKweB1I_4apyJNg7611R2GtjUwWPDF1bBae39itmjYjbtPlbiifTo82Bxv33UQRRXmNwxQQTs8J63Vuqs8prc4QImo2e15y8SF42uj6p_B7kVN0wjX6N8TYBu_DhKJezyRH8-ZZ0FNoUGDGBvadyZPLqvoXdHdSA1VL9FFU9F6ZPGdbUhIfotLvErPcDeEDeqEmJvlisBhruXMKF-Xk3b9lF38sfEhAiz-J6C4p3w%3D%3D&searchType=oneWay&searchBags=0.0", "trackingPixel": "https://www.kiwi.com/pixel?affilid=cj&currency=CNY&flightsId=0f640fa04f1b0000902a7bb2_0&from=LHR&lang=cn&passengers=1&searchBags=0.0&to=PKX&booking_token=HPejXCcqy1yYkUnSavSHqig1Epgz53YKjfK07SGo_at4ziI36jJBOMjyydoHRFuH6q8tYU8IGIWb-5B5Fz54QSqIiiTDhZD1H6MwRvFbI5VlUnOqU79GSJSdJZbb7VZfsWxDjujcx4eulscVDYA-ujdXbtNK8Rd530w8rLkdsZnju76EEYlsPqJaCOjNITI_31jQlq1q_L6ZaRwMaJzhg94qHVUY-Z1bYH1FhdbaZYnHiatBntidGoqM7Cu5YteqWEP4qPvAnLLRmkGLOF1jw7XRypLKTJnThPaTy_1sUX-hgbbkScGchp35RYqJSqMWP58vqcc8ELPWu-nnpSwNG6HIBLqX53HrJXLDkSXrn5c-LwuD8-4YXzUah9b2Ewy7SGIwQNykJfoWK9WujOdj71LS9LpG5CyDv2tmvgGUO9DsdamrJ2AXMroYNHOFAIwehQkTO18RqOsQF8HIj-3hOEVqQ_swyhjFJ4trZkiDUNd6fpnrAFbrjK28Ry5vBs486atzZlNXAsZwpEC0KYO6-l3NFH8LlrjqaEUjwOW-Oq7XtrN_U_PtwvthLSmwXRZw-XY_kGgh2aD1E_wk1ULAi2OKzueoCoQzRLLmhYOOHeQJVtu0md7-YZQykhh8M9vKrHUx98eoxk9PFwUPQsABFI3Dv-ozf6OykzYI2PhI0yiFgyb83Pl3TIfgxOvtdXl-l2UnHsBc8bOjMdAqc--smYvehDKEsqRuxrK5jLJErSOuuUnJkB8dgYaNT2MHJJB1NVUwglLvLdRueFUuSLkfOIey6tHL9Bu73jlmj9v5MAkFR8mKweB1I_4apyJNg7611R2GtjUwWPDF1bBae39itmjYjbtPlbiifTo82Bxv33UQRRXmNwxQQTs8J63Vuqs8prc4QImo2e15y8SF42uj6p_B7kVN0wjX6N8TYBu_DhKJezyRH8-ZZ0FNoUGDGBvadyZPLqvoXdHdSA1VL9FFU9F6ZPGdbUhIfotLvErPcDeEDeqEmJvlisBhruXMKF-Xk3b9lF38sfEhAiz-J6C4p3w==", "itineraryProvider": {"code": "KIWI-BASIC", "name": "Kiwi.com", "subprovider": null, "hasHighProbabilityOfPriceChange": false, "contentProvider": null, "providerCategory": null, "id": "ItineraryProvider:KIWI-BASIC"}, "price": {"amount": "4319"}, "priceEur": {"amount": "529.02"}, "priceLocks": {"priceLocksCurr": [{"default": false, "price": {"amount": "215.95", "roundedFormattedValue": "¥216"}}, {"default": true, "price": {"amount": "431.9", "roundedFormattedValue": "¥432"}}, {"default": false, "price": {"amount": "1079.75", "roundedFormattedValue": "¥1,080"}}], "priceLocksEur": [{"default": false, "price": {"amount": "26.45", "roundedFormattedValue": "26 €"}}, {"default": true, "price": {"amount": "52.9", "roundedFormattedValue": "53 €"}}, {"default": false, "price": {"amount": "132.25", "roundedFormattedValue": "132 €"}}]}, "kiwiProduct": "KIWI_BASIC", "disruptionTreatment": "NO_PROTECTION", "usRulesApply": false}}, {"node": {"token": "HNYHnGkSHTnz-_b7G30GVk9RvzI9vhG1oJc4BTBggy_YJyAVnJ6DufUi1SOVQjN8NHLd2rQYcX2mkeN1IkwoMLvRjDGvO92QovKQx_we8qpxrkLeTR7tKbkDKPd646wLPzsjNzHi6crQm8r3WsWNpKWd1LDNxFRFDtw-LAcq7sriqgx0Okwesw0Wgf-6KmB3R-y8tq2HAni3FACCMEqstg0qRaz6wbq1pbP4KexG5cWqnRhVo8ezke9-3-vt6ttjFpH3I-dnRQ2ocP9A_jBuNQZrdiG2yTJXmnUWSPtr5aWOblTqNZohixfVIHl7aBYGCTfXoU22D3o5cUJexrLcuhVFMG2UOk8vWmg5peVVOtvDNMyndVeJoilcLLs8Sj0SVVT3hOPLrgcUbntq8irt9L1upKm-ux6ir9LgSvEOPqNISY9WBNB4BYp52iWIJ9x6VSFFih0BbwTsB3s4B1FdhZcbMXOr3B5YKROW93dGFbdjrt3UBz8jgosxUDQcxV8ubdnwdHyrRp6ENbi04HKkhdq84uqzDn7X9tsZp_zN7Uuz1neSLloUkrMlryznBvJ7COYwuajfCz2TXbczb_zmsjHFt2MwQ3Bgx-grId6CWuzjI-LwIXxoorkxUaufBgvn2g8vu1s0Y6sioqeb1OV2b_079IZCxXQ69MGLFX18N3AW2RBIsaLZS1DWypyPnV3rJrwWNhaOyr10QuPvRyjq9PEJNX7Ssud5evCVjke-rIgpsEbwqmXyxG3Tt6YJ8_kFUtb6BtnJGiQvCEXxN8jPfZT1MzmDQ5lRIOeLBbisvOIUZjkvkBSSk61KfEBdrpLgRaqT0X3aELH05kiWdbKM_xtkNWNpEaZlTKBGlBC_-qy_Pr-d48dhVMV9IBmKblVns3IS11sOOJpjD9Ey7V45CW5x-3VTI0PMn4AvdYnHuzrOLAbdBdHnbxI_0pd_BlJ9ySFV-ACWhFBw93htFAb_Hw2ysYV_wuVMYbsqNuqAZFqYRF8pYqJso_EP-eVjqo8XNibNTGVnEp-JUD-exVWV0_EJpcKV2pfFoqAOiKt6pTNhZoTFkfyQ6yn_Rzbr-stWEy42Gr8Bx9rgNS2ZOx8fIcerxiDAaEvwjBjUwzsIbFGs=", "bookingUrl": "/cn/booking/?direct=true&locale=cn&currency=cny&passengers=1-0-0&token=HNYHnGkSHTnz-_b7G30GVk9RvzI9vhG1oJc4BTBggy_YJyAVnJ6DufUi1SOVQjN8NHLd2rQYcX2mkeN1IkwoMLvRjDGvO92QovKQx_we8qpxrkLeTR7tKbkDKPd646wLPzsjNzHi6crQm8r3WsWNpKWd1LDNxFRFDtw-LAcq7sriqgx0Okwesw0Wgf-6KmB3R-y8tq2HAni3FACCMEqstg0qRaz6wbq1pbP4KexG5cWqnRhVo8ezke9-3-vt6ttjFpH3I-dnRQ2ocP9A_jBuNQZrdiG2yTJXmnUWSPtr5aWOblTqNZohixfVIHl7aBYGCTfXoU22D3o5cUJexrLcuhVFMG2UOk8vWmg5peVVOtvDNMyndVeJoilcLLs8Sj0SVVT3hOPLrgcUbntq8irt9L1upKm-ux6ir9LgSvEOPqNISY9WBNB4BYp52iWIJ9x6VSFFih0BbwTsB3s4B1FdhZcbMXOr3B5YKROW93dGFbdjrt3UBz8jgosxUDQcxV8ubdnwdHyrRp6ENbi04HKkhdq84uqzDn7X9tsZp_zN7Uuz1neSLloUkrMlryznBvJ7COYwuajfCz2TXbczb_zmsjHFt2MwQ3Bgx-grId6CWuzjI-LwIXxoorkxUaufBgvn2g8vu1s0Y6sioqeb1OV2b_079IZCxXQ69MGLFX18N3AW2RBIsaLZS1DWypyPnV3rJrwWNhaOyr10QuPvRyjq9PEJNX7Ssud5evCVjke-rIgpsEbwqmXyxG3Tt6YJ8_kFUtb6BtnJGiQvCEXxN8jPfZT1MzmDQ5lRIOeLBbisvOIUZjkvkBSSk61KfEBdrpLgRaqT0X3aELH05kiWdbKM_xtkNWNpEaZlTKBGlBC_-qy_Pr-d48dhVMV9IBmKblVns3IS11sOOJpjD9Ey7V45CW5x-3VTI0PMn4AvdYnHuzrOLAbdBdHnbxI_0pd_BlJ9ySFV-ACWhFBw93htFAb_Hw2ysYV_wuVMYbsqNuqAZFqYRF8pYqJso_EP-eVjqo8XNibNTGVnEp-JUD-exVWV0_EJpcKV2pfFoqAOiKt6pTNhZoTFkfyQ6yn_Rzbr-stWEy42Gr8Bx9rgNS2ZOx8fIcerxiDAaEvwjBjUwzsIbFGs%3D&searchType=oneWay&searchBags=0.0", "trackingPixel": "https://www.kiwi.com/pixel?affilid=cj&currency=CNY&flightsId=0f640fa04f1b0000902a7bb2_0&from=LHR&lang=cn&passengers=1&searchBags=0.0&to=PKX&booking_token=HNYHnGkSHTnz-_b7G30GVk9RvzI9vhG1oJc4BTBggy_YJyAVnJ6DufUi1SOVQjN8NHLd2rQYcX2mkeN1IkwoMLvRjDGvO92QovKQx_we8qpxrkLeTR7tKbkDKPd646wLPzsjNzHi6crQm8r3WsWNpKWd1LDNxFRFDtw-LAcq7sriqgx0Okwesw0Wgf-6KmB3R-y8tq2HAni3FACCMEqstg0qRaz6wbq1pbP4KexG5cWqnRhVo8ezke9-3-vt6ttjFpH3I-dnRQ2ocP9A_jBuNQZrdiG2yTJXmnUWSPtr5aWOblTqNZohixfVIHl7aBYGCTfXoU22D3o5cUJexrLcuhVFMG2UOk8vWmg5peVVOtvDNMyndVeJoilcLLs8Sj0SVVT3hOPLrgcUbntq8irt9L1upKm-ux6ir9LgSvEOPqNISY9WBNB4BYp52iWIJ9x6VSFFih0BbwTsB3s4B1FdhZcbMXOr3B5YKROW93dGFbdjrt3UBz8jgosxUDQcxV8ubdnwdHyrRp6ENbi04HKkhdq84uqzDn7X9tsZp_zN7Uuz1neSLloUkrMlryznBvJ7COYwuajfCz2TXbczb_zmsjHFt2MwQ3Bgx-grId6CWuzjI-LwIXxoorkxUaufBgvn2g8vu1s0Y6sioqeb1OV2b_079IZCxXQ69MGLFX18N3AW2RBIsaLZS1DWypyPnV3rJrwWNhaOyr10QuPvRyjq9PEJNX7Ssud5evCVjke-rIgpsEbwqmXyxG3Tt6YJ8_kFUtb6BtnJGiQvCEXxN8jPfZT1MzmDQ5lRIOeLBbisvOIUZjkvkBSSk61KfEBdrpLgRaqT0X3aELH05kiWdbKM_xtkNWNpEaZlTKBGlBC_-qy_Pr-d48dhVMV9IBmKblVns3IS11sOOJpjD9Ey7V45CW5x-3VTI0PMn4AvdYnHuzrOLAbdBdHnbxI_0pd_BlJ9ySFV-ACWhFBw93htFAb_Hw2ysYV_wuVMYbsqNuqAZFqYRF8pYqJso_EP-eVjqo8XNibNTGVnEp-JUD-exVWV0_EJpcKV2pfFoqAOiKt6pTNhZoTFkfyQ6yn_Rzbr-stWEy42Gr8Bx9rgNS2ZOx8fIcerxiDAaEvwjBjUwzsIbFGs=", "itineraryProvider": {"code": "KIWI", "name": "Kiwi.com", "subprovider": null, "hasHighProbabilityOfPriceChange": false, "contentProvider": {"code": "KIWI"}, "providerCategory": null, "id": "ItineraryProvider:KIWI"}, "price": {"amount": "4799"}, "priceEur": {"amount": "587.81"}, "priceLocks": {"priceLocksCurr": [{"default": false, "price": {"amount": "239.95", "roundedFormattedValue": "¥240"}}, {"default": true, "price": {"amount": "479.9", "roundedFormattedValue": "¥480"}}, {"default": false, "price": {"amount": "1199.75", "roundedFormattedValue": "¥1,200"}}], "priceLocksEur": [{"default": false, "price": {"amount": "29.39", "roundedFormattedValue": "29 €"}}, {"default": true, "price": {"amount": "58.78", "roundedFormattedValue": "59 €"}}, {"default": false, "price": {"amount": "146.95", "roundedFormattedValue": "147 €"}}]}, "kiwiProduct": "KIWI_BENEFITS", "disruptionTreatment": "DISRUPTION_PROTECTION", "usRulesApply": null}}]}, "travelHack": {"isTrueHiddenCity": false, "isVirtualInterlining": false, "isThrowawayTicket": false}, "duration": 36900, "pnrCount": 1, "sector": {"id": "Sector:4fff6654af63bb4f848453d32185cfb1e8fbdab5~4fff6654af63bb4f848453d32185cfb1e8fbdab5", "sectorSegments": [{"segment": {"source": {"station": {"code": "LHR", "type": "AIRPORT", "city": {"name": "伦敦", "id": "City:london_gb", "legacyId": "london_gb"}, "id": "Station:airport:LHR", "legacyId": "LHR", "name": "伦敦希思罗机场", "gps": {"lat": 51.4775, "lng": -0.4613889}, "country": {"code": "GB", "id": "Country:GB"}}, "localTime": "2025-06-12T21:00:00", "utcTimeIso": "2025-06-12T20:00:00Z"}, "destination": {"station": {"code": "PKX", "type": "AIRPORT", "city": {"name": "北京市", "id": "City:beijing_cn", "legacyId": "beijing_cn"}, "id": "Station:airport:PKX", "legacyId": "PKX", "name": "北京大兴国际机场", "gps": {"lat": 39.509167, "lng": 116.410556}, "country": {"code": "CN", "id": "Country:CN"}}, "localTime": "2025-06-13T14:15:00", "utcTimeIso": "2025-06-13T06:15:00Z"}, "id": "Segment:4fff6654af63bb4f848453d32185cfb1e8fbdab5", "duration": 36900, "type": "FLIGHT", "code": "674", "carrier": {"id": "Carrier:242", "name": "China Southern Airlines", "code": "CZ"}, "operatingCarrier": {"id": "Carrier:242", "name": "China Southern Airlines", "code": "CZ"}, "cabinClass": "ECONOMY", "hiddenDestination": null, "throwawayDestination": null}, "guarantee": null, "layover": null}], "duration": 36900}, "legacyId": "0f640fa04f1b0000902a7bb2_0", "lastAvailable": {"seatsLeft": 9}, "isRyanair": false, "benefitsData": {"automaticCheckinAvailable": false, "instantChatSupportAvailable": true, "disruptionProtectionAvailable": true, "guaranteeAvailable": false, "guaranteeFee": null, "guaranteeFeeEur": null, "searchReferencePrice": {"roundedAmount": "4319"}}, "isAirBaggageBundleEligible": null, "testEligibilityInformation": {"paretoABTestNewItinerary": null}}]}}}