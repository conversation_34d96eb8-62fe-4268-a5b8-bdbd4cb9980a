{"data": {"onewayItineraries": {"__typename": "Itineraries", "server": {"requestId": "d62f58d378f0411db589e2a57b046cf6", "environment": "PROD", "packageVersion": "git-bff8ac9486769348e980126f223dbeddabd34ddc", "serverToken": "****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"}, "metadata": {"eligibilityInformation": {"baggageEligibilityInformation": {"topFiveResultsBaggageEligibleForPrompt": null, "numberOfBags": null}, "guaranteeAndRedirectsEligibilityInformation": {"redirect": {"anywhere": true, "top10": true, "isKiwiAvailable": true}, "guarantee": {"anywhere": false, "top10": false}, "combination": {"anywhere": false, "top10": false}}, "kiwiBasicEligibility": {"anywhere": true, "top10": true}, "topThreeResortingOccurred": false, "carriersDeeplinkEligibility": null, "responseContainsKayakItinerary": false, "paretoABTestEligible": null}, "carriers": [{"code": "JX", "id": "Carrier:818", "name": "Starlux Airlines"}, {"code": "XY", "id": "Carrier:468", "name": "flynas"}, {"code": "UO", "id": "Carrier:349", "name": "Hong Kong Express Airways"}, {"code": "D8", "id": "Carrier:276", "name": "Norwegian Air Sweden"}, {"code": "VN", "id": "Carrier:611", "name": "Vietnam Airlines"}, {"code": "NH", "id": "Carrier:99", "name": "All Nippon Airways"}, {"code": "EI", "id": "Carrier:10", "name": "<PERSON><PERSON>"}, {"code": "GF", "id": "Carrier:333", "name": "Gulf Air Bahrain"}, {"code": "A3", "id": "Carrier:39", "name": "Aegean"}, {"code": "6E", "id": "Carrier:357", "name": "IndiGo Airlines"}, {"code": "AR", "id": "Carrier:112", "name": "Aerolineas Argentinas"}, {"code": "HH", "id": "Carrier:788", "name": "Qanot Shark"}, {"code": "KC", "id": "Carrier:174", "name": "Air Astana"}, {"code": "9C", "id": "Carrier:785", "name": "Spring Airlines"}, {"code": "LO", "id": "Carrier:409", "name": "LOT Polish Airlines"}, {"code": "OS", "id": "Carrier:121", "name": "Austrian Airlines"}, {"code": "VJ", "id": "Carrier:642", "name": "VietJet Air"}, {"code": "WY", "id": "Carrier:473", "name": "Oman Air"}, {"code": "ME", "id": "Carrier:445", "name": "Middle East Airlines"}, {"code": "W6", "id": "Carrier:12", "name": "Wizz Air"}, {"code": "ET", "id": "Carrier:295", "name": "Ethiopian Airlines"}, {"code": "DE", "id": "Carrier:28", "name": "Condor"}, {"code": "EW", "id": "Carrier:301", "name": "Eurowings"}, {"code": "KN", "id": "Carrier:694", "name": "China United"}, {"code": "LH", "id": "Carrier:419", "name": "Lufthansa"}, {"code": "BT", "id": "Carrier:22", "name": "airBaltic"}, {"code": "D7", "id": "Carrier:314", "name": "AirAsia X"}, {"code": "CZ", "id": "Carrier:242", "name": "China Southern Airlines"}, {"code": "H1", "id": "Carrier:830", "name": "Hahn Air"}, {"code": "ZH", "id": "Carrier:550", "name": "Shenzhen Airlines"}, {"code": "IB", "id": "Carrier:352", "name": "Iberia Airlines"}, {"code": "TW", "id": "Carrier:816", "name": "Tway Airlines"}, {"code": "JD", "id": "Carrier:648", "name": "Beijing Capital Airlines"}, {"code": "FR", "id": "Carrier:11", "name": "<PERSON><PERSON>"}, {"code": "OZ", "id": "Carrier:51", "name": "Asiana Airlines"}, {"code": "MS", "id": "Carrier:288", "name": "Egyptair"}, {"code": "OD", "id": "Carrier:888", "name": "Batik Air Malaysia"}, {"code": "NX", "id": "Carrier:96", "name": "Air Macau"}, {"code": "AI", "id": "Carrier:82", "name": "Air India Limited"}, {"code": "3O", "id": "Carrier:672", "name": "Air Arabia Maroc"}, {"code": "UX", "id": "Carrier:60", "name": "Air Europa"}, {"code": "RJ", "id": "Carrier:512", "name": "Royal Jordanian"}, {"code": "FM", "id": "Carrier:549", "name": "Shanghai Airlines"}, {"code": "JU", "id": "Carrier:378", "name": "Air Serbia"}, {"code": "AF", "id": "Carrier:70", "name": "Air France"}, {"code": "SQ", "id": "Carrier:528", "name": "Singapore Airlines"}, {"code": "VS", "id": "Carrier:618", "name": "Virgin Atlantic Airways"}, {"code": "BG", "id": "Carrier:208", "name": "Biman Bangladesh Airlines"}, {"code": "AT", "id": "Carrier:510", "name": "Royal Air Maroc"}, {"code": "MU", "id": "Carrier:241", "name": "China Eastern Airlines"}, {"code": "X1", "id": "Carrier:2388", "name": "Hahn Air Technologies"}, {"code": "AM", "id": "Carrier:98", "name": "AeroMéxico"}, {"code": "KU", "id": "Carrier:402", "name": "Kuwait Airways"}, {"code": "HV", "id": "Carrier:4", "name": "Transavia"}, {"code": "BA", "id": "Carrier:207", "name": "British Airways"}, {"code": "QR", "id": "Carrier:502", "name": "Qatar Airways"}, {"code": "LX", "id": "Carrier:541", "name": "Swiss International Air Lines"}, {"code": "RO", "id": "Carrier:597", "name": "<PERSON><PERSON>"}, {"code": "SV", "id": "Carrier:538", "name": "Saudi Arabian Airlines"}, {"code": "5J", "id": "Carrier:237", "name": "Cebu Pacific"}, {"code": "LA", "id": "Carrier:405", "name": "LATAM Airlines"}, {"code": "FZ", "id": "Carrier:27", "name": "Fly Dubai"}, {"code": "YP", "id": "Carrier:2723", "name": "Air Premia"}, {"code": "KQ", "id": "Carrier:395", "name": "Kenya Airways"}, {"code": "HO", "id": "Carrier:388", "name": "Juneyao Airlines"}, {"code": "OM", "id": "Carrier:426", "name": "MIAT Mongolian Airlines"}, {"code": "FB", "id": "Carrier:225", "name": "Bulgaria Air"}, {"code": "PC", "id": "Carrier:16", "name": "Pegasus"}, {"code": "VY", "id": "Carrier:9", "name": "<PERSON><PERSON>ing"}, {"code": "AY", "id": "Carrier:307", "name": "<PERSON><PERSON>"}, {"code": "F3", "id": "Carrier:1018", "name": "Flyadeal"}, {"code": "7C", "id": "Carrier:380", "name": "Je<PERSON>"}, {"code": "TO", "id": "Carrier:654", "name": "Transavia France"}, {"code": "UL", "id": "Carrier:522", "name": "SriLankan Airlines"}, {"code": "VZ", "id": "Carrier:454", "name": "Thai Vietjet"}, {"code": "WB", "id": "Carrier:513", "name": "Rwandair"}, {"code": "3U", "id": "Carrier:548", "name": "Sichuan Airlines"}, {"code": "U2", "id": "Carrier:3", "name": "easyJet"}, {"code": "A9", "id": "Carrier:325", "name": "Georgian Airways"}, {"code": "DV", "id": "Carrier:569", "name": "Scat Airlines"}, {"code": "TG", "id": "Carrier:579", "name": "Thai Airways"}, {"code": "GA", "id": "Carrier:323", "name": "Garuda Indonesia"}, {"code": "DY", "id": "Carrier:17", "name": "Norwegian Air Shuttle"}, {"code": "G9", "id": "Carrier:26", "name": "Air Arabia"}, {"code": "HU", "id": "Carrier:335", "name": "Hainan Airlines"}, {"code": "SN", "id": "Carrier:223", "name": "Brussels Airlines"}, {"code": "CA", "id": "Carrier:151", "name": "Air China"}, {"code": "W4", "id": "Carrier:820", "name": "Wizz Air Malta"}, {"code": "PG", "id": "Carrier:211", "name": "Bangkok Airways"}, {"code": "MF", "id": "Carrier:638", "name": "Xiamen Airlines"}, {"code": "HY", "id": "Carrier:606", "name": "Uzbekistan Airways"}, {"code": "J2", "id": "Carrier:79", "name": "Azerbaijan Airlines"}, {"code": "AH", "id": "Carrier:156", "name": "Air Algerie"}, {"code": "TP", "id": "Carrier:572", "name": "TAP Portugal"}, {"code": "TR", "id": "Carrier:577", "name": "<PERSON>oot"}, {"code": "UA", "id": "Carrier:601", "name": "United Airlines"}, {"code": "CI", "id": "Carrier:240", "name": "China Airlines"}, {"code": "SM", "id": "Carrier:737", "name": "Air Cairo"}, {"code": "KL", "id": "Carrier:391", "name": "KLM Royal Dutch Airlines"}, {"code": "VF", "id": "Carrier:610", "name": "AJet"}, {"code": "HB", "id": "Carrier:2721", "name": "Greater Bay Airlines"}, {"code": "QV", "id": "Carrier:412", "name": "Lao Airlines"}, {"code": "KE", "id": "Carrier:399", "name": "Korean Air"}, {"code": "PR", "id": "Carrier:492", "name": "Philippine Airlines"}, {"code": "TK", "id": "Carrier:581", "name": "Turkish Airlines"}, {"code": "GJ", "id": "Carrier:299", "name": "Loong Air"}, {"code": "FI", "id": "Carrier:355", "name": "Icelandair"}, {"code": "OV", "id": "Carrier:294", "name": "SalamAir"}, {"code": "HX", "id": "Carrier:348", "name": "Hong Kong Airlines"}, {"code": "KM", "id": "Carrier:87", "name": "KM Malta Airlines"}, {"code": "3K", "id": "Carrier:383", "name": "Jetstar Asia Airways"}, {"code": "AZ", "id": "Carrier:138", "name": "ITA Airways"}, {"code": "TU", "id": "Carrier:573", "name": "<PERSON><PERSON><PERSON>"}, {"code": "BR", "id": "Carrier:282", "name": "EVA Air"}, {"code": "GQ", "id": "Carrier:1157", "name": "SKY express"}, {"code": "CX", "id": "Carrier:235", "name": "Cathay Pacific"}, {"code": "SK", "id": "Carrier:519", "name": "SAS"}, {"code": "EK", "id": "Carrier:291", "name": "Emirates"}, {"code": "EN", "id": "Carrier:158", "name": "Air Dolomiti"}, {"code": "OU", "id": "Carrier:265", "name": "Croatia Airlines"}, {"code": "IX", "id": "Carrier:134", "name": "Air India Express"}, {"code": "EY", "id": "Carrier:296", "name": "Etihad Airways"}, {"code": "MH", "id": "Carrier:430", "name": "Malaysia Airlines"}, {"code": "KR", "id": "Carrier:256", "name": "Cambodia Airways"}], "stopoverCountries": [], "inboundDays": [], "outboundDays": ["THURSDAY"], "travelTips": [], "topResults": {"best": {"__typename": "ItineraryOneWay", "duration": 36900, "price": {"amount": "3805"}, "id": "ItineraryOneWay:****************************************************************************************************************************************************************"}, "cheapest": {"__typename": "ItineraryOneWay", "duration": 36900, "price": {"amount": "3805"}, "id": "ItineraryOneWay:****************************************************************************************************************************************************************"}, "fastest": {"__typename": "ItineraryOneWay", "duration": 36900, "price": {"amount": "3805"}, "id": "ItineraryOneWay:****************************************************************************************************************************************************************"}, "sourceTakeoffAsc": {"__typename": "ItineraryOneWay", "duration": 36900, "price": {"amount": "3805"}, "id": "ItineraryOneWay:****************************************************************************************************************************************************************"}, "destinationLandingAsc": {"__typename": "ItineraryOneWay", "duration": 36900, "price": {"amount": "3805"}, "id": "ItineraryOneWay:****************************************************************************************************************************************************************"}}, "priceAlertExists": null, "existingPriceAlert": null, "searchFingerprint": "************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "hasMorePending": false, "priceAlertsTopResults": {"best": {"price": {"amount": "4209"}}, "cheapest": {"price": {"amount": "4209"}}, "fastest": {"price": {"amount": "4209"}}, "sourceTakeoffAsc": {"price": {"amount": "4209"}}, "destinationLandingAsc": {"price": {"amount": "4209"}}}, "itinerariesCount": 2, "missingProviders": [], "statusPerProvider": [{"provider": {"id": "ContentProvider:KIWI"}, "errorHappened": false, "errorMessage": null}], "hasTier1MarketItineraries": false, "sharedItinerary": null, "kayakEligibilityTest": {"containsKayakWithNewRules": null, "containsKayakWithCurrentRules": false, "containsKayakAirlinesWithNewRules": null}}, "itineraries": [{"__typename": "ItineraryOneWay", "__isItinerary": "ItineraryOneWay", "id": "ItineraryOneWay:****************************************************************************************************************************************************************", "shareId": "********************************************************************************************************************************************************************************", "price": {"amount": "3805", "priceBeforeDiscount": "3805"}, "priceEur": {"amount": "466.059307"}, "provider": {"name": "Kiwi.com", "code": "KIWI-BASIC", "hasHighProbabilityOfPriceChange": false, "contentProvider": null, "id": "ItineraryProvider:KIWI-BASIC"}, "bagsInfo": {"includedCheckedBags": 0, "includedHandBags": 0, "hasNoBaggageSupported": true, "hasNoCheckedBaggage": false, "checkedBagTiers": [], "handBagTiers": [], "includedPersonalItem": 1, "personalItemTiers": [{"tierPrice": {"amount": "0"}, "bags": [{"weight": {"value": 8}, "height": {"value": 30}, "width": {"value": 15}, "length": {"value": 40}}]}]}, "bookingOptions": {"edges": [{"node": {"token": "HB1DQqvTsaDUGwXvpdZ6ZsFA3sa5-DfH-hcR9nwg4CaEnec9FA9zXQMQSyzzlc5wh-947UaFua03l8smjv1XxGIfKRPNZrxrWruZE2h-SC4gP2xyKk6O-MiyLRIhXR4Ko-eoeN6AtbuzzolUBms1DLg-mhJJkig5wMDjew6ehi_7PBkaUC4fgAU_-tK0MZsBn2FrOV1lJJgpizfnmcBTbg4TETV_3s2L0y99h_SLmMXKreSnLpMrXC1rb5qUZ3ycJ42GIFnJxxwbrt_u1IB3k61vCGbwp0Uy1KOEXn3l51ezTda4a6eNAPl0B5NwksKPPsDFWSOnrDSvvdej14LdlFrJOiLJi-etGfWv67YwAfrgvOt5_rkpGOPnKeZeYoai2oRZLbzToPr8SVdnESig_zhXrnYIEJ2BOGyq3TAgzr2ya6kuIxQugoRMZVDXBTyfyNMMQYZQlWVaCY3QRS9lbfkDZmfTRUVMZJl3yDWBVyW_k_84EOXnZqLHNR289uMFLubYs8--JDbQnTPmDBalNw4p4yezELann12FS7uCbvAsHxbdmfb9lteijaWK7-ISJDZ1xbQ8CJqx0jI_gQloZY0pmaExmHVtTXCndAgoVFxavHqu1dfpVw9plJ9LrYJz1g9psVrkcEPB9LD_iK0Az1XfbhAgdzb4Y2vSN6ovEJSxjehn0F1HHAQvEnT-A9MSXI9rfOdKoyci5efQwSdXFNcG-N_lJl_lF_fgyJJ1vJzcfUtn_HPoNmvIb6u9S8E4xbnmnjLUC0qQcXNVoj6xFUCOB8qVG2yokRTAFd3KtBZIDbqEe5xvo4ieewRhpz1116IHWdGrCxDxL0fzP-yHH_6lhw-BzRo-ohiyXp7iGdknDTHp4e_TskKbxJF4TKY8J4lx3RBH_Phwp-v3o4W9L0NqiaPoPk-5dYhyjO1eOnsuHsoyryQ69NWzSe9Jps0YVi8MiiWpavHMZnLKKZQv28yUGLD_8WBlYB4Ct6VwqxmG7FtdCvZqYYOQWPQPJUxVU0ZHHFTxjWJ5WKGpaGzzobJZS9HKJByl43GWg4mVReGZpw5_vEGHoYoae_o3y7woB", "bookingUrl": "/cn/booking/?direct=true&locale=cn&currency=cny&passengers=1-0-0&token=HB1DQqvTsaDUGwXvpdZ6ZsFA3sa5-DfH-hcR9nwg4CaEnec9FA9zXQMQSyzzlc5wh-947UaFua03l8smjv1XxGIfKRPNZrxrWruZE2h-SC4gP2xyKk6O-MiyLRIhXR4Ko-eoeN6AtbuzzolUBms1DLg-mhJJkig5wMDjew6ehi_7PBkaUC4fgAU_-tK0MZsBn2FrOV1lJJgpizfnmcBTbg4TETV_3s2L0y99h_SLmMXKreSnLpMrXC1rb5qUZ3ycJ42GIFnJxxwbrt_u1IB3k61vCGbwp0Uy1KOEXn3l51ezTda4a6eNAPl0B5NwksKPPsDFWSOnrDSvvdej14LdlFrJOiLJi-etGfWv67YwAfrgvOt5_rkpGOPnKeZeYoai2oRZLbzToPr8SVdnESig_zhXrnYIEJ2BOGyq3TAgzr2ya6kuIxQugoRMZVDXBTyfyNMMQYZQlWVaCY3QRS9lbfkDZmfTRUVMZJl3yDWBVyW_k_84EOXnZqLHNR289uMFLubYs8--JDbQnTPmDBalNw4p4yezELann12FS7uCbvAsHxbdmfb9lteijaWK7-ISJDZ1xbQ8CJqx0jI_gQloZY0pmaExmHVtTXCndAgoVFxavHqu1dfpVw9plJ9LrYJz1g9psVrkcEPB9LD_iK0Az1XfbhAgdzb4Y2vSN6ovEJSxjehn0F1HHAQvEnT-A9MSXI9rfOdKoyci5efQwSdXFNcG-N_lJl_lF_fgyJJ1vJzcfUtn_HPoNmvIb6u9S8E4xbnmnjLUC0qQcXNVoj6xFUCOB8qVG2yokRTAFd3KtBZIDbqEe5xvo4ieewRhpz1116IHWdGrCxDxL0fzP-yHH_6lhw-BzRo-ohiyXp7iGdknDTHp4e_TskKbxJF4TKY8J4lx3RBH_Phwp-v3o4W9L0NqiaPoPk-5dYhyjO1eOnsuHsoyryQ69NWzSe9Jps0YVi8MiiWpavHMZnLKKZQv28yUGLD_8WBlYB4Ct6VwqxmG7FtdCvZqYYOQWPQPJUxVU0ZHHFTxjWJ5WKGpaGzzobJZS9HKJByl43GWg4mVReGZpw5_vEGHoYoae_o3y7woB&searchType=oneWay&searchBags=0.0", "trackingPixel": "https://www.kiwi.com/pixel?affilid=cj&currency=CNY&flightsId=0f6417874f1b00008e11ea39_0%7C0f6417874f1b00008e11ea39_1&from=LHR&lang=cn&passengers=1&searchBags=0.0&to=PKX&booking_token=HB1DQqvTsaDUGwXvpdZ6ZsFA3sa5-DfH-hcR9nwg4CaEnec9FA9zXQMQSyzzlc5wh-947UaFua03l8smjv1XxGIfKRPNZrxrWruZE2h-SC4gP2xyKk6O-MiyLRIhXR4Ko-eoeN6AtbuzzolUBms1DLg-mhJJkig5wMDjew6ehi_7PBkaUC4fgAU_-tK0MZsBn2FrOV1lJJgpizfnmcBTbg4TETV_3s2L0y99h_SLmMXKreSnLpMrXC1rb5qUZ3ycJ42GIFnJxxwbrt_u1IB3k61vCGbwp0Uy1KOEXn3l51ezTda4a6eNAPl0B5NwksKPPsDFWSOnrDSvvdej14LdlFrJOiLJi-etGfWv67YwAfrgvOt5_rkpGOPnKeZeYoai2oRZLbzToPr8SVdnESig_zhXrnYIEJ2BOGyq3TAgzr2ya6kuIxQugoRMZVDXBTyfyNMMQYZQlWVaCY3QRS9lbfkDZmfTRUVMZJl3yDWBVyW_k_84EOXnZqLHNR289uMFLubYs8--JDbQnTPmDBalNw4p4yezELann12FS7uCbvAsHxbdmfb9lteijaWK7-ISJDZ1xbQ8CJqx0jI_gQloZY0pmaExmHVtTXCndAgoVFxavHqu1dfpVw9plJ9LrYJz1g9psVrkcEPB9LD_iK0Az1XfbhAgdzb4Y2vSN6ovEJSxjehn0F1HHAQvEnT-A9MSXI9rfOdKoyci5efQwSdXFNcG-N_lJl_lF_fgyJJ1vJzcfUtn_HPoNmvIb6u9S8E4xbnmnjLUC0qQcXNVoj6xFUCOB8qVG2yokRTAFd3KtBZIDbqEe5xvo4ieewRhpz1116IHWdGrCxDxL0fzP-yHH_6lhw-BzRo-ohiyXp7iGdknDTHp4e_TskKbxJF4TKY8J4lx3RBH_Phwp-v3o4W9L0NqiaPoPk-5dYhyjO1eOnsuHsoyryQ69NWzSe9Jps0YVi8MiiWpavHMZnLKKZQv28yUGLD_8WBlYB4Ct6VwqxmG7FtdCvZqYYOQWPQPJUxVU0ZHHFTxjWJ5WKGpaGzzobJZS9HKJByl43GWg4mVReGZpw5_vEGHoYoae_o3y7woB", "itineraryProvider": {"code": "KIWI-BASIC", "name": "Kiwi.com", "subprovider": null, "hasHighProbabilityOfPriceChange": false, "contentProvider": null, "providerCategory": null, "id": "ItineraryProvider:KIWI-BASIC"}, "price": {"amount": "3805"}, "priceEur": {"amount": "466.06"}, "priceLocks": {"priceLocksCurr": [{"default": false, "price": {"amount": "190.25", "roundedFormattedValue": "¥190"}}, {"default": true, "price": {"amount": "380.5", "roundedFormattedValue": "¥381"}}, {"default": false, "price": {"amount": "951.25", "roundedFormattedValue": "¥951"}}], "priceLocksEur": [{"default": false, "price": {"amount": "23.3", "roundedFormattedValue": "23 €"}}, {"default": true, "price": {"amount": "46.61", "roundedFormattedValue": "47 €"}}, {"default": false, "price": {"amount": "116.51", "roundedFormattedValue": "117 €"}}]}, "kiwiProduct": "KIWI_BASIC", "disruptionTreatment": "NO_PROTECTION", "usRulesApply": false}}, {"node": {"token": "H-_5MQWGLlPWdkKaYc3nH6p7g_ZGLiukDZOMMl1x0FgnzwJfV_a8TXH3r8TPm675ynzHief47WYlNyO-zoiogyA5QVNOgyqs-Abc5WCyvnH4enEPidB32k5HVv4N9By7jA1nCGixVk8bukhwqpYrN_oCacLNKUx4eTmalFCaOwYj3ntUvXb6PMFedjsEip4Xfxal7ifITJ8NyXwkMb29uU7qLPJR7z_AqD3_nejTKwijr1Ghw5BtEPgaABUX6k8r0m4Bnp8J0Thm7OaC2XvYh0ZlAyG_xjTQJIGVjGRZmaGOrb6VB-CTqBZlNaFJzZTmQCZmpWxweX_a4FbmvxFyH5bobcIxyGZ_0kq_TfQ678g7p6yg56v1nidhr7-RtEJKFeQEYGTW5tTKi1M1gpseoc0SsC-nLsvsCdQ_1mCm9oTjIiYV6HgdI7GMWxPb2iK5V-FKCB2yzsthTgE24SrMShJfE-gcKgirtv05WZdl_YAq_B4003dvaBnRSYPE_KHGkwYWxnVriLFjVIzK_5SHDaPHP2pSwCsZQ5TbEDNgGSXBxEkdNlfC4C30Ku4OEpmYppJ8yoWPTGHmej1WYqbth-ZFNTWOa0aAhFSEucaWcKzIfDnmpXLmMpNcC2NFG7gQzRV7_lQFzCSj9B5a9wzhQ9NJCr5ixmY2rJLRULxuV1B2OVyBak4TFieII9gsxNFpaQAr-uCsHb08OUkBr0UFyaJEtYLzFLZmCswxA72dgGI-Svfq3MgjZDSRRJpZvJBvGWzoRIYEyOblgEwZXHT_W_Zh4MUAEHz1bYHK1hZRQFZ2T_ysrd2B44J4T1XjprTugjkMYd0d49HNB9m36ls3q68MlvGv64y7b0sptXm4HLVd1IBedehfDsCj2gOulPiSqh4FPPMBMNn7i7_jVifIGNSNA1NymL5tCOPUJQ3oFdZh9lEjljL9fWs_Tj71eGpOEtoCe3PT2Shte8ZqMn35rjha55IRnFWAnJzAXdKKQNJ_20fzUESD-PNjkpBWf_p2EXn6OplwnkSWLeLq1jQRRv2E7P_yFWAsxYWJ4p41Y0bfuTOGhzK4v-SgSv1ONxAtqdoAPOVho4rubU0Xp95ohjXs2rkJoN-vak3g_3ovGwRpzrAuIYFm4MIZOLFe3bTS0", "bookingUrl": "/cn/booking/?direct=true&locale=cn&currency=cny&passengers=1-0-0&token=H-_5MQWGLlPWdkKaYc3nH6p7g_ZGLiukDZOMMl1x0FgnzwJfV_a8TXH3r8TPm675ynzHief47WYlNyO-zoiogyA5QVNOgyqs-Abc5WCyvnH4enEPidB32k5HVv4N9By7jA1nCGixVk8bukhwqpYrN_oCacLNKUx4eTmalFCaOwYj3ntUvXb6PMFedjsEip4Xfxal7ifITJ8NyXwkMb29uU7qLPJR7z_AqD3_nejTKwijr1Ghw5BtEPgaABUX6k8r0m4Bnp8J0Thm7OaC2XvYh0ZlAyG_xjTQJIGVjGRZmaGOrb6VB-CTqBZlNaFJzZTmQCZmpWxweX_a4FbmvxFyH5bobcIxyGZ_0kq_TfQ678g7p6yg56v1nidhr7-RtEJKFeQEYGTW5tTKi1M1gpseoc0SsC-nLsvsCdQ_1mCm9oTjIiYV6HgdI7GMWxPb2iK5V-FKCB2yzsthTgE24SrMShJfE-gcKgirtv05WZdl_YAq_B4003dvaBnRSYPE_KHGkwYWxnVriLFjVIzK_5SHDaPHP2pSwCsZQ5TbEDNgGSXBxEkdNlfC4C30Ku4OEpmYppJ8yoWPTGHmej1WYqbth-ZFNTWOa0aAhFSEucaWcKzIfDnmpXLmMpNcC2NFG7gQzRV7_lQFzCSj9B5a9wzhQ9NJCr5ixmY2rJLRULxuV1B2OVyBak4TFieII9gsxNFpaQAr-uCsHb08OUkBr0UFyaJEtYLzFLZmCswxA72dgGI-Svfq3MgjZDSRRJpZvJBvGWzoRIYEyOblgEwZXHT_W_Zh4MUAEHz1bYHK1hZRQFZ2T_ysrd2B44J4T1XjprTugjkMYd0d49HNB9m36ls3q68MlvGv64y7b0sptXm4HLVd1IBedehfDsCj2gOulPiSqh4FPPMBMNn7i7_jVifIGNSNA1NymL5tCOPUJQ3oFdZh9lEjljL9fWs_Tj71eGpOEtoCe3PT2Shte8ZqMn35rjha55IRnFWAnJzAXdKKQNJ_20fzUESD-PNjkpBWf_p2EXn6OplwnkSWLeLq1jQRRv2E7P_yFWAsxYWJ4p41Y0bfuTOGhzK4v-SgSv1ONxAtqdoAPOVho4rubU0Xp95ohjXs2rkJoN-vak3g_3ovGwRpzrAuIYFm4MIZOLFe3bTS0&searchType=oneWay&searchBags=0.0", "trackingPixel": "https://www.kiwi.com/pixel?affilid=cj&currency=CNY&flightsId=0f6417874f1b00008e11ea39_0%7C0f6417874f1b00008e11ea39_1&from=LHR&lang=cn&passengers=1&searchBags=0.0&to=PKX&booking_token=H-_5MQWGLlPWdkKaYc3nH6p7g_ZGLiukDZOMMl1x0FgnzwJfV_a8TXH3r8TPm675ynzHief47WYlNyO-zoiogyA5QVNOgyqs-Abc5WCyvnH4enEPidB32k5HVv4N9By7jA1nCGixVk8bukhwqpYrN_oCacLNKUx4eTmalFCaOwYj3ntUvXb6PMFedjsEip4Xfxal7ifITJ8NyXwkMb29uU7qLPJR7z_AqD3_nejTKwijr1Ghw5BtEPgaABUX6k8r0m4Bnp8J0Thm7OaC2XvYh0ZlAyG_xjTQJIGVjGRZmaGOrb6VB-CTqBZlNaFJzZTmQCZmpWxweX_a4FbmvxFyH5bobcIxyGZ_0kq_TfQ678g7p6yg56v1nidhr7-RtEJKFeQEYGTW5tTKi1M1gpseoc0SsC-nLsvsCdQ_1mCm9oTjIiYV6HgdI7GMWxPb2iK5V-FKCB2yzsthTgE24SrMShJfE-gcKgirtv05WZdl_YAq_B4003dvaBnRSYPE_KHGkwYWxnVriLFjVIzK_5SHDaPHP2pSwCsZQ5TbEDNgGSXBxEkdNlfC4C30Ku4OEpmYppJ8yoWPTGHmej1WYqbth-ZFNTWOa0aAhFSEucaWcKzIfDnmpXLmMpNcC2NFG7gQzRV7_lQFzCSj9B5a9wzhQ9NJCr5ixmY2rJLRULxuV1B2OVyBak4TFieII9gsxNFpaQAr-uCsHb08OUkBr0UFyaJEtYLzFLZmCswxA72dgGI-Svfq3MgjZDSRRJpZvJBvGWzoRIYEyOblgEwZXHT_W_Zh4MUAEHz1bYHK1hZRQFZ2T_ysrd2B44J4T1XjprTugjkMYd0d49HNB9m36ls3q68MlvGv64y7b0sptXm4HLVd1IBedehfDsCj2gOulPiSqh4FPPMBMNn7i7_jVifIGNSNA1NymL5tCOPUJQ3oFdZh9lEjljL9fWs_Tj71eGpOEtoCe3PT2Shte8ZqMn35rjha55IRnFWAnJzAXdKKQNJ_20fzUESD-PNjkpBWf_p2EXn6OplwnkSWLeLq1jQRRv2E7P_yFWAsxYWJ4p41Y0bfuTOGhzK4v-SgSv1ONxAtqdoAPOVho4rubU0Xp95ohjXs2rkJoN-vak3g_3ovGwRpzrAuIYFm4MIZOLFe3bTS0", "itineraryProvider": {"code": "KIWI", "name": "Kiwi.com", "subprovider": null, "hasHighProbabilityOfPriceChange": false, "contentProvider": {"code": "KIWI"}, "providerCategory": null, "id": "ItineraryProvider:KIWI"}, "price": {"amount": "4209"}, "priceEur": {"amount": "515.54"}, "priceLocks": {"priceLocksCurr": [{"default": false, "price": {"amount": "210.45", "roundedFormattedValue": "¥210"}}, {"default": true, "price": {"amount": "420.9", "roundedFormattedValue": "¥421"}}, {"default": false, "price": {"amount": "1052.25", "roundedFormattedValue": "¥1,052"}}], "priceLocksEur": [{"default": false, "price": {"amount": "25.78", "roundedFormattedValue": "26 €"}}, {"default": true, "price": {"amount": "51.55", "roundedFormattedValue": "52 €"}}, {"default": false, "price": {"amount": "128.89", "roundedFormattedValue": "129 €"}}]}, "kiwiProduct": "KIWI_BENEFITS", "disruptionTreatment": "DISRUPTION_PROTECTION", "usRulesApply": null}}]}, "travelHack": {"isTrueHiddenCity": true, "isVirtualInterlining": false, "isThrowawayTicket": false}, "duration": 36900, "pnrCount": 1, "sector": {"id": "Sector:adcfbe213ec4e887f1b4d0174d49824958a2cc45~adcfbe213ec4e887f1b4d0174d49824958a2cc45", "sectorSegments": [{"segment": {"source": {"station": {"code": "LHR", "type": "AIRPORT", "city": {"name": "伦敦", "id": "City:london_gb", "legacyId": "london_gb"}, "id": "Station:airport:LHR", "legacyId": "LHR", "name": "伦敦希思罗机场", "gps": {"lat": 51.4775, "lng": -0.4613889}, "country": {"code": "GB", "id": "Country:GB"}}, "localTime": "2025-06-12T21:00:00", "utcTimeIso": "2025-06-12T20:00:00Z"}, "destination": {"station": {"code": "PKX", "type": "AIRPORT", "city": {"name": "北京市", "id": "City:beijing_cn", "legacyId": "beijing_cn"}, "id": "Station:airport:PKX", "legacyId": "PKX", "name": "北京大兴国际机场", "gps": {"lat": 39.509167, "lng": 116.410556}, "country": {"code": "CN", "id": "Country:CN"}}, "localTime": "2025-06-13T14:15:00", "utcTimeIso": "2025-06-13T06:15:00Z"}, "id": "Segment:adcfbe213ec4e887f1b4d0174d49824958a2cc45", "duration": 36900, "type": "FLIGHT", "code": "674", "carrier": {"id": "Carrier:242", "name": "China Southern Airlines", "code": "CZ"}, "operatingCarrier": {"id": "Carrier:242", "name": "China Southern Airlines", "code": "CZ"}, "cabinClass": "ECONOMY", "hiddenDestination": {"code": "HAK", "name": "海口美兰国际机场", "city": {"name": "海口市", "id": "City:haikou_cn"}, "country": {"name": "中国", "id": "Country:CN"}, "id": "Station:airport:HAK"}, "throwawayDestination": null}, "guarantee": null, "layover": null}], "duration": 36900}, "legacyId": "0f6417874f1b00008e11ea39_0", "lastAvailable": {"seatsLeft": 7}, "isRyanair": false, "benefitsData": {"automaticCheckinAvailable": false, "instantChatSupportAvailable": true, "disruptionProtectionAvailable": true, "guaranteeAvailable": false, "guaranteeFee": null, "guaranteeFeeEur": null, "searchReferencePrice": {"roundedAmount": "3805"}}, "isAirBaggageBundleEligible": null, "testEligibilityInformation": {"paretoABTestNewItinerary": null}}, {"__typename": "ItineraryOneWay", "__isItinerary": "ItineraryOneWay", "id": "ItineraryOneWay:************************************************************************************************************************************************************", "shareId": "****************************************************************************************************************************************************************************", "price": {"amount": "4319", "priceBeforeDiscount": "4319"}, "priceEur": {"amount": "529.017122"}, "provider": {"name": "Kiwi.com", "code": "KIWI-BASIC", "hasHighProbabilityOfPriceChange": false, "contentProvider": null, "id": "ItineraryProvider:KIWI-BASIC"}, "bagsInfo": {"includedCheckedBags": 1, "includedHandBags": 1, "hasNoBaggageSupported": false, "hasNoCheckedBaggage": false, "checkedBagTiers": [{"tierPrice": {"amount": "0"}, "bags": [{"weight": {"value": 23}}]}], "handBagTiers": [{"tierPrice": {"amount": "0"}, "bags": [{"weight": {"value": 8}}]}], "includedPersonalItem": 1, "personalItemTiers": [{"tierPrice": {"amount": "0"}, "bags": [{"weight": {"value": 8}, "height": {"value": 30}, "width": {"value": 15}, "length": {"value": 40}}]}]}, "bookingOptions": {"edges": [{"node": {"token": "HYrnaVoFf4Y4hXm2508zGpHuIowhUNgTnjjAJF83UnX0hvvEA2KWyN2I87PNW5lvjVKkTOn1bqZ09VNdbBXAI5gkGROVd3DN_gXyrYca9hVuVrxQTF31CL0v263Xa_cJci-5A9cf_w1pGYir8EzhJiq4-oX4NIoN7c5O0kIaEUch4mmj5Jx_KGIgJJ3hHDJ9VizHl81KF8BeGZIjqoi5u3CumbsNqtJw5eFCFnazL-B60elLSGia7VyeM6K40Un4N83HEHTpMccWUZQ3ChZfA5FxxBpxoi-N9gF19oFr2Qo0iCL7HgB4e9A8W5oJnmOMbrGvSKk-LUedLNLJmbW_flvBmA3Dk7Zr7fH_DHgzcX0wm9-RwFh71uM-wh63leKTXyyHVrQFVGw7l6h-IQ2fALT6HeubgYEnP95XATYA2oMdS4VsQUjdHHUQe_LnMqpkLjMl4GAbXxG7X3R8fc8PTgeqPTKRpFfyJV-RWtQQONTw-tKxJV-B9CsIlB4L3gnaDCEYoFrXGd9LFoKzBh2blZ6O-Vr_RzmW3fkRZEvLki82TAuzLPxaxuTj0nJBkC09oiP2AG-bFfVba55_ldHGaE_H4UbD3eAUoi-d7Vu9JldJTQLtlmepbTgtU4QymMW3IjbeoOfv7a7voyYbllKZ7HUmMKT2BA2kBHT5sH9iIsTZSxE9UB0U8zb1vjosjQg-ic88rD2gkapgLMU0pEz96vkDfbELov_m-lQjQBM-db-Q-TR8aLBQyPIJp54Z2rgpJ_b9rju_2emhbsSWyCXAzuxZX_2SC4QIv_OxsrPWgQTIs5AJHeQdlj6sQIQS230y7Z2xFbzRVDYGdKzyMEwEcZUohVZj4xSuhaCO6Ohi4DCKmx7Mdv4PqIH8qAcFlMMp4ZnjpPXwyoYfwcN8MvAe2oLm2Ys8zqrLRd1mV-GfplbZiBvnvMo_rUV4vg4yclsLrEeyKizjy8xRskQwvBHF_vUMHtpj4PQjn5h_QzsBPYc-UggBeE2rOQNgb4KpZ_SXW2_tR2Z6JknJ81DcK2WHgpw==", "bookingUrl": "/cn/booking/?direct=true&locale=cn&currency=cny&passengers=1-0-0&token=HYrnaVoFf4Y4hXm2508zGpHuIowhUNgTnjjAJF83UnX0hvvEA2KWyN2I87PNW5lvjVKkTOn1bqZ09VNdbBXAI5gkGROVd3DN_gXyrYca9hVuVrxQTF31CL0v263Xa_cJci-5A9cf_w1pGYir8EzhJiq4-oX4NIoN7c5O0kIaEUch4mmj5Jx_KGIgJJ3hHDJ9VizHl81KF8BeGZIjqoi5u3CumbsNqtJw5eFCFnazL-B60elLSGia7VyeM6K40Un4N83HEHTpMccWUZQ3ChZfA5FxxBpxoi-N9gF19oFr2Qo0iCL7HgB4e9A8W5oJnmOMbrGvSKk-LUedLNLJmbW_flvBmA3Dk7Zr7fH_DHgzcX0wm9-RwFh71uM-wh63leKTXyyHVrQFVGw7l6h-IQ2fALT6HeubgYEnP95XATYA2oMdS4VsQUjdHHUQe_LnMqpkLjMl4GAbXxG7X3R8fc8PTgeqPTKRpFfyJV-RWtQQONTw-tKxJV-B9CsIlB4L3gnaDCEYoFrXGd9LFoKzBh2blZ6O-Vr_RzmW3fkRZEvLki82TAuzLPxaxuTj0nJBkC09oiP2AG-bFfVba55_ldHGaE_H4UbD3eAUoi-d7Vu9JldJTQLtlmepbTgtU4QymMW3IjbeoOfv7a7voyYbllKZ7HUmMKT2BA2kBHT5sH9iIsTZSxE9UB0U8zb1vjosjQg-ic88rD2gkapgLMU0pEz96vkDfbELov_m-lQjQBM-db-Q-TR8aLBQyPIJp54Z2rgpJ_b9rju_2emhbsSWyCXAzuxZX_2SC4QIv_OxsrPWgQTIs5AJHeQdlj6sQIQS230y7Z2xFbzRVDYGdKzyMEwEcZUohVZj4xSuhaCO6Ohi4DCKmx7Mdv4PqIH8qAcFlMMp4ZnjpPXwyoYfwcN8MvAe2oLm2Ys8zqrLRd1mV-GfplbZiBvnvMo_rUV4vg4yclsLrEeyKizjy8xRskQwvBHF_vUMHtpj4PQjn5h_QzsBPYc-UggBeE2rOQNgb4KpZ_SXW2_tR2Z6JknJ81DcK2WHgpw%3D%3D&searchType=oneWay&searchBags=0.0", "trackingPixel": "https://www.kiwi.com/pixel?affilid=cj&currency=CNY&flightsId=0f640fa04f1b0000902a7bb2_0&from=LHR&lang=cn&passengers=1&searchBags=0.0&to=PKX&booking_token=HYrnaVoFf4Y4hXm2508zGpHuIowhUNgTnjjAJF83UnX0hvvEA2KWyN2I87PNW5lvjVKkTOn1bqZ09VNdbBXAI5gkGROVd3DN_gXyrYca9hVuVrxQTF31CL0v263Xa_cJci-5A9cf_w1pGYir8EzhJiq4-oX4NIoN7c5O0kIaEUch4mmj5Jx_KGIgJJ3hHDJ9VizHl81KF8BeGZIjqoi5u3CumbsNqtJw5eFCFnazL-B60elLSGia7VyeM6K40Un4N83HEHTpMccWUZQ3ChZfA5FxxBpxoi-N9gF19oFr2Qo0iCL7HgB4e9A8W5oJnmOMbrGvSKk-LUedLNLJmbW_flvBmA3Dk7Zr7fH_DHgzcX0wm9-RwFh71uM-wh63leKTXyyHVrQFVGw7l6h-IQ2fALT6HeubgYEnP95XATYA2oMdS4VsQUjdHHUQe_LnMqpkLjMl4GAbXxG7X3R8fc8PTgeqPTKRpFfyJV-RWtQQONTw-tKxJV-B9CsIlB4L3gnaDCEYoFrXGd9LFoKzBh2blZ6O-Vr_RzmW3fkRZEvLki82TAuzLPxaxuTj0nJBkC09oiP2AG-bFfVba55_ldHGaE_H4UbD3eAUoi-d7Vu9JldJTQLtlmepbTgtU4QymMW3IjbeoOfv7a7voyYbllKZ7HUmMKT2BA2kBHT5sH9iIsTZSxE9UB0U8zb1vjosjQg-ic88rD2gkapgLMU0pEz96vkDfbELov_m-lQjQBM-db-Q-TR8aLBQyPIJp54Z2rgpJ_b9rju_2emhbsSWyCXAzuxZX_2SC4QIv_OxsrPWgQTIs5AJHeQdlj6sQIQS230y7Z2xFbzRVDYGdKzyMEwEcZUohVZj4xSuhaCO6Ohi4DCKmx7Mdv4PqIH8qAcFlMMp4ZnjpPXwyoYfwcN8MvAe2oLm2Ys8zqrLRd1mV-GfplbZiBvnvMo_rUV4vg4yclsLrEeyKizjy8xRskQwvBHF_vUMHtpj4PQjn5h_QzsBPYc-UggBeE2rOQNgb4KpZ_SXW2_tR2Z6JknJ81DcK2WHgpw==", "itineraryProvider": {"code": "KIWI-BASIC", "name": "Kiwi.com", "subprovider": null, "hasHighProbabilityOfPriceChange": false, "contentProvider": null, "providerCategory": null, "id": "ItineraryProvider:KIWI-BASIC"}, "price": {"amount": "4319"}, "priceEur": {"amount": "529.02"}, "priceLocks": {"priceLocksCurr": [{"default": false, "price": {"amount": "215.95", "roundedFormattedValue": "¥216"}}, {"default": true, "price": {"amount": "431.9", "roundedFormattedValue": "¥432"}}, {"default": false, "price": {"amount": "1079.75", "roundedFormattedValue": "¥1,080"}}], "priceLocksEur": [{"default": false, "price": {"amount": "26.45", "roundedFormattedValue": "26 €"}}, {"default": true, "price": {"amount": "52.9", "roundedFormattedValue": "53 €"}}, {"default": false, "price": {"amount": "132.25", "roundedFormattedValue": "132 €"}}]}, "kiwiProduct": "KIWI_BASIC", "disruptionTreatment": "NO_PROTECTION", "usRulesApply": false}}, {"node": {"token": "Ha89cvHAsyW5tjg3FF6o7dV3-JV3qz93tEl_FwKrcckpjkeTzuHIVeh75s5u_1u8UWLp9vjN5PmXt1sbUZJHKP5v3Ha1qaSteMTXOvxHB4bQw9Tmxh72LK-A9uSlo-5jrjsMk14Cwy9jSWWgHABc4jASvtfGTdZMZheEEokCMfRCriZ5E8saZj0UzALUuB2tqGR0zxpaRDbn7dNpfUNVOITEOcs6xMGrj1amRoIPXjB95k81vDYbe_iT8GAbjGOX9109aXKIULvxiDMRlK-EBMcy6p_qc_xlSZAUOhyqUqVTpjGg0XtRBpYkyJOOQoZyE3kF834JeRz59PjTkS7CU4tX0AB2HJECdImMEP5e1gpTva1_Rvq9gKdynXcfJoNba8m0AFAux33eSX8wD2j7Xt-6Nt4m4xcFmPmxoXzgUA1XwDj-pAS1CsI5xW405b5MG_4BLOQKSHq-tPLYj21UtEGNtujfEDZ0ztnoUG7280wzAL7Yt6cNG_u47fLzZeM5cyFTgv0h4RON9_RuW9lvtELCWg_5FCD7OP73UBx5CU_MZ3wZJgMQXJRX3RPbtpY3AgBWEptVt5EWmqOFbeodUonTomzDDF15cg7EuJOMCs1vFYxsLU8K17tVEKTfZHToBrgdwQyPM0YAinFjKKNFgrj00XbcshtOuCshLf8ygYbirbOGAvOEcKszr6UZ2-SCqiCjjLqjs_kMlVojsJtNRpSuWdCEIfMZfwIq6ylyaBzrVZWcoNJ4UogCnuu4b1TTV4xH0_lbHvzj9ZObk8iP9nHr4p9kI5nvY_RvhbBvlC7pPyDHzB-TSCZAsrSBOvDRtxI-GiFpeiXhWwDq3riRnzCnW8JuCdYcPt5zOGSClfxX0I1G1haX65IOqHBnCZnFFejvyovN4qJ8rxb8Qv6fkd2fppZReU-fT5wHg6OSjaL3FfQ1YYNAz0qQAW8iNkTy7m3DJpeP6xBTUUxX22v2MLhMlpmVTkGk_IctbJEw060493FahmElaC4rA4M1-fwiIr8mmdMfDfSnYVqPbu_drfwJvd1ozV_6RjWYoVHLPTYLCg2rXPY-eUjRXT61zKHeR3c_MFlD3cCM4VcaDW_QE2IiAoLH7nplfQkspSQEiuTI=", "bookingUrl": "/cn/booking/?direct=true&locale=cn&currency=cny&passengers=1-0-0&token=Ha89cvHAsyW5tjg3FF6o7dV3-JV3qz93tEl_FwKrcckpjkeTzuHIVeh75s5u_1u8UWLp9vjN5PmXt1sbUZJHKP5v3Ha1qaSteMTXOvxHB4bQw9Tmxh72LK-A9uSlo-5jrjsMk14Cwy9jSWWgHABc4jASvtfGTdZMZheEEokCMfRCriZ5E8saZj0UzALUuB2tqGR0zxpaRDbn7dNpfUNVOITEOcs6xMGrj1amRoIPXjB95k81vDYbe_iT8GAbjGOX9109aXKIULvxiDMRlK-EBMcy6p_qc_xlSZAUOhyqUqVTpjGg0XtRBpYkyJOOQoZyE3kF834JeRz59PjTkS7CU4tX0AB2HJECdImMEP5e1gpTva1_Rvq9gKdynXcfJoNba8m0AFAux33eSX8wD2j7Xt-6Nt4m4xcFmPmxoXzgUA1XwDj-pAS1CsI5xW405b5MG_4BLOQKSHq-tPLYj21UtEGNtujfEDZ0ztnoUG7280wzAL7Yt6cNG_u47fLzZeM5cyFTgv0h4RON9_RuW9lvtELCWg_5FCD7OP73UBx5CU_MZ3wZJgMQXJRX3RPbtpY3AgBWEptVt5EWmqOFbeodUonTomzDDF15cg7EuJOMCs1vFYxsLU8K17tVEKTfZHToBrgdwQyPM0YAinFjKKNFgrj00XbcshtOuCshLf8ygYbirbOGAvOEcKszr6UZ2-SCqiCjjLqjs_kMlVojsJtNRpSuWdCEIfMZfwIq6ylyaBzrVZWcoNJ4UogCnuu4b1TTV4xH0_lbHvzj9ZObk8iP9nHr4p9kI5nvY_RvhbBvlC7pPyDHzB-TSCZAsrSBOvDRtxI-GiFpeiXhWwDq3riRnzCnW8JuCdYcPt5zOGSClfxX0I1G1haX65IOqHBnCZnFFejvyovN4qJ8rxb8Qv6fkd2fppZReU-fT5wHg6OSjaL3FfQ1YYNAz0qQAW8iNkTy7m3DJpeP6xBTUUxX22v2MLhMlpmVTkGk_IctbJEw060493FahmElaC4rA4M1-fwiIr8mmdMfDfSnYVqPbu_drfwJvd1ozV_6RjWYoVHLPTYLCg2rXPY-eUjRXT61zKHeR3c_MFlD3cCM4VcaDW_QE2IiAoLH7nplfQkspSQEiuTI%3D&searchType=oneWay&searchBags=0.0", "trackingPixel": "https://www.kiwi.com/pixel?affilid=cj&currency=CNY&flightsId=0f640fa04f1b0000902a7bb2_0&from=LHR&lang=cn&passengers=1&searchBags=0.0&to=PKX&booking_token=Ha89cvHAsyW5tjg3FF6o7dV3-JV3qz93tEl_FwKrcckpjkeTzuHIVeh75s5u_1u8UWLp9vjN5PmXt1sbUZJHKP5v3Ha1qaSteMTXOvxHB4bQw9Tmxh72LK-A9uSlo-5jrjsMk14Cwy9jSWWgHABc4jASvtfGTdZMZheEEokCMfRCriZ5E8saZj0UzALUuB2tqGR0zxpaRDbn7dNpfUNVOITEOcs6xMGrj1amRoIPXjB95k81vDYbe_iT8GAbjGOX9109aXKIULvxiDMRlK-EBMcy6p_qc_xlSZAUOhyqUqVTpjGg0XtRBpYkyJOOQoZyE3kF834JeRz59PjTkS7CU4tX0AB2HJECdImMEP5e1gpTva1_Rvq9gKdynXcfJoNba8m0AFAux33eSX8wD2j7Xt-6Nt4m4xcFmPmxoXzgUA1XwDj-pAS1CsI5xW405b5MG_4BLOQKSHq-tPLYj21UtEGNtujfEDZ0ztnoUG7280wzAL7Yt6cNG_u47fLzZeM5cyFTgv0h4RON9_RuW9lvtELCWg_5FCD7OP73UBx5CU_MZ3wZJgMQXJRX3RPbtpY3AgBWEptVt5EWmqOFbeodUonTomzDDF15cg7EuJOMCs1vFYxsLU8K17tVEKTfZHToBrgdwQyPM0YAinFjKKNFgrj00XbcshtOuCshLf8ygYbirbOGAvOEcKszr6UZ2-SCqiCjjLqjs_kMlVojsJtNRpSuWdCEIfMZfwIq6ylyaBzrVZWcoNJ4UogCnuu4b1TTV4xH0_lbHvzj9ZObk8iP9nHr4p9kI5nvY_RvhbBvlC7pPyDHzB-TSCZAsrSBOvDRtxI-GiFpeiXhWwDq3riRnzCnW8JuCdYcPt5zOGSClfxX0I1G1haX65IOqHBnCZnFFejvyovN4qJ8rxb8Qv6fkd2fppZReU-fT5wHg6OSjaL3FfQ1YYNAz0qQAW8iNkTy7m3DJpeP6xBTUUxX22v2MLhMlpmVTkGk_IctbJEw060493FahmElaC4rA4M1-fwiIr8mmdMfDfSnYVqPbu_drfwJvd1ozV_6RjWYoVHLPTYLCg2rXPY-eUjRXT61zKHeR3c_MFlD3cCM4VcaDW_QE2IiAoLH7nplfQkspSQEiuTI=", "itineraryProvider": {"code": "KIWI", "name": "Kiwi.com", "subprovider": null, "hasHighProbabilityOfPriceChange": false, "contentProvider": {"code": "KIWI"}, "providerCategory": null, "id": "ItineraryProvider:KIWI"}, "price": {"amount": "4799"}, "priceEur": {"amount": "587.81"}, "priceLocks": {"priceLocksCurr": [{"default": false, "price": {"amount": "239.95", "roundedFormattedValue": "¥240"}}, {"default": true, "price": {"amount": "479.9", "roundedFormattedValue": "¥480"}}, {"default": false, "price": {"amount": "1199.75", "roundedFormattedValue": "¥1,200"}}], "priceLocksEur": [{"default": false, "price": {"amount": "29.39", "roundedFormattedValue": "29 €"}}, {"default": true, "price": {"amount": "58.78", "roundedFormattedValue": "59 €"}}, {"default": false, "price": {"amount": "146.95", "roundedFormattedValue": "147 €"}}]}, "kiwiProduct": "KIWI_BENEFITS", "disruptionTreatment": "DISRUPTION_PROTECTION", "usRulesApply": null}}]}, "travelHack": {"isTrueHiddenCity": false, "isVirtualInterlining": false, "isThrowawayTicket": false}, "duration": 36900, "pnrCount": 1, "sector": {"id": "Sector:4fff6654af63bb4f848453d32185cfb1e8fbdab5~4fff6654af63bb4f848453d32185cfb1e8fbdab5", "sectorSegments": [{"segment": {"source": {"station": {"code": "LHR", "type": "AIRPORT", "city": {"name": "伦敦", "id": "City:london_gb", "legacyId": "london_gb"}, "id": "Station:airport:LHR", "legacyId": "LHR", "name": "伦敦希思罗机场", "gps": {"lat": 51.4775, "lng": -0.4613889}, "country": {"code": "GB", "id": "Country:GB"}}, "localTime": "2025-06-12T21:00:00", "utcTimeIso": "2025-06-12T20:00:00Z"}, "destination": {"station": {"code": "PKX", "type": "AIRPORT", "city": {"name": "北京市", "id": "City:beijing_cn", "legacyId": "beijing_cn"}, "id": "Station:airport:PKX", "legacyId": "PKX", "name": "北京大兴国际机场", "gps": {"lat": 39.509167, "lng": 116.410556}, "country": {"code": "CN", "id": "Country:CN"}}, "localTime": "2025-06-13T14:15:00", "utcTimeIso": "2025-06-13T06:15:00Z"}, "id": "Segment:4fff6654af63bb4f848453d32185cfb1e8fbdab5", "duration": 36900, "type": "FLIGHT", "code": "674", "carrier": {"id": "Carrier:242", "name": "China Southern Airlines", "code": "CZ"}, "operatingCarrier": {"id": "Carrier:242", "name": "China Southern Airlines", "code": "CZ"}, "cabinClass": "ECONOMY", "hiddenDestination": null, "throwawayDestination": null}, "guarantee": null, "layover": null}], "duration": 36900}, "legacyId": "0f640fa04f1b0000902a7bb2_0", "lastAvailable": {"seatsLeft": 9}, "isRyanair": false, "benefitsData": {"automaticCheckinAvailable": false, "instantChatSupportAvailable": true, "disruptionProtectionAvailable": true, "guaranteeAvailable": false, "guaranteeFee": null, "guaranteeFeeEur": null, "searchReferencePrice": {"roundedAmount": "4319"}}, "isAirBaggageBundleEligible": null, "testEligibilityInformation": {"paretoABTestNewItinerary": null}}]}}}