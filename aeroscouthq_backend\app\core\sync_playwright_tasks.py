"""
Synchronous Playwright tasks for Celery workers.

This module contains synchronous implementations of Playwright tasks
that can be safely used with Celery workers on Windows.
"""
import json
import logging
import time
from typing import Optional, Dict, Any

from playwright.sync_api import sync_playwright, <PERSON>rror as PlaywrightError
from celery.exceptions import MaxRetriesExceededError, Retry

from app.celery_worker import celery_app
from app.core.config import settings
from app.core.task_status_manager import get_task_status_manager

logger = logging.getLogger(__name__)

# --- Synchronous Playwright Session Fetching Tasks ---

@celery_app.task(bind=True, max_retries=3, default_retry_delay=60, acks_late=True)
def fetch_trip_session_task_sync(self) -> Optional[Dict[str, str]]:
    """
    Celery task to fetch Trip.com session data (headers) using synchronous Playwright.
    This version is designed to work with Windows Celery workers.
    Retries on failure.
    """
    attempt_number = self.request.retries + 1
    logger.info(f"[Trip.com Sync Celery Task Attempt {attempt_number}/{self.max_retries + 1}] Starting fetch...")
    playwright_overall_timeout = 60000  # 60s
    navigation_timeout = 30000      # 30s
    post_navigation_wait = 7000     # 7s

    local_captured_headers: Dict[str, str] = {}
    success_flag = False

    try:
        with sync_playwright() as p:
            browser = None
            context = None
            page = None
            try:
                logger.info(f"[Trip.com Sync Celery Task Attempt {attempt_number}] Launching browser...")
                browser = p.chromium.launch(headless=True)

                logger.info(f"[Trip.com Sync Celery Task Attempt {attempt_number}] Creating browser context...")
                context = browser.new_context(
                    user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
                    locale="zh-HK",
                    timezone_id="Asia/Hong_Kong",
                )
                context.set_default_timeout(playwright_overall_timeout)

                logger.info(f"[Trip.com Sync Celery Task Attempt {attempt_number}] Creating new page...")
                page = context.new_page()

                target_url = "https://hk.trip.com/flights/"
                logger.info(f"[Trip.com Sync Celery Task Attempt {attempt_number}] Navigating to: {target_url}")

                try:
                    page.goto(target_url, timeout=navigation_timeout, wait_until="domcontentloaded")
                    logger.info(f"[Trip.com Sync Celery Task Attempt {attempt_number}] Page '{target_url}' DOM content loaded.")
                except PlaywrightError as e_goto:
                    logger.warning(f"[Trip.com Sync Celery Task Attempt {attempt_number}] page.goto timed out or failed: {str(e_goto)[:200]}. Proceeding.")
                    # Continue even if goto fails

                logger.info(f"[Trip.com Sync Celery Task Attempt {attempt_number}] Waiting {post_navigation_wait/1000}s...")
                page.wait_for_timeout(post_navigation_wait)

                # Try to accept cookie banner
                cookie_accept_selectors = [
                    '#ibu_cookie_banner_agree',
                    'div[data-eventhastype*="Popup"] button[data-eventhastype*="Accept"]',
                    'button[class*="cookie_accept"]',
                    '//button[contains(text(),"接受") or contains(text(),"Accept") or contains(text(),"Agree")]'
                ]
                for selector in cookie_accept_selectors:
                    try:
                        element = page.query_selector(selector)
                        if element and element.is_visible(timeout=2000):
                            logger.info(f"[Trip.com Sync Celery Task Attempt {attempt_number}] Clicking cookie accept button ({selector})")
                            element.click(timeout=3000)
                            page.wait_for_timeout(1000)
                            logger.info(f"[Trip.com Sync Celery Task Attempt {attempt_number}] Cookie button clicked.")
                            break
                    except PlaywrightError:
                        logger.debug(f"[Trip.com Sync Celery Task Attempt {attempt_number}] Cookie button selector '{selector}' not found/failed.")
                        pass

                logger.info(f"[Trip.com Sync Celery Task Attempt {attempt_number}] Extracting cookies...")
                cookies_list = context.cookies(urls=[target_url])
                if cookies_list:
                    cookies_str = "; ".join([f"{cookie['name']}={cookie['value']}" for cookie in cookies_list])
                    logger.info(f"[Trip.com Sync Celery Task Attempt {attempt_number}] Extracted cookies (partial): {cookies_str[:150]}...")

                    local_captured_headers = {
                        'accept': '*/*',
                        'accept-language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7,en-GB;q=0.6',
                        'content-type': 'application/json',
                        'origin': 'https://hk.trip.com',
                        'referer': target_url,
                        'sec-ch-ua': '"Chromium";v="125", "Microsoft Edge";v="125", "Not.A/Brand";v="24"',
                        'sec-ch-ua-mobile': '?0',
                        'sec-ch-ua-platform': '"Windows"',
                        'sec-fetch-dest': 'empty',
                        'sec-fetch-mode': 'cors',
                        'sec-fetch-site': 'same-origin',
                        'x-ibu-flt-currency': 'CNY',
                        'x-ibu-flt-language': 'hk',
                        'x-ibu-flt-locale': 'zh-HK',
                        'cookie': cookies_str
                    }
                    try:
                        if page and not page.is_closed():
                            user_agent = page.evaluate("() => navigator.userAgent")
                            local_captured_headers['user-agent'] = user_agent
                            logger.info(f"[Trip.com Sync Celery Task Attempt {attempt_number}] Extracted User-Agent: {user_agent}")
                        else:
                            # Need to create and close temp page if main page is closed
                            temp_page_for_ua = context.new_page()
                            ua = temp_page_for_ua.evaluate("() => navigator.userAgent")
                            local_captured_headers['user-agent'] = ua
                            if not temp_page_for_ua.is_closed():
                                temp_page_for_ua.close()
                            logger.info(f"[Trip.com Sync Celery Task Attempt {attempt_number}] Extracted User-Agent via temp page.")
                    except Exception as ua_e:
                        logger.warning(f"[Trip.com Sync Celery Task Attempt {attempt_number}] Failed to get User-Agent, using default: {ua_e}")
                        local_captured_headers['user-agent'] = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********'

                    success_flag = True
                else:
                    logger.error(f"[Trip.com Sync Celery Task Attempt {attempt_number}] Failed to extract any cookies.")

            except PlaywrightError as e_playwright:
                logger.error(f"[Trip.com Sync Celery Task Attempt {attempt_number}] Playwright operation error: {e_playwright}")
                try:
                    raise self.retry(exc=e_playwright, countdown=60 * attempt_number)
                except MaxRetriesExceededError:
                    logger.critical(f"[Trip.com Sync Celery Task] Max retries exceeded for Playwright error: {e_playwright}")
                    return None
            except Exception as e_general:
                logger.error(f"[Trip.com Sync Celery Task Attempt {attempt_number}] Unexpected error: {e_general}", exc_info=True)
                try:
                    raise self.retry(exc=e_general, countdown=60 * attempt_number)
                except MaxRetriesExceededError:
                    logger.critical(f"[Trip.com Sync Celery Task] Max retries exceeded for general error: {e_general}")
                    return None
            finally:
                logger.debug(f"[Trip.com Sync Celery Task Attempt {attempt_number}] Cleaning up Playwright resources...")
                # Enhanced closing logic
                if page and not page.is_closed():
                    try:
                        logger.debug(f"[Trip.com Sync Celery Task Attempt {attempt_number}] Closing page...")
                        page.close()
                        logger.debug(f"[Trip.com Sync Celery Task Attempt {attempt_number}] Page closed.")
                    except Exception as e_pc:
                        logger.warning(f"[Trip.com Sync Celery Task Attempt {attempt_number}] Error closing page: {e_pc}")
                if context:
                    try:
                        logger.debug(f"[Trip.com Sync Celery Task Attempt {attempt_number}] Closing context...")
                        context.close()
                        logger.debug(f"[Trip.com Sync Celery Task Attempt {attempt_number}] Context closed.")
                    except Exception as e_cc:
                        logger.warning(f"[Trip.com Sync Celery Task Attempt {attempt_number}] Error closing context: {e_cc}")
                if browser and browser.is_connected():
                    try:
                        logger.info(f"[Trip.com Sync Celery Task Attempt {attempt_number}] Closing browser...")
                        browser.close()
                        logger.info(f"[Trip.com Sync Celery Task Attempt {attempt_number}] Browser closed.")
                    except Exception as e_bc:
                        logger.warning(f"[Trip.com Sync Celery Task Attempt {attempt_number}] Error closing browser: {e_bc}")

    except Exception as e_outer:
        logger.error(f"[Trip.com Sync Celery Task Attempt {attempt_number}] Error setting up/tearing down Playwright: {e_outer}", exc_info=True)
        try:
            raise self.retry(exc=e_outer, countdown=60 * attempt_number)
        except MaxRetriesExceededError:
            logger.critical(f"[Trip.com Sync Celery Task] Max retries exceeded for outer error: {e_outer}")
            return None

    if success_flag and 'cookie' in local_captured_headers and 'user-agent' in local_captured_headers:
        logger.info(f"[Trip.com Sync Celery Task Attempt {attempt_number}] Fetch successful.")
        # Save to file cache directly from the task upon success
        try:
            with open(settings.TRIP_COOKIE_FILE, 'w') as f:
                json.dump(local_captured_headers, f, indent=2)
            logger.info(f"Saved new Trip.com headers to cache file from task: {settings.TRIP_COOKIE_FILE}")
        except Exception as e:
            logger.error(f"Failed to save Trip.com headers to cache file from task {settings.TRIP_COOKIE_FILE}: {e}")
        return local_captured_headers
    else:
        logger.error(f"[Trip.com Sync Celery Task Attempt {attempt_number}] Fetch failed (no success flag or missing headers).")
        try:
            raise self.retry(exc=RuntimeError("Fetch failed logic check"), countdown=60 * attempt_number)
        except MaxRetriesExceededError:
            logger.critical(f"[Trip.com Sync Celery Task] Max retries exceeded after fetch logic failure.")
            return None
        except Retry:
            raise
        except Exception as e_final_retry:
            logger.error(f"[Trip.com Sync Celery Task] Unexpected error during final retry logic: {e_final_retry}")
            return None


@celery_app.task(bind=True, max_retries=3, default_retry_delay=60, acks_late=True)
def fetch_kiwi_session_task_sync(self) -> Optional[Dict[str, str]]:
    """
    Celery task to fetch Kiwi.com session data (headers) using synchronous Playwright.
    This version is designed to work with Windows Celery workers.
    Retries on failure.
    """
    # 导入状态管理器
    status_manager = get_task_status_manager()

    task_id = self.request.id
    task_type = "kiwi"

    # 设置任务为运行状态
    if not status_manager.set_task_running(task_type, task_id):
        logger.warning(f"[Kiwi.com Sync Celery Task] 任务类型 {task_type} 已有运行中的任务，当前任务将退出")
        return None

    attempt_number = self.request.retries + 1
    logger.info(f"[Kiwi.com Sync Celery Task Attempt {attempt_number}/{self.max_retries + 1}] Starting fetch... Task ID: {task_id}")
    playwright_overall_timeout = 60000  # 60s
    navigation_timeout = 30000      # 30s
    token_wait_timeout = 15.0       # 15s

    captured_headers_dict: Dict[str, str] = {}
    token_captured = False
    target_url = "https://www.kiwi.com/cn/search/tiles/--/--/anytime/anytime"
    ua = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********'

    try:
        with sync_playwright() as p:
            browser = None
            context = None
            page = None
            try:
                logger.info(f"[Kiwi.com Sync Celery Task Attempt {attempt_number}] Launching browser...")
                browser = p.chromium.launch(headless=True)

                logger.info(f"[Kiwi.com Sync Celery Task Attempt {attempt_number}] Creating browser context...")
                context = browser.new_context(user_agent=ua)
                context.set_default_timeout(playwright_overall_timeout)

                logger.info(f"[Kiwi.com Sync Celery Task Attempt {attempt_number}] Creating new page...")
                page = context.new_page()

                # Set up request handler to capture headers
                def handle_request(request):
                    nonlocal captured_headers_dict, token_captured
                    if not token_captured and \
                       "api.skypicker.com/umbrella/v2/graphql" in request.url and \
                       request.method == "POST":

                        temp_headers = {}
                        required_keys = {
                            'kw-skypicker-visitor-uniqid', 'kw-umbrella-token',
                            'kw-x-rand-id', 'user-agent', 'content-type'
                        }
                        all_headers = request.headers
                        for key, value in all_headers.items():
                            if key.lower() in required_keys:
                                temp_headers[key.lower()] = value

                        if 'kw-umbrella-token' in temp_headers:
                            captured_headers_dict.clear()
                            captured_headers_dict.update(temp_headers)
                            logger.info(f"[Kiwi.com Sync Celery Task Attempt {attempt_number}] Captured key headers! kw-umbrella-token: {temp_headers['kw-umbrella-token'][:10]}...")
                            token_captured = True

                page.on("request", handle_request)

                logger.info(f"[Kiwi.com Sync Celery Task Attempt {attempt_number}] Navigating to: {target_url}")

                try:
                    page.goto(target_url, timeout=navigation_timeout, wait_until="domcontentloaded")
                    logger.info(f"[Kiwi.com Sync Celery Task Attempt {attempt_number}] Page navigation completed.")
                except PlaywrightError as e_goto:
                    logger.warning(f"[Kiwi.com Sync Celery Task Attempt {attempt_number}] page.goto timed out or failed: {str(e_goto)[:200]}. Checking if token was captured.")

                # Wait for token capture if not already captured
                if not token_captured:
                    logger.info(f"[Kiwi.com Sync Celery Task Attempt {attempt_number}] Token not captured yet, waiting up to {token_wait_timeout}s more...")
                    start_time = time.time()
                    while not token_captured and (time.time() - start_time) < token_wait_timeout:
                        page.wait_for_timeout(1000)  # Check every second
                        if token_captured:
                            break

                    if not token_captured:
                        logger.error(f"[Kiwi.com Sync Celery Task Attempt {attempt_number}] Timed out waiting for Kiwi token capture.")
                        raise PlaywrightError("Timed out waiting for Kiwi token capture after navigation.")

            except PlaywrightError as e_playwright:
                logger.error(f"[Kiwi.com Sync Celery Task Attempt {attempt_number}] Playwright operation error: {e_playwright}")
                # 设置任务失败状态
                status_manager.set_task_failed(task_type, task_id, f"Playwright error: {str(e_playwright)}")
                try:
                    raise self.retry(exc=e_playwright, countdown=60 * attempt_number)
                except MaxRetriesExceededError:
                    logger.critical(f"[Kiwi.com Sync Celery Task] Max retries exceeded for Playwright error: {e_playwright}")
                    status_manager.set_task_failed(task_type, task_id, f"Max retries exceeded: {str(e_playwright)}")
                    return None
            except Exception as e_general:
                logger.error(f"[Kiwi.com Sync Celery Task Attempt {attempt_number}] Unexpected error: {e_general}", exc_info=True)
                # 设置任务失败状态
                status_manager.set_task_failed(task_type, task_id, f"Unexpected error: {str(e_general)}")
                try:
                    raise self.retry(exc=e_general, countdown=60 * attempt_number)
                except MaxRetriesExceededError:
                    logger.critical(f"[Kiwi.com Sync Celery Task] Max retries exceeded for general error: {e_general}")
                    status_manager.set_task_failed(task_type, task_id, f"Max retries exceeded: {str(e_general)}")
                    return None
            finally:
                logger.debug(f"[Kiwi.com Sync Celery Task Attempt {attempt_number}] Cleaning up Kiwi Playwright resources...")
                if page and not page.is_closed():
                    try:
                        page.remove_listener("request", handle_request)
                        logger.debug(f"[Kiwi.com Sync Celery Task Attempt {attempt_number}] Closing page...")
                        page.close()
                        logger.debug(f"[Kiwi.com Sync Celery Task Attempt {attempt_number}] Page closed.")
                    except Exception as e_pc:
                        logger.warning(f"[Kiwi.com Sync Celery Task Attempt {attempt_number}] Error closing Kiwi page: {e_pc}")
                if context:
                    try:
                        logger.debug(f"[Kiwi.com Sync Celery Task Attempt {attempt_number}] Closing context...")
                        context.close()
                        logger.debug(f"[Kiwi.com Sync Celery Task Attempt {attempt_number}] Context closed.")
                    except Exception as e_cc:
                        logger.warning(f"[Kiwi.com Sync Celery Task Attempt {attempt_number}] Error closing Kiwi context: {e_cc}")
                if browser and browser.is_connected():
                    try:
                        logger.info(f"[Kiwi.com Sync Celery Task Attempt {attempt_number}] Closing Kiwi browser...")
                        browser.close()
                        logger.info(f"[Kiwi.com Sync Celery Task Attempt {attempt_number}] Kiwi browser closed.")
                    except Exception as e_bc:
                        logger.warning(f"[Kiwi.com Sync Celery Task Attempt {attempt_number}] Error closing Kiwi browser: {e_bc}")

    except Exception as e_outer:
        logger.error(f"[Kiwi.com Sync Celery Task Attempt {attempt_number}] Error setting up/tearing down Kiwi Playwright: {e_outer}", exc_info=True)
        # 设置任务失败状态
        status_manager.set_task_failed(task_type, task_id, f"Setup/teardown error: {str(e_outer)}")
        try:
            raise self.retry(exc=e_outer, countdown=60 * attempt_number)
        except MaxRetriesExceededError:
            logger.critical(f"[Kiwi.com Sync Celery Task] Max retries exceeded for outer error: {e_outer}")
            status_manager.set_task_failed(task_type, task_id, f"Max retries exceeded: {str(e_outer)}")
            return None

    if token_captured and 'kw-umbrella-token' in captured_headers_dict:
        if 'origin' not in captured_headers_dict: captured_headers_dict['origin'] = 'https://www.kiwi.com'
        if 'referer' not in captured_headers_dict: captured_headers_dict['referer'] = target_url
        if 'user-agent' not in captured_headers_dict:
            logger.warning(f"[Kiwi.com Sync Celery Task Attempt {attempt_number}] User-agent not captured, using default.")
            captured_headers_dict['user-agent'] = ua
        if 'content-type' not in captured_headers_dict:
            logger.warning(f"[Kiwi.com Sync Celery Task Attempt {attempt_number}] Content-Type not captured, using default.")
            captured_headers_dict['content-type'] = 'application/json'

        logger.info(f"[Kiwi.com Sync Celery Task Attempt {attempt_number}] Kiwi fetch successful. Headers: {json.dumps(captured_headers_dict)}")

        # Save to file cache directly from the task upon success
        try:
            # Ensure KIWI_TOKEN_FILE is defined and accessible
            token_file = settings.KIWI_TOKEN_FILE
            with open(token_file, 'w') as f:
                json.dump(captured_headers_dict, f, indent=2)
            logger.info(f"Saved new Kiwi.com headers to cache file from task: {token_file}")
        except AttributeError:
            logger.error("settings.KIWI_TOKEN_FILE is not defined in config.py! Cannot save Kiwi token.")
        except Exception as e:
            logger.error(f"Failed to save Kiwi.com headers to cache file from task: {e}")

        # 更新内存缓存（如果可能）
        try:
            from app.core.dynamic_fetcher import KIWI_CURRENT_HEADERS, KIWI_LAST_FETCH_TIME
            # 更新全局变量
            globals_dict = globals()
            if 'KIWI_CURRENT_HEADERS' in globals_dict:
                globals_dict['KIWI_CURRENT_HEADERS'] = captured_headers_dict.copy()
                globals_dict['KIWI_LAST_FETCH_TIME'] = time.time()
                logger.info("Updated in-memory Kiwi.com cache from task")
        except Exception as e:
            logger.warning(f"Failed to update in-memory cache: {e}")

        # 设置任务完成状态
        status_manager.set_task_completed(task_type, task_id, captured_headers_dict)

        return captured_headers_dict
    else:
        logger.error(f"[Kiwi.com Sync Celery Task Attempt {attempt_number}] Kiwi fetch failed: Token not captured or missing.")
        # 设置任务失败状态
        status_manager.set_task_failed(task_type, task_id, "Token not captured or missing")
        try:
            raise self.retry(exc=RuntimeError("Kiwi fetch failed logic check"), countdown=60 * attempt_number)
        except MaxRetriesExceededError:
            logger.critical(f"[Kiwi.com Sync Celery Task] Max retries exceeded after fetch logic failure.")
            status_manager.set_task_failed(task_type, task_id, "Max retries exceeded after fetch logic failure")
            return None
        except Retry:
            raise
        except Exception as e_final_retry:
            logger.error(f"[Kiwi.com Sync Celery Task] Unexpected error during final retry logic: {e_final_retry}")
            status_manager.set_task_failed(task_type, task_id, f"Final retry error: {str(e_final_retry)}")
            return None

# Direct synchronous function for fallback use
def fetch_trip_cookies_direct() -> Optional[Dict[str, str]]:
    """
    同步函数直接获取Trip.com的cookies和headers。
    在Celery任务失败时用作降级方案。

    Returns:
        包含Trip.com headers的字典，如果失败则返回None。
    """
    import logging
    logger = logging.getLogger(__name__)

    logger.info("[直接同步获取] 开始获取Trip.com的cookies和headers...")
    playwright_overall_timeout = 60000  # 60s
    navigation_timeout = 30000      # 30s
    post_navigation_wait = 7000     # 7s

    local_captured_headers: Dict[str, str] = {}
    success_flag = False

    try:
        with sync_playwright() as p:
            browser = None
            context = None
            page = None
            try:
                logger.info("[直接同步获取] 启动浏览器...")
                browser = p.chromium.launch(headless=True)

                logger.info("[直接同步获取] 创建浏览器上下文...")
                context = browser.new_context(
                    user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
                    locale="zh-HK",
                    timezone_id="Asia/Hong_Kong",
                )
                context.set_default_timeout(playwright_overall_timeout)

                logger.info("[直接同步获取] 创建新页面...")
                page = context.new_page()

                target_url = "https://hk.trip.com/flights/"
                logger.info(f"[直接同步获取] 导航到: {target_url}")

                try:
                    page.goto(target_url, timeout=navigation_timeout, wait_until="domcontentloaded")
                    logger.info(f"[直接同步获取] 页面 '{target_url}' DOM内容已加载。")
                except PlaywrightError as e_goto:
                    logger.warning(f"[直接同步获取] page.goto超时或失败: {str(e_goto)[:200]}. 继续执行。")
                    # 即使goto失败也继续执行

                logger.info(f"[直接同步获取] 等待{post_navigation_wait/1000}秒...")
                page.wait_for_timeout(post_navigation_wait)

                # 尝试接受cookie横幅
                cookie_accept_selectors = [
                    '#ibu_cookie_banner_agree',
                    'div[data-eventhastype*="Popup"] button[data-eventhastype*="Accept"]',
                    'button[class*="cookie_accept"]',
                    '//button[contains(text(),"接受") or contains(text(),"Accept") or contains(text(),"Agree")]'
                ]
                for selector in cookie_accept_selectors:
                    try:
                        element = page.query_selector(selector)
                        if element and element.is_visible(timeout=2000):
                            logger.info(f"[直接同步获取] 点击cookie接受按钮 ({selector})")
                            element.click(timeout=3000)
                            page.wait_for_timeout(1000)
                            logger.info(f"[直接同步获取] Cookie按钮已点击。")
                            break
                    except PlaywrightError:
                        logger.debug(f"[直接同步获取] Cookie按钮选择器 '{selector}' 未找到/失败。")
                        pass

                logger.info(f"[直接同步获取] 提取cookies...")
                cookies_list = context.cookies(urls=[target_url])
                if cookies_list:
                    cookies_str = "; ".join([f"{cookie['name']}={cookie['value']}" for cookie in cookies_list])
                    logger.info(f"[直接同步获取] 提取的cookies (部分): {cookies_str[:150]}...")

                    local_captured_headers = {
                        'accept': '*/*',
                        'accept-language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7,en-GB;q=0.6',
                        'content-type': 'application/json',
                        'origin': 'https://hk.trip.com',
                        'referer': target_url,
                        'sec-ch-ua': '"Chromium";v="125", "Microsoft Edge";v="125", "Not.A/Brand";v="24"',
                        'sec-ch-ua-mobile': '?0',
                        'sec-ch-ua-platform': '"Windows"',
                        'sec-fetch-dest': 'empty',
                        'sec-fetch-mode': 'cors',
                        'sec-fetch-site': 'same-origin',
                        'x-ibu-flt-currency': 'CNY',
                        'x-ibu-flt-language': 'hk',
                        'x-ibu-flt-locale': 'zh-HK',
                        'cookie': cookies_str
                    }
                    try:
                        if page and not page.is_closed():
                            user_agent = page.evaluate("() => navigator.userAgent")
                            local_captured_headers['user-agent'] = user_agent
                            logger.info(f"[直接同步获取] 提取的User-Agent: {user_agent}")
                        else:
                            # 如果主页面已关闭则创建新页面获取UA
                            temp_page_for_ua = context.new_page()
                            ua = temp_page_for_ua.evaluate("() => navigator.userAgent")
                            local_captured_headers['user-agent'] = ua
                            if not temp_page_for_ua.is_closed():
                                temp_page_for_ua.close()
                            logger.info(f"[直接同步获取] 通过临时页面提取User-Agent。")
                    except Exception as ua_e:
                        logger.warning(f"[直接同步获取] 获取User-Agent失败，使用默认值: {ua_e}")
                        local_captured_headers['user-agent'] = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********'

                    success_flag = True
                else:
                    logger.error(f"[直接同步获取] 无法提取任何cookies。")

            except PlaywrightError as e_playwright:
                logger.error(f"[直接同步获取] Playwright操作错误: {e_playwright}")
                return None
            except Exception as e_general:
                logger.error(f"[直接同步获取] 意外错误: {e_general}", exc_info=True)
                return None
            finally:
                logger.debug(f"[直接同步获取] 清理Playwright资源...")
                if page and not page.is_closed():
                    try:
                        logger.debug(f"[直接同步获取] 关闭页面...")
                        page.close()
                        logger.debug(f"[直接同步获取] 页面已关闭。")
                    except Exception as e_pc:
                        logger.warning(f"[直接同步获取] 关闭页面时出错: {e_pc}")
                    if context:
                        try:
                            logger.debug(f"[直接同步获取] 关闭context...")
                            context.close()
                            logger.debug(f"[直接同步获取] Context已关闭。")
                        except Exception as e_cc:
                            logger.warning(f"[直接同步获取] 关闭context时出错: {e_cc}")
                    if browser and browser.is_connected():
                        try:
                            logger.info(f"[直接同步获取] 关闭浏览器...")
                            browser.close()
                            logger.info(f"[直接同步获取] 浏览器已关闭。")
                        except Exception as e_bc:
                            logger.warning(f"[直接同步获取] 关闭浏览器时出错: {e_bc}")

    except Exception as e_outer:
        logger.error(f"[直接同步获取] 设置/关闭Playwright时出错: {e_outer}", exc_info=True)
        return None

    if success_flag and 'cookie' in local_captured_headers and 'user-agent' in local_captured_headers:
        logger.info(f"[直接同步获取] 获取成功。")
        try:
            with open(settings.TRIP_COOKIE_FILE, 'w') as f:
                json.dump(local_captured_headers, f, indent=2)
            logger.info(f"保存到文件: {settings.TRIP_COOKIE_FILE}")
        except Exception as e:
            logger.error(f"保存到文件失败: {e}")
        return local_captured_headers
    else:
        logger.error(f"[直接同步获取] 获取失败 (未设置成功标志或缺少headers)。")
        return None

# HTTP 方法获取 Trip.com 会话数据，不依赖 Playwright
def fetch_trip_cookies_http() -> Optional[Dict[str, str]]:
    """
    使用 HTTP 请求获取 Trip.com 的 cookies 和 headers。
    这个函数不依赖 Playwright，使用 requests 库实现。
    针对 Windows + Python 3.12 环境进行了优化。

    Returns:
        Dict[str, str]: 包含 Trip.com headers 的字典，如果失败则返回 None
    """
    import requests
    import json
    import time
    import ssl
    from urllib3.util import create_urllib3_context
    from app.core.config import settings

    logger.info("[HTTP方法] 开始获取Trip.com的cookies和headers...")

    # 设置标准的用户代理
    user_agent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********'

    # 设置请求头
    request_headers = {
        'User-Agent': user_agent,
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7,en-GB;q=0.6',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Cache-Control': 'max-age=0',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Sec-Fetch-User': '?1',
        'sec-ch-ua': '"Chromium";v="125", "Microsoft Edge";v="125", "Not.A/Brand";v="24"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"'
    }

    try:
        # 创建自定义会话，处理SSL和重定向
        session = requests.Session()

        # 为Python 3.12在Windows上配置SSL上下文
        # 这有助于解决某些SSL证书验证问题
        if hasattr(ssl, 'SSLContext'):
            ctx = create_urllib3_context()
            ctx.set_ciphers('DEFAULT@SECLEVEL=1')
            session.mount('https://', requests.adapters.HTTPAdapter(max_retries=3))

        # 访问主页面获取初始 cookies
        target_url = "https://hk.trip.com/flights/"
        logger.info(f"[HTTP方法] 访问目标页面: {target_url}")

        # 发送请求，禁用重定向以保留所有cookies
        try:
            response = session.get(
                target_url,
                headers=request_headers,
                timeout=30,
                allow_redirects=True,
                verify=True  # 在某些环境中可能需要设置为False
            )
        except requests.exceptions.SSLError as ssl_err:
            logger.warning(f"[HTTP方法] SSL错误: {ssl_err}，尝试禁用SSL验证...")
            # 如果SSL验证失败，尝试禁用验证
            response = session.get(
                target_url,
                headers=request_headers,
                timeout=30,
                allow_redirects=True,
                verify=False
            )
        except requests.exceptions.RequestException as e:
            logger.error(f"[HTTP方法] 请求错误: {e}")
            return None

        # 检查响应状态
        if response.status_code != 200:
            logger.error(f"[HTTP方法] 获取页面失败，状态码: {response.status_code}")
            return None

        logger.info(f"[HTTP方法] 页面访问成功，状态码: {response.status_code}")

        # 获取cookies
        cookies_dict = {}
        # 合并来自请求和响应的cookies
        for cookie in session.cookies:
            cookies_dict[cookie.name] = cookie.value

        # 如果cookies为空，尝试从响应头中提取
        if not cookies_dict and 'Set-Cookie' in response.headers:
            logger.info("[HTTP方法] 从响应头中提取cookies")
            cookie_headers = response.headers.get('Set-Cookie', '').split(', ')
            for header in cookie_headers:
                parts = header.split(';')[0].strip().split('=', 1)
                if len(parts) == 2:
                    cookies_dict[parts[0]] = parts[1]

        # 构建 cookie 字符串
        cookies_str = "; ".join([f"{k}={v}" for k, v in cookies_dict.items()])

        if not cookies_str:
            logger.warning("[HTTP方法] 未能获取任何cookies")
            return None

        logger.info(f"[HTTP方法] 成功获取cookies (部分): {cookies_str[:150]}...")

        # 构建完整的 headers - 确保包含所有原始代码中的关键字段
        headers = {
            'cookie': cookies_str,
            'user-agent': user_agent,
            'content-type': 'application/json',
            'origin': 'https://hk.trip.com',
            'referer': target_url,
            'accept': '*/*',
            'accept-language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7,en-GB;q=0.6',
            'sec-ch-ua': '"Chromium";v="125", "Microsoft Edge";v="125", "Not.A/Brand";v="24"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
            'x-ibu-flt-currency': 'CNY',
            'x-ibu-flt-language': 'hk',
            'x-ibu-flt-locale': 'zh-HK'
        }

        logger.info(f"[HTTP方法] 构建了完整的请求头，包含所有必要字段")

        # 保存到文件
        try:
            with open(settings.TRIP_COOKIE_FILE, 'w') as f:
                json.dump(headers, f, indent=2)
            logger.info(f"[HTTP方法] 保存headers到文件: {settings.TRIP_COOKIE_FILE}")
        except Exception as e:
            logger.error(f"[HTTP方法] 保存到文件失败: {e}")

        logger.info("[HTTP方法] 获取成功")
        return headers

    except Exception as e:
        logger.error(f"[HTTP方法] 出现异常: {e}", exc_info=True)
        return None




def get_fallback_kiwi_headers() -> Dict[str, str]:
    """
    返回固定的Kiwi headers作为降级策略。
    这些headers来自工作代码中验证过的固定值。

    Returns:
        Dict[str, str]: 固定的Kiwi headers
    """
    import uuid
    import random

    logger.info("[降级策略] 使用固定的Kiwi headers")

    # 生成新的visitor ID和rand ID以避免冲突
    visitor_uniqid = str(uuid.uuid4())
    rand_id = f"{random.randint(0x10000000, 0xffffffff):08x}"

    return {
        'content-type': 'application/json',
        'kw-skypicker-visitor-uniqid': visitor_uniqid,
        'kw-umbrella-token': '0d23674b463dadee841cc65da51e34fe47bbbe895ae13b69d42ece267c7a2f51',
        'kw-x-rand-id': rand_id,
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
        'origin': 'https://www.kiwi.com',
        'referer': 'https://www.kiwi.com/cn/search/tiles/--/--/anytime/anytime',
        'accept': '*/*',
        'accept-language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7,en-GB;q=0.6',
        'sec-ch-ua': '"Chromium";v="125", "Microsoft Edge";v="125", "Not.A/Brand";v="24"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-origin'
    }

def validate_kiwi_token(headers: Dict[str, str]) -> bool:
    """
    验证Kiwi token的有效性。
    发送一个简单的GraphQL请求来测试token是否有效。

    Args:
        headers: 包含Kiwi token的headers字典

    Returns:
        bool: True如果token有效，False如果无效
    """
    import requests

    if not headers or 'kw-umbrella-token' not in headers:
        logger.warning("[Token验证] Headers中缺少kw-umbrella-token")
        return False

    logger.info(f"[Token验证] 验证token: {headers['kw-umbrella-token'][:10]}...")

    # 构建一个简单的GraphQL查询来测试token
    test_payload = {
        "query": """
        query TestQuery {
            __typename
        }
        """,
        "variables": {}
    }

    try:
        response = requests.post(
            "https://api.skypicker.com/umbrella/v2/graphql",
            headers=headers,
            json=test_payload,
            timeout=15,
            verify=False  # 在某些环境中可能需要禁用SSL验证
        )

        if response.status_code == 200:
            try:
                data = response.json()
                # 检查是否有错误信息
                if 'errors' in data:
                    error_msg = str(data['errors'])
                    if 'token' in error_msg.lower() or 'unauthorized' in error_msg.lower():
                        logger.warning(f"[Token验证] Token无效，错误信息: {error_msg}")
                        return False
                    else:
                        logger.info("[Token验证] Token验证成功（有非关键错误）")
                        return True
                else:
                    logger.info("[Token验证] Token验证成功")
                    return True
            except Exception as e:
                logger.warning(f"[Token验证] 解析响应时出错: {e}")
                # 如果能获得200响应，即使解析失败也认为token可能有效
                return True
        elif response.status_code in [401, 403]:
            logger.warning(f"[Token验证] Token无效，状态码: {response.status_code}")
            return False
        else:
            logger.warning(f"[Token验证] 验证请求失败，状态码: {response.status_code}")
            # 对于其他状态码，保守地认为token可能有效（可能是网络问题）
            return True

    except requests.exceptions.RequestException as e:
        logger.warning(f"[Token验证] 验证请求异常: {e}")
        # 网络异常时保守地认为token可能有效
        return True
    except Exception as e:
        logger.error(f"[Token验证] 验证过程中出现异常: {e}")
        return False