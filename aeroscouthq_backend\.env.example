# Database
DATABASE_URL=sqlite+aiosqlite:///./aeroscout.db

# JWT
SECRET_KEY=your_secret_key_here_please_change_me
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# API Limits - Specific limits per API type
# API_CALL_LIMIT_PER_DAY=100 # General limit (commented out, prefer specific limits)
POI_DAILY_LIMIT=100 # Daily limit for POI related endpoints
FLIGHT_DAILY_LIMIT=50 # Daily limit for flight search endpoints

# Dynamic Fetcher Cache Files
TRIP_COOKIE_FILE=trip_cookies.json
KIWI_TOKEN_FILE=kiwi_token.json

# Hub Probing (Example, ensure valid JSON list format)
CHINA_HUB_CITIES_FOR_PROBE='["SHA", "PVG", "PEK", "PKX", "CAN", "CTU", "TFU", "SZX"]'

# 日志配置
LOG_LEVEL=WARNING  # 日志级别: DEBUG, INFO, WARNING, ERROR, CRITICAL
ENABLE_SEARCH_DEBUG_LOGS=false  # 是否启用搜索过程的详细调试日志

# Testing flag (Optional, for database connection)
TESTING=False
# Cookie Expiry Configuration (in seconds)
TRIP_COOKIE_EXPIRY_SECONDS=3600
KIWI_COOKIE_EXPIRY_SECONDS=3600
# Celery Configuration (Example using Redis)
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/1