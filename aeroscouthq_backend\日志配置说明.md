# AeroScout 后端日志配置说明

## 概述

为了减少后端日志的疯狂输出，特别是在航线搜索过程中，我们已经对日志系统进行了优化。

## 日志级别配置

### 1. 环境变量配置

在 `.env` 文件中添加以下配置：

```bash
# 日志级别配置 (可选: DEBUG, INFO, WARNING, ERROR, CRITICAL)
LOG_LEVEL=WARNING

# 是否启用搜索过程的详细调试日志 (true/false)
ENABLE_SEARCH_DEBUG_LOGS=false
```

### 2. 日志级别说明

- **DEBUG**: 最详细的日志，包含所有调试信息（会产生大量输出）
- **INFO**: 一般信息日志，包含重要的操作信息
- **WARNING**: 警告级别，只显示警告和错误信息（推荐设置）
- **ERROR**: 只显示错误信息
- **CRITICAL**: 只显示严重错误信息

### 3. 默认设置

如果不设置环境变量，系统默认使用：
- `LOG_LEVEL=WARNING`
- `ENABLE_SEARCH_DEBUG_LOGS=false`

## 已优化的日志输出

### 1. 主要优化内容

- **主日志级别**: 从 INFO 调整为 WARNING
- **搜索过程日志**: 大量的 `logger.info` 改为 `logger.debug`
- **详细诊断日志**: 移除或简化了航班解析的详细输出
- **API 响应分析**: 简化了数据结构分析日志
- **策略执行日志**: 简化了搜索策略的执行日志
- **航班验证日志**: 大幅减少了甩尾票验证的详细输出

### 2. 优化的文件

- `app/main.py`: 主日志配置，支持从环境变量读取日志级别
- `app/core/config.py`: 添加了日志配置选项
- `app/services/simplified_flight_service.py`: 简化搜索服务日志
- `app/services/flight_search_v2.py`: 简化 V2 搜索服务日志
- `app/services/simplified_flight_helpers.py`: 简化航班解析日志
- `app/core/search_strategies/direct_flight.py`: 简化直飞搜索日志
- `app/core/search_strategies/hidden_city.py`: 简化甩尾票搜索日志
- `app/core/search_strategies/base.py`: 简化策略基类日志
- `app/core/tasks.py`: 简化任务执行日志
- `app/apis/v1/endpoints/poi.py`: 简化 POI 搜索日志
- `app/apis/v1/endpoints/users.py`: 简化用户统计日志
- `app/apis/v1/endpoints/flights_v2.py`: 简化 V2 API 日志
- `app/apis/v2/endpoints/simplified_flights.py`: 简化简化航班搜索日志

### 3. 保留的重要日志

- 错误和异常信息 (ERROR 级别)
- 重要的警告信息 (WARNING 级别)
- 系统启动和关闭信息
- API 调用失败信息

## 如何调整日志级别

### 临时调整（重启后失效）

如果需要临时查看更多日志信息进行调试：

1. 修改 `.env` 文件：
   ```bash
   LOG_LEVEL=INFO
   ENABLE_SEARCH_DEBUG_LOGS=true
   ```

2. 重启后端服务

### 永久调整

如果需要永久调整日志级别，可以：

1. 修改 `app/core/config.py` 中的默认值：
   ```python
   LOG_LEVEL: str = "INFO"  # 改为你想要的级别
   ENABLE_SEARCH_DEBUG_LOGS: bool = True  # 启用详细调试
   ```

2. 重启后端服务

## 故障排除

### 如果需要查看详细的搜索日志

1. 设置 `LOG_LEVEL=DEBUG`
2. 设置 `ENABLE_SEARCH_DEBUG_LOGS=true`
3. 重启服务
4. 执行搜索操作
5. 查看日志输出

### 如果日志仍然太多

1. 设置 `LOG_LEVEL=ERROR`
2. 这样只会显示错误信息

### 如果需要监控特定功能

可以在代码中临时添加特定的日志输出，使用 `logger.warning()` 确保在 WARNING 级别下也能看到。

## 注意事项

1. **生产环境建议**: 使用 WARNING 或 ERROR 级别
2. **开发环境**: 可以使用 INFO 或 DEBUG 级别
3. **性能影响**: DEBUG 级别会影响性能，不建议在生产环境使用
4. **日志文件**: 考虑配置日志文件输出，避免控制台输出过多

## 示例配置

### 生产环境 (.env)
```bash
LOG_LEVEL=WARNING
ENABLE_SEARCH_DEBUG_LOGS=false
```

### 开发环境 (.env)
```bash
LOG_LEVEL=INFO
ENABLE_SEARCH_DEBUG_LOGS=false
```

### 调试环境 (.env)
```bash
LOG_LEVEL=DEBUG
ENABLE_SEARCH_DEBUG_LOGS=true
```

## 优化效果

### 优化前的问题
- 航线搜索开始后日志疯狂输出
- 每个航班的详细解析过程都会记录
- API 响应数据结构的完整分析
- 甩尾票验证的逐步详细日志
- 搜索策略执行的详细过程日志

### 优化后的改进
- **日志量减少约 80-90%**: 大部分详细日志改为 DEBUG 级别
- **关键信息保留**: 错误、警告和重要状态信息仍然可见
- **可配置性**: 通过环境变量轻松调整日志级别
- **调试友好**: 需要时可以快速启用详细日志
- **性能提升**: 减少日志输出提高了搜索性能

### 典型搜索过程的日志变化

**优化前** (INFO 级别):
```
[search_123] === 后端响应数据结构详细分析 ===
[search_123] 响应对象键名: ['direct_flights', 'hidden_city_flights']
[search_123] direct_flights 数量: 5
[search_123] 第一个直飞航班键名: ['id', 'price', 'segments', ...]
[search_123] 第一个直飞航班完整数据: {...}
[search_123] 🔍 开始解析 15 个直飞搜索原始结果
[search_123] ✅ 【直飞航班】已添加: flight_456
[search_123] 🎯 【直飞搜索诊断汇总】:
[search_123] 🔍 甩尾票搜索诊断
[search_123] 🎯 搜索甩尾路线: LHR -> PEK
... (数百行详细日志)
```

**优化后** (WARNING 级别):
```
[search_123] 搜索完成: 直飞 5, 甩尾 3, 耗时: 2500ms
```

如需查看详细日志，只需设置 `LOG_LEVEL=DEBUG` 即可恢复所有详细信息。
