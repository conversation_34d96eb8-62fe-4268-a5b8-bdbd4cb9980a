{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/AeroScout/aeroscout-frontend/src/store/flightResultsStore.ts"], "sourcesContent": ["import { create } from 'zustand';\r\nimport { ApiFlightItinerary } from '@/lib/apiService';\r\n\r\nexport const POLLING_TIMEOUT_DURATION = 2 * 60 * 1000; // 2 minutes\r\nexport const MAX_CONSECUTIVE_FAILURES = 5;\r\n\r\nexport type SearchStatus = 'idle' | 'loading' | 'success' | 'error' | 'stopped'; // 添加 'stopped' 状态\r\nexport type PollingStoppedReason = 'timeout' | 'max_failures';\r\n\r\n// 定义更具体的类型\r\nexport interface AirportInfo {\r\n  code: string; // IATA code, e.g., \"PVG\"\r\n  name: string; // e.g., \"Shanghai Pudong International Airport\"\r\n  cityName: string; // e.g., \"Shanghai\" - mapped from apiService.ApiAirportInfo.city_name\r\n  cityCode?: string;\r\n  countryName?: string;\r\n  countryCode?: string;\r\n}\r\n\r\nexport interface AirlineInfo {\r\n  code: string; // IATA code\r\n  name: string;\r\n  logoUrl?: string; // mapped from apiService.ApiAirlineInfo.logo_url\r\n}\r\n\r\nexport interface FlightSegment {\r\n  id?: string;\r\n  airlineCode: string; // mapped from apiService.ApiFlightSegment.airline_code\r\n  airlineName: string; // mapped from apiService.ApiFlightSegment.airline_name\r\n  airlineLogoUrl?: string; // 航空公司 Logo 的 URL\r\n  flightNumber: string; // mapped from apiService.ApiFlightSegment.flight_number\r\n  departureAirportCode: string; // mapped from apiService.ApiFlightSegment.departure_airport_code\r\n  departureAirportName: string; // mapped from apiService.ApiFlightSegment.departure_airport_name\r\n  departureAirportFull?: string; // 出发机场的详细名称/信息\r\n  departureCityName: string; // mapped from apiService.ApiFlightSegment.departure_city_name\r\n  departureTime: string; // mapped from apiService.ApiFlightSegment.departure_time\r\n  departureTerminal?: string; // Added: e.g., \"T2\" - assumed from apiSegment.departure_terminal\r\n  arrivalAirportCode: string; // mapped from apiService.ApiFlightSegment.arrival_airport_code\r\n  arrivalAirportName: string; // mapped from apiService.ApiFlightSegment.arrival_airport_name\r\n  arrivalAirportFull?: string; // 到达机场的详细名称/信息\r\n  arrivalCityName: string; // mapped from apiService.ApiFlightSegment.arrival_city_name\r\n  arrivalTime: string; // mapped from apiService.ApiFlightSegment.arrival_time\r\n  arrivalTerminal?: string; // Added: e.g., \"T1\" - assumed from apiSegment.arrival_terminal\r\n  durationMinutes: number; // mapped from apiService.ApiFlightSegment.duration_minutes\r\n  cabinClass?: string; // mapped from apiService.ApiFlightSegment.cabin_class\r\n  equipment?: string; // 机型信息\r\n  isLayover?: boolean; // 标记此航段是否为中转停留的一部分\r\n  layoverDuration?: string; // 如果是中转，中转时长\r\n  nextSegmentRequiresAirportChange?: boolean; // 如果是中转，下一段是否需要更换机场\r\n  isBaggageRecheck?: boolean; // 如果是中转，是否需要重新托运行李\r\n  operatingCarrierCode?: string; // 实际执飞航司代码\r\n  operatingCarrierName?: string; // 实际执飞航司名称\r\n}\r\n\r\nexport interface TransferInfo {\r\n  city?: string; // City where the transfer occurs\r\n  durationMinutes: number; // Duration of the layover in minutes\r\n  isDifferentAirport: boolean; // True if transfer involves changing airports\r\n  airportChangeDetail?: {\r\n    fromAirportCode: string;\r\n    toAirportCode: string;\r\n  };\r\n  layoverTime?: string; // Formatted layover time e.g., \"2h 30m\"\r\n  isBaggageRecheck?: boolean; // Whether baggage needs to be rechecked during this transfer\r\n  isAirlineChange?: boolean; // Whether the airline changes during this transfer\r\n  fromAirline?: {\r\n    code: string;\r\n    name: string;\r\n  };\r\n  toAirline?: {\r\n    code: string;\r\n    name: string;\r\n  };\r\n}\r\n\r\nexport interface FlightItinerary {\r\n  id: string; // Unique ID for the itinerary\r\n  segments: FlightSegment[];\r\n  transfers?: TransferInfo[]; // Added: Detailed transfer information\r\n  totalDurationMinutes: number; // mapped from apiService.ApiFlightItinerary.total_duration_minutes\r\n  totalTravelTime?: string; // 整个行程的总旅行时间（包括中转）\r\n  price: {\r\n    amount: number;\r\n    currency: string;\r\n  };\r\n  airlines?: Array<{ // 参与该行程的所有航司信息\r\n    code: string;\r\n    name: string;\r\n    logoUrl?: string;\r\n  }>;\r\n  isDirectFlight: boolean; // mapped from apiService.ApiFlightItinerary.is_direct_flight\r\n  bookingToken?: string; // mapped from apiService.ApiFlightItinerary.booking_token\r\n  deepLink?: string; // mapped from apiService.ApiFlightItinerary.deep_link\r\n  // Added for more itinerary details\r\n  numberOfStops?: number;\r\n  isProbeSuggestion?: boolean; // 是否为通过\"中国中转城市探测\"逻辑找到的建议\r\n  probeHub?: string; // 如果是探测建议，相关的枢纽城市代码或名称\r\n  probeDisclaimer?: string; // Specific disclaimer for this probe suggestion\r\n  isComboDeal?: boolean; // Though often determined by array, map if API provides\r\n  providerName?: string; // e.g., \"Kiwi.com\"\r\n  tags?: string[]; // For any other relevant tags from the API\r\n  // 添加自行中转和隐藏城市标志\r\n  isSelfTransfer?: boolean; // 是否为自行中转航班\r\n  isHiddenCity?: boolean; // 是否为隐藏城市航班\r\n  isThrowawayDeal?: boolean; // 是否为甩尾票\r\n  isTrueHiddenCity?: boolean; // 是否为真正的隐藏城市航班\r\n  hiddenDestination?: { // 隐藏目的地信息（用于甩尾票）\r\n    code: string; // 机场代码\r\n    name: string; // 机场名称\r\n    cityName: string; // 城市名称\r\n    countryName?: string; // 国家名称\r\n  };\r\n  // Optional: store the raw API data for debugging or advanced use cases\r\n  // rawApiItinerary?: ApiFlightItinerary;\r\n}\r\n\r\nexport interface FlightData { // API 响应中 'result' 字段的结构 (TaskResultData in apiService)\r\n  directFlights?: ApiFlightItinerary[]; // Corresponds to \"direct_flights\" in TaskResultData\r\n  comboDeals?: ApiFlightItinerary[]; // Corresponds to \"combo_deals\" in TaskResultData\r\n  disclaimers?: string[];\r\n  // 🔧 添加后端实际返回的字段名支持\r\n  direct_flights?: ApiFlightItinerary[]; // 后端实际返回的字段名\r\n  hidden_city_flights?: ApiFlightItinerary[]; // 后端实际返回的字段名\r\n  // 根据 API_Documentation.md 或实际 API 响应，可能还有 search_parameters, context 等字段\r\n}\r\n\r\nexport interface ErrorInfo {\r\n  message: string;\r\n  type?: 'network' | 'server' | 'client' | 'timeout' | string;\r\n  details?: Record<string, unknown>; // 使用Record<string, unknown>代替any，更安全\r\n}\r\n\r\ninterface FlightResultsState {\r\n  taskId: string | null;\r\n  searchStatus: SearchStatus;\r\n  directFlights: FlightItinerary[];\r\n  comboDeals: FlightItinerary[];\r\n  disclaimers: string[];\r\n  error: string | ErrorInfo | null;\r\n  // 新增状态用于超时和连续失败处理\r\n  pollingTimeoutId: NodeJS.Timeout | null;\r\n  consecutiveFailures: number;\r\n  pollingStoppedReason: PollingStoppedReason | null;\r\n  lastActivityTime: number | null;\r\n\r\n  // Actions\r\n  setSearchInitiated: (taskId: string) => void;\r\n  setSearchPolling: () => void; // 用于任务状态 PENDING/STARTED 时，维持 loading 状态\r\n  setSearchSuccess: (data: FlightData, isFinalResult: boolean) => void; // 添加 isFinalResult 参数\r\n  setSearchError: (errorMessage: string | ErrorInfo) => void;\r\n  resetFlightSearch: () => void;\r\n  // 新增 action 用于处理轮询停止\r\n  stopPolling: (reason: PollingStoppedReason) => void;\r\n  // 新增立即设置loading状态的action\r\n  setSearchLoading: () => void;\r\n}\r\n\r\nconst initialState: Omit<FlightResultsState, 'setSearchInitiated' | 'setSearchPolling' | 'setSearchSuccess' | 'setSearchError' | 'resetFlightSearch' | 'stopPolling' | 'setSearchLoading'> = {\r\n  taskId: null,\r\n  searchStatus: 'idle',\r\n  directFlights: [],\r\n  comboDeals: [],\r\n  disclaimers: [],\r\n  error: null,\r\n  // 初始化新增状态\r\n  pollingTimeoutId: null,\r\n  consecutiveFailures: 0,\r\n  pollingStoppedReason: null,\r\n  lastActivityTime: null,\r\n};\r\n\r\nexport const useFlightResultsStore = create<FlightResultsState>((set, get) => ({\r\n  ...initialState,\r\n\r\n  setSearchLoading: () => {\r\n    console.log('🔄 [DEBUG] 立即设置loading状态');\r\n    set({\r\n      searchStatus: 'loading',\r\n      error: null,\r\n      lastActivityTime: Date.now(),\r\n    });\r\n  },\r\n\r\n  setSearchInitiated: (taskId) => {\r\n    const { pollingTimeoutId } = get();\r\n    if (pollingTimeoutId) {\r\n      clearTimeout(pollingTimeoutId);\r\n    }\r\n    const newTimeoutId = setTimeout(() => get().stopPolling('timeout'), POLLING_TIMEOUT_DURATION);\r\n\r\n    set({\r\n      ...initialState, // 重置结果和错误，开始新的搜索\r\n      taskId,\r\n      searchStatus: 'loading',\r\n      lastActivityTime: Date.now(),\r\n      pollingTimeoutId: newTimeoutId,\r\n      consecutiveFailures: 0,\r\n      pollingStoppedReason: null,\r\n    });\r\n  },\r\n\r\n  setSearchPolling: () =>\r\n    set((state) => {\r\n      if (state.pollingStoppedReason) { // 如果已停止，则不改变状态\r\n        return {};\r\n      }\r\n      // 仅当当前状态为 'loading' 时，才需要显式设置为 'loading'\r\n      // 如果已经是 'success' 或 'error'，则不应覆盖\r\n      if (state.searchStatus === 'loading') {\r\n        return { searchStatus: 'loading', lastActivityTime: Date.now() };\r\n      }\r\n      // 如果是从 'idle' 状态因为某种原因调用（理论上不应该），也设置为 'loading'\r\n      if (state.searchStatus === 'idle' && state.taskId) {\r\n         return { searchStatus: 'loading', lastActivityTime: Date.now() };\r\n      }\r\n      return {}; // 其他状态下不改变\r\n    }),\r\n\r\n  setSearchSuccess: (data: FlightData, isFinalResult: boolean) => {\r\n    console.log('🔍 === setSearchSuccess 调试信息 ===');\r\n    console.log('🔍 接收到的原始数据:', data);\r\n    console.log('🔍 数据类型:', typeof data);\r\n    console.log('🔍 是否为最终结果:', isFinalResult);\r\n    console.log('🔍 directFlights 字段:', data.directFlights);\r\n    console.log('🔍 comboDeals 字段:', data.comboDeals);\r\n    console.log('🔍 directFlights 长度:', data.directFlights?.length);\r\n    console.log('🔍 comboDeals 长度:', data.comboDeals?.length);\r\n    \r\n    // 🔍 添加详细的API响应数据结构分析\r\n    console.log('🔍 === API响应数据结构详细分析 ===');\r\n    console.log('🔍 完整的data对象键名:', Object.keys(data));\r\n    \r\n    // 检查是否有其他可能的字段名\r\n    const possibleFlightFields = ['directFlights', 'direct_flights', 'comboDeals', 'combo_deals', 'hiddenCityFlights', 'hidden_city_flights'];\r\n    possibleFlightFields.forEach(field => {\r\n      const fieldValue = (data as Record<string, unknown>)[field];\r\n      if (fieldValue) {\r\n        console.log(`🔍 发现字段 ${field}:`, fieldValue);\r\n        console.log(`🔍 ${field} 长度:`, Array.isArray(fieldValue) ? fieldValue.length : '不是数组');\r\n        \r\n        // 如果是数组且有数据，显示第一个元素的结构\r\n        if (Array.isArray(fieldValue) && fieldValue.length > 0) {\r\n          const firstItem = fieldValue[0] as Record<string, unknown>;\r\n          console.log(`🔍 ${field} 第一个元素的键名:`, Object.keys(firstItem));\r\n          console.log(`🔍 ${field} 第一个元素完整数据:`, firstItem);\r\n          \r\n          // 检查航段数据结构\r\n          const possibleSegmentFields = ['segments', 'outbound_segments', 'inbound_segments', 'sector', 'sectorSegments'];\r\n          possibleSegmentFields.forEach(segField => {\r\n            const segmentValue = firstItem[segField];\r\n            if (segmentValue) {\r\n              console.log(`🔍 ${field} 中发现航段字段 ${segField}:`, segmentValue);\r\n              if (Array.isArray(segmentValue) && segmentValue.length > 0) {\r\n                console.log(`🔍 ${segField} 第一个航段数据:`, segmentValue[0]);\r\n              }\r\n            }\r\n          });\r\n        }\r\n      }\r\n    });\r\n    \r\n    const { pollingTimeoutId } = get();\r\n    if (pollingTimeoutId) {\r\n      clearTimeout(pollingTimeoutId);\r\n    }\r\n\r\n    let newTimeoutId: NodeJS.Timeout | null = null;\r\n    if (!isFinalResult) {\r\n      newTimeoutId = setTimeout(() => get().stopPolling('timeout'), POLLING_TIMEOUT_DURATION);\r\n    }\r\n\r\n    // 🔧 修复数据映射逻辑，支持后端实际返回的字段名\r\n    console.log('开始映射 API 数据到 Store 格式...');\r\n    \r\n    // 支持后端实际返回的字段名\r\n    const directFlights = data.directFlights || data.direct_flights || [];\r\n    const hiddenCityFlights = data.comboDeals || data.hidden_city_flights || [];\r\n    \r\n    console.log('🔧 使用的数据源:');\r\n    console.log('- directFlights 来源:', data.directFlights ? 'data.directFlights' : 'data.direct_flights');\r\n    console.log('- hiddenCityFlights 来源:', data.comboDeals ? 'data.comboDeals' : 'data.hidden_city_flights');\r\n    console.log('- directFlights 数量:', directFlights.length);\r\n    console.log('- hiddenCityFlights 数量:', hiddenCityFlights.length);\r\n    \r\n    const mappedDirectFlights = directFlights.map(mapApiItineraryToStoreItinerary);\r\n    const mappedComboDeals = hiddenCityFlights.map(mapApiItineraryToStoreItinerary);\r\n    \r\n    console.log('映射完成:');\r\n    console.log('- 直飞航班映射结果数量:', mappedDirectFlights.length);\r\n    console.log('- 隐藏城市航班映射结果数量:', mappedComboDeals.length);\r\n    \r\n    // 后端已经处理去重，前端直接使用映射结果\r\n    console.log('使用后端去重结果:');\r\n    console.log('- 直飞航班数量:', mappedDirectFlights.length);\r\n    console.log('- 组合航班数量:', mappedComboDeals.length);\r\n    \r\n    // 如果有映射结果，显示第一个的详细信息\r\n    if (mappedDirectFlights.length > 0) {\r\n      console.log('第一个映射后的直飞航班:', mappedDirectFlights[0]);\r\n    }\r\n    if (mappedComboDeals.length > 0) {\r\n      console.log('第一个映射后的组合航班:', mappedComboDeals[0]);\r\n    }\r\n\r\n    set({\r\n      directFlights: mappedDirectFlights,\r\n      comboDeals: mappedComboDeals,\r\n      disclaimers: data.disclaimers || [],\r\n      searchStatus: isFinalResult ? 'success' : 'loading', // 如果不是最终结果，保持loading以继续轮询\r\n      error: null, // 清除之前的错误\r\n      consecutiveFailures: 0, // 重置连续失败次数\r\n      lastActivityTime: Date.now(),\r\n      pollingTimeoutId: newTimeoutId,\r\n      pollingStoppedReason: isFinalResult ? get().pollingStoppedReason : null, // 如果是最终结果，保留之前的停止原因（如果有），否则重置\r\n    });\r\n    \r\n    console.log('Store 状态更新完成');\r\n    console.log('当前 Store 状态:', get());\r\n  },\r\n\r\n  setSearchError: (error: string | ErrorInfo) => {\r\n    const { pollingTimeoutId, consecutiveFailures: prevFailures } = get();\r\n    if (pollingTimeoutId) {\r\n      clearTimeout(pollingTimeoutId);\r\n    }\r\n    const currentFailures = prevFailures + 1;\r\n\r\n    if (currentFailures >= MAX_CONSECUTIVE_FAILURES) {\r\n      get().stopPolling('max_failures');\r\n      set({\r\n        searchStatus: 'stopped', // 更新状态为 stopped\r\n        error: error,\r\n        consecutiveFailures: currentFailures,\r\n        pollingTimeoutId: null,\r\n        lastActivityTime: Date.now(),\r\n      });\r\n    } else {\r\n      // 如果未达到最大失败次数，仍然设置超时以便下次轮询\r\n      const newTimeoutId = setTimeout(() => get().stopPolling('timeout'), POLLING_TIMEOUT_DURATION);\r\n      set({\r\n        searchStatus: 'error', // 或 'loading' 如果希望错误后继续尝试轮询直到超时或最大失败\r\n        error: error,\r\n        consecutiveFailures: currentFailures,\r\n        pollingTimeoutId: newTimeoutId, // 重新设置超时\r\n        lastActivityTime: Date.now(),\r\n      });\r\n    }\r\n  },\r\n\r\n  stopPolling: (reason: PollingStoppedReason) => {\r\n    const { pollingTimeoutId } = get();\r\n    if (pollingTimeoutId) {\r\n      clearTimeout(pollingTimeoutId);\r\n    }\r\n    let errorMessage: ErrorInfo | string = '搜索已停止。';\r\n    if (reason === 'timeout') {\r\n        errorMessage = { type: 'timeout', message: '搜索超时，请稍后重试或调整搜索条件。' };\r\n    } else if (reason === 'max_failures') {\r\n        errorMessage = { type: 'max_failures', message: '多次尝试连接失败，请检查您的网络连接或稍后重试。' };\r\n    }\r\n\r\n    set({\r\n      searchStatus: 'stopped',\r\n      pollingStoppedReason: reason,\r\n      pollingTimeoutId: null,\r\n      error: errorMessage, // 设置相应的错误信息\r\n      lastActivityTime: Date.now(),\r\n    });\r\n  },\r\n\r\n  resetFlightSearch: () => {\r\n    const { pollingTimeoutId } = get();\r\n    if (pollingTimeoutId) {\r\n      clearTimeout(pollingTimeoutId);\r\n    }\r\n    set(initialState);\r\n  },\r\n}));\r\n\r\n\r\n// 以上类型定义已移至文件顶部并取消注释\r\n\r\n// 映射函数：将 API 响应类型转换为前端状态类型\r\n// 导出以便测试\r\nexport const mapApiItineraryToStoreItinerary = (apiItinerary: Record<string, unknown>): FlightItinerary => {\r\n  console.log('=== mapApiItineraryToStoreItinerary 调试 ===');\r\n  console.log('输入的 apiItinerary:', apiItinerary);\r\n  \r\n  // === 价格调试信息 ===\r\n  console.log('=== 价格字段调试 ===');\r\n  console.log('price:', apiItinerary.price, typeof apiItinerary.price);\r\n  console.log('currency:', apiItinerary.currency, typeof apiItinerary.currency);\r\n  console.log('原始price字段（deprecated）:', apiItinerary.price_eur);\r\n  \r\n  // 🔧 处理后端实际返回的数据结构\r\n  const segments = (apiItinerary.segments as Record<string, unknown>[]) ||\r\n                   (apiItinerary.outbound_segments as Record<string, unknown>[]) || [];\r\n  console.log('🔧 提取的 segments:', segments);\r\n  console.log('🔧 segments 来源:', apiItinerary.segments ? 'apiItinerary.segments' : 'apiItinerary.outbound_segments');\r\n  \r\n  // 计算总旅行时间 - 支持多种字段名\r\n  const totalDurationMinutes = (apiItinerary.totalDurationMinutes as number) ||\r\n                               (apiItinerary.total_duration_minutes as number) ||\r\n                               (apiItinerary.duration_minutes as number) ||\r\n                               (apiItinerary.duration as number) || 0;\r\n  \r\n  // 添加飞行时间调试信息\r\n  console.log('=== 飞行时间字段调试 ===');\r\n  console.log('totalDurationMinutes:', apiItinerary.totalDurationMinutes, typeof apiItinerary.totalDurationMinutes);\r\n  console.log('total_duration_minutes:', apiItinerary.total_duration_minutes, typeof apiItinerary.total_duration_minutes);\r\n  console.log('duration_minutes:', apiItinerary.duration_minutes, typeof apiItinerary.duration_minutes);\r\n  console.log('duration:', apiItinerary.duration, typeof apiItinerary.duration);\r\n  console.log('最终使用的totalDurationMinutes:', totalDurationMinutes);\r\n  \r\n  const totalTravelTime = (() => {\r\n    const hours = Math.floor(totalDurationMinutes / 60);\r\n    const minutes = totalDurationMinutes % 60;\r\n    const timeStr = `${hours}h ${minutes > 0 ? `${minutes}m` : ''}`.trim();\r\n    console.log('格式化后的飞行时间:', timeStr);\r\n    return timeStr;\r\n  })();\r\n\r\n  // 确定是否为直飞航班\r\n  const isDirectFlight = segments.length === 1;\r\n\r\n  // 计算中转次数\r\n  const numberOfStops = segments.length > 0 ? segments.length - 1 : 0;\r\n  \r\n  // 🔍 添加调试日志来诊断中转判断问题\r\n  console.log('🔍 数据映射器中转诊断 - 航班ID:', apiItinerary.id);\r\n  console.log('🔍 原始segments数量:', segments.length);\r\n  console.log('🔍 计算的isDirectFlight:', isDirectFlight);\r\n  console.log('🔍 计算的numberOfStops:', numberOfStops);\r\n  console.log('🔍 原始segments详情:', segments.map((seg, idx) => ({\r\n    index: idx,\r\n    from: `${seg.departure_city || seg.departureCityName}(${seg.departure_airport || seg.departureAirportCode})`,\r\n    to: `${seg.arrival_city || seg.arrivalCityName}(${seg.arrival_airport || seg.arrivalAirportCode})`,\r\n    flight: seg.flight_number || seg.flightNumber\r\n  })));\r\n\r\n  // 映射航段信息\r\n  const mappedSegments = segments.map((segment: Record<string, unknown>, index: number) => {\r\n    console.log(`映射航段 ${index + 1}:`, segment);\r\n    \r\n    // 🔧 处理后端Kiwi API字段名到前端字段名的映射\r\n    const carrier = segment.carrier as Record<string, unknown> || {};\r\n    const origin = segment.origin as Record<string, unknown> || {};\r\n    const destination = segment.destination as Record<string, unknown> || {};\r\n    const departure = segment.departure as Record<string, unknown> || {};\r\n    const arrival = segment.arrival as Record<string, unknown> || {};\r\n    \r\n    const mappedSegment = {\r\n      id: (segment.id as string) || `segment-${index}`,\r\n      // 🔧 支持Kiwi API的carrier结构\r\n      airlineCode: (carrier.code as string) || (segment.carrier_code as string) || (segment.airlineCode as string) || '',\r\n      airlineName: (carrier.name as string) || (segment.carrier_name as string) || (segment.airlineName as string) || '',\r\n      flightNumber: (segment.flight_number as string) || (segment.flightNumber as string) || '',\r\n      // 🔧 支持Kiwi API的origin/destination结构\r\n      departureAirportCode: (origin.code as string) || (segment.departure_airport as string) || (segment.departureAirportCode as string) || '',\r\n      departureAirportName: (origin.name as string) || (segment.departure_airport_name as string) || (segment.departureAirportName as string) || '',\r\n      departureCityName: (origin.city as string) || (segment.departure_city as string) || (segment.departureCityName as string) || '',\r\n      departureTime: (departure.local_time as string) || (segment.departure_time as string) || (segment.departureTime as string) || '',\r\n      arrivalAirportCode: (destination.code as string) || (segment.arrival_airport as string) || (segment.arrivalAirportCode as string) || '',\r\n      arrivalAirportName: (destination.name as string) || (segment.arrival_airport_name as string) || (segment.arrivalAirportName as string) || '',\r\n      arrivalCityName: (destination.city as string) || (segment.arrival_city as string) || (segment.arrivalCityName as string) || '',\r\n      arrivalTime: (arrival.local_time as string) || (segment.arrival_time as string) || (segment.arrivalTime as string) || '',\r\n      durationMinutes: (segment.duration_minutes as number) || (segment.durationMinutes as number) || 0,\r\n      cabinClass: (segment.cabin_class as string) || (segment.cabinClass as string) || '',\r\n      airlineLogoUrl: (segment.airlineLogoUrl as string) || ((carrier.code as string) || (segment.carrier_code as string) || (segment.airlineCode as string)\r\n        ? `https://daisycon.io/images/airline/?width=300&height=150&iata=${(carrier.code as string) || (segment.carrier_code as string) || (segment.airlineCode as string)}`\r\n        : undefined),\r\n      departureAirportFull: (origin.name as string) || (segment.departure_airport_name as string) || (segment.departureAirportName as string)\r\n        ? `${(origin.name as string) || (segment.departure_airport_name as string) || (segment.departureAirportName as string)} (${(origin.code as string) || (segment.departure_airport as string) || (segment.departureAirportCode as string)})`\r\n        : (origin.code as string) || (segment.departure_airport as string) || (segment.departureAirportCode as string) || '',\r\n      arrivalAirportFull: (destination.name as string) || (segment.arrival_airport_name as string) || (segment.arrivalAirportName as string)\r\n        ? `${(destination.name as string) || (segment.arrival_airport_name as string) || (segment.arrivalAirportName as string)} (${(destination.code as string) || (segment.arrival_airport as string) || (segment.arrivalAirportCode as string)})`\r\n        : (destination.code as string) || (segment.arrival_airport as string) || (segment.arrivalAirportCode as string) || '',\r\n      equipment: (segment.aircraft as string) || (segment.equipment as string),\r\n      isLayover: index > 0,\r\n      operatingCarrierCode: (segment.operating_carrier_code as string) || (segment.operatingCarrierCode as string),\r\n      operatingCarrierName: (segment.operating_carrier_name as string) || (segment.operatingCarrierName as string),\r\n    };\r\n    \r\n    console.log(`映射后的航段 ${index + 1}:`, mappedSegment);\r\n    return mappedSegment;\r\n  });\r\n\r\n  // 构建中转信息\r\n  const transfers = segments.length > 1\r\n    ? segments.slice(0, -1).map((currentSegment: Record<string, unknown>, index: number) => {\r\n        const nextSegment = segments[index + 1];\r\n\r\n        // 🔧 支持Kiwi API的嵌套结构\r\n        const currentArrivalData = (currentSegment.arrival as Record<string, unknown>) || {};\r\n        const nextDepartureData = (nextSegment.departure as Record<string, unknown>) || {};\r\n        const currentDestination = (currentSegment.destination as Record<string, unknown>) || {};\r\n        const nextOrigin = (nextSegment.origin as Record<string, unknown>) || {};\r\n        const currentCarrierData = (currentSegment.carrier as Record<string, unknown>) || {};\r\n        const nextCarrierData = (nextSegment.carrier as Record<string, unknown>) || {};\r\n        \r\n        // 计算中转时长\r\n        const currentArrivalTime = (currentArrivalData.local_time as string) || (currentSegment.arrival_time as string) || (currentSegment.arrivalTime as string);\r\n        const nextDepartureTime = (nextDepartureData.local_time as string) || (nextSegment.departure_time as string) || (nextSegment.departureTime as string);\r\n        const currentArrival = new Date(currentArrivalTime).getTime();\r\n        const nextDeparture = new Date(nextDepartureTime).getTime();\r\n        const durationMinutes = Math.round((nextDeparture - currentArrival) / (1000 * 60));\r\n\r\n        // 格式化中转时间\r\n        const hours = Math.floor(durationMinutes / 60);\r\n        const minutes = durationMinutes % 60;\r\n        const layoverTime = `${hours > 0 ? `${hours}h ` : ''}${minutes > 0 ? `${minutes}m` : (hours === 0 ? '0m' : '')}`.trim();\r\n\r\n        // 检查是否需要换机场\r\n        const currentArrivalAirport = (currentDestination.code as string) || (currentSegment.arrival_airport as string) || (currentSegment.arrivalAirportCode as string);\r\n        const nextDepartureAirport = (nextOrigin.code as string) || (nextSegment.departure_airport as string) || (nextSegment.departureAirportCode as string);\r\n        const isDifferentAirport = currentArrivalAirport !== nextDepartureAirport;\r\n        \r\n        let airportChangeDetail;\r\n        if (isDifferentAirport) {\r\n          airportChangeDetail = {\r\n            fromAirportCode: currentArrivalAirport,\r\n            toAirportCode: nextDepartureAirport,\r\n          };\r\n        }\r\n\r\n        // 检查是否更换航空公司\r\n        const currentCarrier = (currentCarrierData.code as string) || (currentSegment.carrier_code as string) || (currentSegment.airlineCode as string);\r\n        const nextCarrier = (nextCarrierData.code as string) || (nextSegment.carrier_code as string) || (nextSegment.airlineCode as string);\r\n        const isAirlineChange = currentCarrier !== nextCarrier;\r\n\r\n        return {\r\n          city: (currentDestination.city as string) || (currentSegment.arrival_city as string) || (currentSegment.arrivalCityName as string) || '未知城市',\r\n          durationMinutes,\r\n          isDifferentAirport,\r\n          airportChangeDetail,\r\n          layoverTime,\r\n          isBaggageRecheck: isDifferentAirport, // 简化逻辑：不同机场需要重新托运\r\n          isAirlineChange,\r\n          fromAirline: {\r\n            code: currentCarrier || '',\r\n            name: (currentCarrierData.name as string) || (currentSegment.carrier_name as string) || (currentSegment.airlineName as string) || '',\r\n          },\r\n          toAirline: {\r\n            code: nextCarrier || '',\r\n            name: (nextCarrierData.name as string) || (nextSegment.carrier_name as string) || (nextSegment.airlineName as string) || '',\r\n          },\r\n        };\r\n      })\r\n    : undefined;\r\n\r\n  // 🔧 构建所有参与航司信息，支持Kiwi API的carrier结构\r\n  const airlines = segments.reduce((acc: Array<{ code: string; name: string; logoUrl?: string }>, segment: Record<string, unknown>) => {\r\n    const carrier = segment.carrier as Record<string, unknown> || {};\r\n    const carrierCode = (carrier.code as string) || (segment.carrier_code as string) || (segment.airlineCode as string);\r\n    const carrierName = (carrier.name as string) || (segment.carrier_name as string) || (segment.airlineName as string);\r\n    \r\n    if (carrierCode && !acc.find(a => a.code === carrierCode)) {\r\n      acc.push({\r\n        code: carrierCode,\r\n        name: carrierName || '',\r\n        logoUrl: `https://daisycon.io/images/airline/?width=300&height=150&iata=${carrierCode}`,\r\n      });\r\n    }\r\n    return acc;\r\n  }, []);\r\n\r\n  // 查找隐藏目的地信息（优先从顶层字段提取，然后从segments中提取）\r\n  let hiddenDestination: FlightItinerary['hiddenDestination'];\r\n  \r\n  // 首先检查顶层的hiddenDestination字段（V2 API适配器传入的）\r\n  if (apiItinerary.hiddenDestination) {\r\n    const hiddenDest = apiItinerary.hiddenDestination as Record<string, unknown>;\r\n    hiddenDestination = {\r\n      code: (hiddenDest.code as string) || '',\r\n      name: (hiddenDest.name as string) || '',\r\n      cityName: (hiddenDest.cityName as string) || (hiddenDest.city_name as string) ||\r\n                ((hiddenDest.city as Record<string, unknown>)?.name as string) || '',\r\n      countryName: (hiddenDest.countryName as string) || (hiddenDest.country_name as string) ||\r\n                   ((hiddenDest.country as Record<string, unknown>)?.name as string) || '',\r\n    };\r\n    console.log('🎯 从顶层字段找到隐藏目的地:', hiddenDestination);\r\n  } else {\r\n    // 如果顶层没有，再从segments中查找（兼容旧版本）\r\n    for (const segment of segments) {\r\n      if (segment.hidden_destination || segment.hiddenDestination) {\r\n        const hiddenDest = segment.hidden_destination || segment.hiddenDestination;\r\n        if (typeof hiddenDest === 'object' && hiddenDest !== null) {\r\n          const dest = hiddenDest as Record<string, unknown>;\r\n          hiddenDestination = {\r\n            code: (dest.code as string) || '',\r\n            name: (dest.name as string) || '',\r\n            cityName: (dest.city_name as string) || (dest.cityName as string) ||\r\n                      ((dest.city as Record<string, unknown>)?.name as string) || '',\r\n            countryName: (dest.country_name as string) || (dest.countryName as string) ||\r\n                         ((dest.country as Record<string, unknown>)?.name as string) || '',\r\n          };\r\n          console.log('🎯 从segments中找到隐藏目的地:', hiddenDestination);\r\n          break; // 找到第一个隐藏目的地就停止\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  // 🔧 构建最终的航班行程对象，正确处理隐藏城市航班标记\r\n  const isHiddenCityFlight = (apiItinerary.is_hidden_city as boolean) ||\r\n                             (apiItinerary.flight_type as string) === 'hidden_city' ||\r\n                             false;\r\n  \r\n  // 🔧 如果是隐藏城市航班，重新计算直飞状态\r\n  // 隐藏城市航班虽然只有一个航段，但实际上是中转航班（在中转城市下机）\r\n  const actualIsDirectFlight = isHiddenCityFlight ? false : isDirectFlight;\r\n  const actualNumberOfStops = isHiddenCityFlight ? 1 : numberOfStops; // 隐藏城市航班至少有1次中转\r\n  \r\n  console.log('🔧 隐藏城市航班处理:');\r\n  console.log('- 原始isDirectFlight:', isDirectFlight);\r\n  console.log('- isHiddenCityFlight:', isHiddenCityFlight);\r\n  console.log('- 修正后的actualIsDirectFlight:', actualIsDirectFlight);\r\n  console.log('- 原始numberOfStops:', numberOfStops);\r\n  console.log('- 修正后的actualNumberOfStops:', actualNumberOfStops);\r\n  \r\n  const result: FlightItinerary = {\r\n    id: (apiItinerary.id as string) || '',\r\n    segments: mappedSegments,\r\n    transfers,\r\n    totalDurationMinutes,\r\n    totalTravelTime,\r\n    price: {\r\n      amount: (() => {\r\n        const priceValue = apiItinerary.price;\r\n        if (typeof priceValue === 'number') {\r\n          return priceValue;\r\n        }\r\n        if (typeof priceValue === 'object' && priceValue !== null) {\r\n          const priceObj = priceValue as Record<string, unknown>;\r\n          const amount = priceObj.amount;\r\n          if (typeof amount === 'string') {\r\n            return parseFloat(amount) || 0;\r\n          }\r\n          if (typeof amount === 'number') {\r\n            return amount;\r\n          }\r\n        }\r\n        return 0;\r\n      })(),\r\n      currency: (apiItinerary.currency as string) || 'CNY',\r\n    },\r\n    airlines,\r\n    isDirectFlight: actualIsDirectFlight, // 🔧 使用修正后的直飞状态\r\n    bookingToken: (apiItinerary.booking_token as string),\r\n    deepLink: (apiItinerary.deep_link as string),\r\n    numberOfStops: actualNumberOfStops, // 🔧 使用修正后的中转次数\r\n    isProbeSuggestion: (apiItinerary.is_probe_suggestion as boolean) || false,\r\n    probeHub: (apiItinerary.probe_hub as string),\r\n    probeDisclaimer: (apiItinerary.probe_disclaimer as string),\r\n    isComboDeal: false, // 根据数组位置确定\r\n    providerName: 'Kiwi.com',\r\n    isSelfTransfer: (apiItinerary.is_self_transfer as boolean) || false,\r\n    isHiddenCity: isHiddenCityFlight, // 🔧 使用正确的隐藏城市标记\r\n    isThrowawayDeal: (apiItinerary.is_throwaway_deal as boolean) || false,\r\n    isTrueHiddenCity: (apiItinerary.is_true_hidden_city as boolean) || false,\r\n    hiddenDestination,\r\n  };\r\n\r\n  console.log('=== 简化后的价格映射结果 ===');\r\n  console.log('最终价格对象:', result.price);\r\n  console.log('甩尾票标记:', result.isHiddenCity, result.isThrowawayDeal);\r\n  console.log('最终映射结果:', result);\r\n  return result;\r\n};"], "names": [], "mappings": ";;;;;;AAAA;;AAGO,MAAM,2BAA2B,IAAI,KAAK,MAAM,YAAY;AAC5D,MAAM,2BAA2B;AAyJxC,MAAM,eAAuL;IAC3L,QAAQ;IACR,cAAc;IACd,eAAe,EAAE;IACjB,YAAY,EAAE;IACd,aAAa,EAAE;IACf,OAAO;IACP,UAAU;IACV,kBAAkB;IAClB,qBAAqB;IACrB,sBAAsB;IACtB,kBAAkB;AACpB;AAEO,MAAM,wBAAwB,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,EAAsB,CAAC,KAAK,MAAQ,CAAC;QAC7E,GAAG,YAAY;QAEf,kBAAkB;YAChB,QAAQ,GAAG,CAAC;YACZ,IAAI;gBACF,cAAc;gBACd,OAAO;gBACP,kBAAkB,KAAK,GAAG;YAC5B;QACF;QAEA,oBAAoB,CAAC;YACnB,MAAM,EAAE,gBAAgB,EAAE,GAAG;YAC7B,IAAI,kBAAkB;gBACpB,aAAa;YACf;YACA,MAAM,eAAe,WAAW,IAAM,MAAM,WAAW,CAAC,YAAY;YAEpE,IAAI;gBACF,GAAG,YAAY;gBACf;gBACA,cAAc;gBACd,kBAAkB,KAAK,GAAG;gBAC1B,kBAAkB;gBAClB,qBAAqB;gBACrB,sBAAsB;YACxB;QACF;QAEA,kBAAkB,IAChB,IAAI,CAAC;gBACH,IAAI,MAAM,oBAAoB,EAAE;oBAC9B,OAAO,CAAC;gBACV;gBACA,yCAAyC;gBACzC,kCAAkC;gBAClC,IAAI,MAAM,YAAY,KAAK,WAAW;oBACpC,OAAO;wBAAE,cAAc;wBAAW,kBAAkB,KAAK,GAAG;oBAAG;gBACjE;gBACA,gDAAgD;gBAChD,IAAI,MAAM,YAAY,KAAK,UAAU,MAAM,MAAM,EAAE;oBAChD,OAAO;wBAAE,cAAc;wBAAW,kBAAkB,KAAK,GAAG;oBAAG;gBAClE;gBACA,OAAO,CAAC,GAAG,WAAW;YACxB;QAEF,kBAAkB,CAAC,MAAkB;YACnC,QAAQ,GAAG,CAAC;YACZ,QAAQ,GAAG,CAAC,gBAAgB;YAC5B,QAAQ,GAAG,CAAC,YAAY,OAAO;YAC/B,QAAQ,GAAG,CAAC,eAAe;YAC3B,QAAQ,GAAG,CAAC,wBAAwB,KAAK,aAAa;YACtD,QAAQ,GAAG,CAAC,qBAAqB,KAAK,UAAU;YAChD,QAAQ,GAAG,CAAC,wBAAwB,KAAK,aAAa,EAAE;YACxD,QAAQ,GAAG,CAAC,qBAAqB,KAAK,UAAU,EAAE;YAElD,sBAAsB;YACtB,QAAQ,GAAG,CAAC;YACZ,QAAQ,GAAG,CAAC,mBAAmB,OAAO,IAAI,CAAC;YAE3C,gBAAgB;YAChB,MAAM,uBAAuB;gBAAC;gBAAiB;gBAAkB;gBAAc;gBAAe;gBAAqB;aAAsB;YACzI,qBAAqB,OAAO,CAAC,CAAA;gBAC3B,MAAM,aAAa,AAAC,IAAgC,CAAC,MAAM;gBAC3D,IAAI,YAAY;oBACd,QAAQ,GAAG,CAAC,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC,EAAE;oBACjC,QAAQ,GAAG,CAAC,CAAC,GAAG,EAAE,MAAM,IAAI,CAAC,EAAE,MAAM,OAAO,CAAC,cAAc,WAAW,MAAM,GAAG;oBAE/E,uBAAuB;oBACvB,IAAI,MAAM,OAAO,CAAC,eAAe,WAAW,MAAM,GAAG,GAAG;wBACtD,MAAM,YAAY,UAAU,CAAC,EAAE;wBAC/B,QAAQ,GAAG,CAAC,CAAC,GAAG,EAAE,MAAM,UAAU,CAAC,EAAE,OAAO,IAAI,CAAC;wBACjD,QAAQ,GAAG,CAAC,CAAC,GAAG,EAAE,MAAM,WAAW,CAAC,EAAE;wBAEtC,WAAW;wBACX,MAAM,wBAAwB;4BAAC;4BAAY;4BAAqB;4BAAoB;4BAAU;yBAAiB;wBAC/G,sBAAsB,OAAO,CAAC,CAAA;4BAC5B,MAAM,eAAe,SAAS,CAAC,SAAS;4BACxC,IAAI,cAAc;gCAChB,QAAQ,GAAG,CAAC,CAAC,GAAG,EAAE,MAAM,SAAS,EAAE,SAAS,CAAC,CAAC,EAAE;gCAChD,IAAI,MAAM,OAAO,CAAC,iBAAiB,aAAa,MAAM,GAAG,GAAG;oCAC1D,QAAQ,GAAG,CAAC,CAAC,GAAG,EAAE,SAAS,SAAS,CAAC,EAAE,YAAY,CAAC,EAAE;gCACxD;4BACF;wBACF;oBACF;gBACF;YACF;YAEA,MAAM,EAAE,gBAAgB,EAAE,GAAG;YAC7B,IAAI,kBAAkB;gBACpB,aAAa;YACf;YAEA,IAAI,eAAsC;YAC1C,IAAI,CAAC,eAAe;gBAClB,eAAe,WAAW,IAAM,MAAM,WAAW,CAAC,YAAY;YAChE;YAEA,2BAA2B;YAC3B,QAAQ,GAAG,CAAC;YAEZ,eAAe;YACf,MAAM,gBAAgB,KAAK,aAAa,IAAI,KAAK,cAAc,IAAI,EAAE;YACrE,MAAM,oBAAoB,KAAK,UAAU,IAAI,KAAK,mBAAmB,IAAI,EAAE;YAE3E,QAAQ,GAAG,CAAC;YACZ,QAAQ,GAAG,CAAC,uBAAuB,KAAK,aAAa,GAAG,uBAAuB;YAC/E,QAAQ,GAAG,CAAC,2BAA2B,KAAK,UAAU,GAAG,oBAAoB;YAC7E,QAAQ,GAAG,CAAC,uBAAuB,cAAc,MAAM;YACvD,QAAQ,GAAG,CAAC,2BAA2B,kBAAkB,MAAM;YAE/D,MAAM,sBAAsB,cAAc,GAAG,CAAC;YAC9C,MAAM,mBAAmB,kBAAkB,GAAG,CAAC;YAE/C,QAAQ,GAAG,CAAC;YACZ,QAAQ,GAAG,CAAC,iBAAiB,oBAAoB,MAAM;YACvD,QAAQ,GAAG,CAAC,mBAAmB,iBAAiB,MAAM;YAEtD,sBAAsB;YACtB,QAAQ,GAAG,CAAC;YACZ,QAAQ,GAAG,CAAC,aAAa,oBAAoB,MAAM;YACnD,QAAQ,GAAG,CAAC,aAAa,iBAAiB,MAAM;YAEhD,qBAAqB;YACrB,IAAI,oBAAoB,MAAM,GAAG,GAAG;gBAClC,QAAQ,GAAG,CAAC,gBAAgB,mBAAmB,CAAC,EAAE;YACpD;YACA,IAAI,iBAAiB,MAAM,GAAG,GAAG;gBAC/B,QAAQ,GAAG,CAAC,gBAAgB,gBAAgB,CAAC,EAAE;YACjD;YAEA,IAAI;gBACF,eAAe;gBACf,YAAY;gBACZ,aAAa,KAAK,WAAW,IAAI,EAAE;gBACnC,cAAc,gBAAgB,YAAY;gBAC1C,OAAO;gBACP,qBAAqB;gBACrB,kBAAkB,KAAK,GAAG;gBAC1B,kBAAkB;gBAClB,sBAAsB,gBAAgB,MAAM,oBAAoB,GAAG;YACrE;YAEA,QAAQ,GAAG,CAAC;YACZ,QAAQ,GAAG,CAAC,gBAAgB;QAC9B;QAEA,gBAAgB,CAAC;YACf,MAAM,EAAE,gBAAgB,EAAE,qBAAqB,YAAY,EAAE,GAAG;YAChE,IAAI,kBAAkB;gBACpB,aAAa;YACf;YACA,MAAM,kBAAkB,eAAe;YAEvC,IAAI,mBAAmB,0BAA0B;gBAC/C,MAAM,WAAW,CAAC;gBAClB,IAAI;oBACF,cAAc;oBACd,OAAO;oBACP,qBAAqB;oBACrB,kBAAkB;oBAClB,kBAAkB,KAAK,GAAG;gBAC5B;YACF,OAAO;gBACL,2BAA2B;gBAC3B,MAAM,eAAe,WAAW,IAAM,MAAM,WAAW,CAAC,YAAY;gBACpE,IAAI;oBACF,cAAc;oBACd,OAAO;oBACP,qBAAqB;oBACrB,kBAAkB;oBAClB,kBAAkB,KAAK,GAAG;gBAC5B;YACF;QACF;QAEA,aAAa,CAAC;YACZ,MAAM,EAAE,gBAAgB,EAAE,GAAG;YAC7B,IAAI,kBAAkB;gBACpB,aAAa;YACf;YACA,IAAI,eAAmC;YACvC,IAAI,WAAW,WAAW;gBACtB,eAAe;oBAAE,MAAM;oBAAW,SAAS;gBAAqB;YACpE,OAAO,IAAI,WAAW,gBAAgB;gBAClC,eAAe;oBAAE,MAAM;oBAAgB,SAAS;gBAA2B;YAC/E;YAEA,IAAI;gBACF,cAAc;gBACd,sBAAsB;gBACtB,kBAAkB;gBAClB,OAAO;gBACP,kBAAkB,KAAK,GAAG;YAC5B;QACF;QAEA,mBAAmB;YACjB,MAAM,EAAE,gBAAgB,EAAE,GAAG;YAC7B,IAAI,kBAAkB;gBACpB,aAAa;YACf;YACA,IAAI;QACN;IACF,CAAC;AAOM,MAAM,kCAAkC,CAAC;IAC9C,QAAQ,GAAG,CAAC;IACZ,QAAQ,GAAG,CAAC,qBAAqB;IAEjC,iBAAiB;IACjB,QAAQ,GAAG,CAAC;IACZ,QAAQ,GAAG,CAAC,UAAU,aAAa,KAAK,EAAE,OAAO,aAAa,KAAK;IACnE,QAAQ,GAAG,CAAC,aAAa,aAAa,QAAQ,EAAE,OAAO,aAAa,QAAQ;IAC5E,QAAQ,GAAG,CAAC,0BAA0B,aAAa,SAAS;IAE5D,mBAAmB;IACnB,MAAM,WAAW,AAAC,aAAa,QAAQ,IACrB,aAAa,iBAAiB,IAAkC,EAAE;IACpF,QAAQ,GAAG,CAAC,oBAAoB;IAChC,QAAQ,GAAG,CAAC,mBAAmB,aAAa,QAAQ,GAAG,0BAA0B;IAEjF,oBAAoB;IACpB,MAAM,uBAAuB,AAAC,aAAa,oBAAoB,IACjC,aAAa,sBAAsB,IACnC,aAAa,gBAAgB,IAC7B,aAAa,QAAQ,IAAe;IAElE,aAAa;IACb,QAAQ,GAAG,CAAC;IACZ,QAAQ,GAAG,CAAC,yBAAyB,aAAa,oBAAoB,EAAE,OAAO,aAAa,oBAAoB;IAChH,QAAQ,GAAG,CAAC,2BAA2B,aAAa,sBAAsB,EAAE,OAAO,aAAa,sBAAsB;IACtH,QAAQ,GAAG,CAAC,qBAAqB,aAAa,gBAAgB,EAAE,OAAO,aAAa,gBAAgB;IACpG,QAAQ,GAAG,CAAC,aAAa,aAAa,QAAQ,EAAE,OAAO,aAAa,QAAQ;IAC5E,QAAQ,GAAG,CAAC,8BAA8B;IAE1C,MAAM,kBAAkB,CAAC;QACvB,MAAM,QAAQ,KAAK,KAAK,CAAC,uBAAuB;QAChD,MAAM,UAAU,uBAAuB;QACvC,MAAM,UAAU,GAAG,MAAM,EAAE,EAAE,UAAU,IAAI,GAAG,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI;QACpE,QAAQ,GAAG,CAAC,cAAc;QAC1B,OAAO;IACT,CAAC;IAED,YAAY;IACZ,MAAM,iBAAiB,SAAS,MAAM,KAAK;IAE3C,SAAS;IACT,MAAM,gBAAgB,SAAS,MAAM,GAAG,IAAI,SAAS,MAAM,GAAG,IAAI;IAElE,qBAAqB;IACrB,QAAQ,GAAG,CAAC,wBAAwB,aAAa,EAAE;IACnD,QAAQ,GAAG,CAAC,oBAAoB,SAAS,MAAM;IAC/C,QAAQ,GAAG,CAAC,yBAAyB;IACrC,QAAQ,GAAG,CAAC,wBAAwB;IACpC,QAAQ,GAAG,CAAC,oBAAoB,SAAS,GAAG,CAAC,CAAC,KAAK,MAAQ,CAAC;YAC1D,OAAO;YACP,MAAM,GAAG,IAAI,cAAc,IAAI,IAAI,iBAAiB,CAAC,CAAC,EAAE,IAAI,iBAAiB,IAAI,IAAI,oBAAoB,CAAC,CAAC,CAAC;YAC5G,IAAI,GAAG,IAAI,YAAY,IAAI,IAAI,eAAe,CAAC,CAAC,EAAE,IAAI,eAAe,IAAI,IAAI,kBAAkB,CAAC,CAAC,CAAC;YAClG,QAAQ,IAAI,aAAa,IAAI,IAAI,YAAY;QAC/C,CAAC;IAED,SAAS;IACT,MAAM,iBAAiB,SAAS,GAAG,CAAC,CAAC,SAAkC;QACrE,QAAQ,GAAG,CAAC,CAAC,KAAK,EAAE,QAAQ,EAAE,CAAC,CAAC,EAAE;QAElC,8BAA8B;QAC9B,MAAM,UAAU,QAAQ,OAAO,IAA+B,CAAC;QAC/D,MAAM,SAAS,QAAQ,MAAM,IAA+B,CAAC;QAC7D,MAAM,cAAc,QAAQ,WAAW,IAA+B,CAAC;QACvE,MAAM,YAAY,QAAQ,SAAS,IAA+B,CAAC;QACnE,MAAM,UAAU,QAAQ,OAAO,IAA+B,CAAC;QAE/D,MAAM,gBAAgB;YACpB,IAAI,AAAC,QAAQ,EAAE,IAAe,CAAC,QAAQ,EAAE,OAAO;YAChD,0BAA0B;YAC1B,aAAa,AAAC,QAAQ,IAAI,IAAgB,QAAQ,YAAY,IAAgB,QAAQ,WAAW,IAAe;YAChH,aAAa,AAAC,QAAQ,IAAI,IAAgB,QAAQ,YAAY,IAAgB,QAAQ,WAAW,IAAe;YAChH,cAAc,AAAC,QAAQ,aAAa,IAAgB,QAAQ,YAAY,IAAe;YACvF,qCAAqC;YACrC,sBAAsB,AAAC,OAAO,IAAI,IAAgB,QAAQ,iBAAiB,IAAgB,QAAQ,oBAAoB,IAAe;YACtI,sBAAsB,AAAC,OAAO,IAAI,IAAgB,QAAQ,sBAAsB,IAAgB,QAAQ,oBAAoB,IAAe;YAC3I,mBAAmB,AAAC,OAAO,IAAI,IAAgB,QAAQ,cAAc,IAAgB,QAAQ,iBAAiB,IAAe;YAC7H,eAAe,AAAC,UAAU,UAAU,IAAgB,QAAQ,cAAc,IAAgB,QAAQ,aAAa,IAAe;YAC9H,oBAAoB,AAAC,YAAY,IAAI,IAAgB,QAAQ,eAAe,IAAgB,QAAQ,kBAAkB,IAAe;YACrI,oBAAoB,AAAC,YAAY,IAAI,IAAgB,QAAQ,oBAAoB,IAAgB,QAAQ,kBAAkB,IAAe;YAC1I,iBAAiB,AAAC,YAAY,IAAI,IAAgB,QAAQ,YAAY,IAAgB,QAAQ,eAAe,IAAe;YAC5H,aAAa,AAAC,QAAQ,UAAU,IAAgB,QAAQ,YAAY,IAAgB,QAAQ,WAAW,IAAe;YACtH,iBAAiB,AAAC,QAAQ,gBAAgB,IAAgB,QAAQ,eAAe,IAAe;YAChG,YAAY,AAAC,QAAQ,WAAW,IAAgB,QAAQ,UAAU,IAAe;YACjF,gBAAgB,AAAC,QAAQ,cAAc,IAAe,CAAC,AAAC,QAAQ,IAAI,IAAgB,QAAQ,YAAY,IAAgB,QAAQ,WAAW,GACvI,CAAC,8DAA8D,EAAE,AAAC,QAAQ,IAAI,IAAgB,QAAQ,YAAY,IAAgB,QAAQ,WAAW,EAAa,GAClK,SAAS;YACb,sBAAsB,AAAC,OAAO,IAAI,IAAgB,QAAQ,sBAAsB,IAAgB,QAAQ,oBAAoB,GACxH,GAAG,AAAC,OAAO,IAAI,IAAgB,QAAQ,sBAAsB,IAAgB,QAAQ,oBAAoB,CAAY,EAAE,EAAE,AAAC,OAAO,IAAI,IAAgB,QAAQ,iBAAiB,IAAgB,QAAQ,oBAAoB,CAAY,CAAC,CAAC,GACxO,AAAC,OAAO,IAAI,IAAgB,QAAQ,iBAAiB,IAAgB,QAAQ,oBAAoB,IAAe;YACpH,oBAAoB,AAAC,YAAY,IAAI,IAAgB,QAAQ,oBAAoB,IAAgB,QAAQ,kBAAkB,GACvH,GAAG,AAAC,YAAY,IAAI,IAAgB,QAAQ,oBAAoB,IAAgB,QAAQ,kBAAkB,CAAY,EAAE,EAAE,AAAC,YAAY,IAAI,IAAgB,QAAQ,eAAe,IAAgB,QAAQ,kBAAkB,CAAY,CAAC,CAAC,GAC1O,AAAC,YAAY,IAAI,IAAgB,QAAQ,eAAe,IAAgB,QAAQ,kBAAkB,IAAe;YACrH,WAAW,AAAC,QAAQ,QAAQ,IAAgB,QAAQ,SAAS;YAC7D,WAAW,QAAQ;YACnB,sBAAsB,AAAC,QAAQ,sBAAsB,IAAgB,QAAQ,oBAAoB;YACjG,sBAAsB,AAAC,QAAQ,sBAAsB,IAAgB,QAAQ,oBAAoB;QACnG;QAEA,QAAQ,GAAG,CAAC,CAAC,OAAO,EAAE,QAAQ,EAAE,CAAC,CAAC,EAAE;QACpC,OAAO;IACT;IAEA,SAAS;IACT,MAAM,YAAY,SAAS,MAAM,GAAG,IAChC,SAAS,KAAK,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,gBAAyC;QAClE,MAAM,cAAc,QAAQ,CAAC,QAAQ,EAAE;QAEvC,qBAAqB;QACrB,MAAM,qBAAqB,AAAC,eAAe,OAAO,IAAgC,CAAC;QACnF,MAAM,oBAAoB,AAAC,YAAY,SAAS,IAAgC,CAAC;QACjF,MAAM,qBAAqB,AAAC,eAAe,WAAW,IAAgC,CAAC;QACvF,MAAM,aAAa,AAAC,YAAY,MAAM,IAAgC,CAAC;QACvE,MAAM,qBAAqB,AAAC,eAAe,OAAO,IAAgC,CAAC;QACnF,MAAM,kBAAkB,AAAC,YAAY,OAAO,IAAgC,CAAC;QAE7E,SAAS;QACT,MAAM,qBAAqB,AAAC,mBAAmB,UAAU,IAAgB,eAAe,YAAY,IAAgB,eAAe,WAAW;QAC9I,MAAM,oBAAoB,AAAC,kBAAkB,UAAU,IAAgB,YAAY,cAAc,IAAgB,YAAY,aAAa;QAC1I,MAAM,iBAAiB,IAAI,KAAK,oBAAoB,OAAO;QAC3D,MAAM,gBAAgB,IAAI,KAAK,mBAAmB,OAAO;QACzD,MAAM,kBAAkB,KAAK,KAAK,CAAC,CAAC,gBAAgB,cAAc,IAAI,CAAC,OAAO,EAAE;QAEhF,UAAU;QACV,MAAM,QAAQ,KAAK,KAAK,CAAC,kBAAkB;QAC3C,MAAM,UAAU,kBAAkB;QAClC,MAAM,cAAc,GAAG,QAAQ,IAAI,GAAG,MAAM,EAAE,CAAC,GAAG,KAAK,UAAU,IAAI,GAAG,QAAQ,CAAC,CAAC,GAAI,UAAU,IAAI,OAAO,IAAK,CAAC,IAAI;QAErH,YAAY;QACZ,MAAM,wBAAwB,AAAC,mBAAmB,IAAI,IAAgB,eAAe,eAAe,IAAgB,eAAe,kBAAkB;QACrJ,MAAM,uBAAuB,AAAC,WAAW,IAAI,IAAgB,YAAY,iBAAiB,IAAgB,YAAY,oBAAoB;QAC1I,MAAM,qBAAqB,0BAA0B;QAErD,IAAI;QACJ,IAAI,oBAAoB;YACtB,sBAAsB;gBACpB,iBAAiB;gBACjB,eAAe;YACjB;QACF;QAEA,aAAa;QACb,MAAM,iBAAiB,AAAC,mBAAmB,IAAI,IAAgB,eAAe,YAAY,IAAgB,eAAe,WAAW;QACpI,MAAM,cAAc,AAAC,gBAAgB,IAAI,IAAgB,YAAY,YAAY,IAAgB,YAAY,WAAW;QACxH,MAAM,kBAAkB,mBAAmB;QAE3C,OAAO;YACL,MAAM,AAAC,mBAAmB,IAAI,IAAgB,eAAe,YAAY,IAAgB,eAAe,eAAe,IAAe;YACtI;YACA;YACA;YACA;YACA,kBAAkB;YAClB;YACA,aAAa;gBACX,MAAM,kBAAkB;gBACxB,MAAM,AAAC,mBAAmB,IAAI,IAAgB,eAAe,YAAY,IAAgB,eAAe,WAAW,IAAe;YACpI;YACA,WAAW;gBACT,MAAM,eAAe;gBACrB,MAAM,AAAC,gBAAgB,IAAI,IAAgB,YAAY,YAAY,IAAgB,YAAY,WAAW,IAAe;YAC3H;QACF;IACF,KACA;IAEJ,qCAAqC;IACrC,MAAM,WAAW,SAAS,MAAM,CAAC,CAAC,KAA8D;QAC9F,MAAM,UAAU,QAAQ,OAAO,IAA+B,CAAC;QAC/D,MAAM,cAAc,AAAC,QAAQ,IAAI,IAAgB,QAAQ,YAAY,IAAgB,QAAQ,WAAW;QACxG,MAAM,cAAc,AAAC,QAAQ,IAAI,IAAgB,QAAQ,YAAY,IAAgB,QAAQ,WAAW;QAExG,IAAI,eAAe,CAAC,IAAI,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,cAAc;YACzD,IAAI,IAAI,CAAC;gBACP,MAAM;gBACN,MAAM,eAAe;gBACrB,SAAS,CAAC,8DAA8D,EAAE,aAAa;YACzF;QACF;QACA,OAAO;IACT,GAAG,EAAE;IAEL,sCAAsC;IACtC,IAAI;IAEJ,2CAA2C;IAC3C,IAAI,aAAa,iBAAiB,EAAE;QAClC,MAAM,aAAa,aAAa,iBAAiB;QACjD,oBAAoB;YAClB,MAAM,AAAC,WAAW,IAAI,IAAe;YACrC,MAAM,AAAC,WAAW,IAAI,IAAe;YACrC,UAAU,AAAC,WAAW,QAAQ,IAAgB,WAAW,SAAS,IACtD,WAAW,IAAI,EAA8B,QAAmB;YAC5E,aAAa,AAAC,WAAW,WAAW,IAAgB,WAAW,YAAY,IAC5D,WAAW,OAAO,EAA8B,QAAmB;QACpF;QACA,QAAQ,GAAG,CAAC,oBAAoB;IAClC,OAAO;QACL,8BAA8B;QAC9B,KAAK,MAAM,WAAW,SAAU;YAC9B,IAAI,QAAQ,kBAAkB,IAAI,QAAQ,iBAAiB,EAAE;gBAC3D,MAAM,aAAa,QAAQ,kBAAkB,IAAI,QAAQ,iBAAiB;gBAC1E,IAAI,OAAO,eAAe,YAAY,eAAe,MAAM;oBACzD,MAAM,OAAO;oBACb,oBAAoB;wBAClB,MAAM,AAAC,KAAK,IAAI,IAAe;wBAC/B,MAAM,AAAC,KAAK,IAAI,IAAe;wBAC/B,UAAU,AAAC,KAAK,SAAS,IAAgB,KAAK,QAAQ,IAC1C,KAAK,IAAI,EAA8B,QAAmB;wBACtE,aAAa,AAAC,KAAK,YAAY,IAAgB,KAAK,WAAW,IAChD,KAAK,OAAO,EAA8B,QAAmB;oBAC9E;oBACA,QAAQ,GAAG,CAAC,yBAAyB;oBACrC,OAAO,gBAAgB;gBACzB;YACF;QACF;IACF;IAEA,8BAA8B;IAC9B,MAAM,qBAAqB,AAAC,aAAa,cAAc,IAC5B,AAAC,aAAa,WAAW,KAAgB,iBACzC;IAE3B,wBAAwB;IACxB,oCAAoC;IACpC,MAAM,uBAAuB,qBAAqB,QAAQ;IAC1D,MAAM,sBAAsB,qBAAqB,IAAI,eAAe,gBAAgB;IAEpF,QAAQ,GAAG,CAAC;IACZ,QAAQ,GAAG,CAAC,uBAAuB;IACnC,QAAQ,GAAG,CAAC,yBAAyB;IACrC,QAAQ,GAAG,CAAC,+BAA+B;IAC3C,QAAQ,GAAG,CAAC,sBAAsB;IAClC,QAAQ,GAAG,CAAC,8BAA8B;IAE1C,MAAM,SAA0B;QAC9B,IAAI,AAAC,aAAa,EAAE,IAAe;QACnC,UAAU;QACV;QACA;QACA;QACA,OAAO;YACL,QAAQ,CAAC;gBACP,MAAM,aAAa,aAAa,KAAK;gBACrC,IAAI,OAAO,eAAe,UAAU;oBAClC,OAAO;gBACT;gBACA,IAAI,OAAO,eAAe,YAAY,eAAe,MAAM;oBACzD,MAAM,WAAW;oBACjB,MAAM,SAAS,SAAS,MAAM;oBAC9B,IAAI,OAAO,WAAW,UAAU;wBAC9B,OAAO,WAAW,WAAW;oBAC/B;oBACA,IAAI,OAAO,WAAW,UAAU;wBAC9B,OAAO;oBACT;gBACF;gBACA,OAAO;YACT,CAAC;YACD,UAAU,AAAC,aAAa,QAAQ,IAAe;QACjD;QACA;QACA,gBAAgB;QAChB,cAAe,aAAa,aAAa;QACzC,UAAW,aAAa,SAAS;QACjC,eAAe;QACf,mBAAmB,AAAC,aAAa,mBAAmB,IAAgB;QACpE,UAAW,aAAa,SAAS;QACjC,iBAAkB,aAAa,gBAAgB;QAC/C,aAAa;QACb,cAAc;QACd,gBAAgB,AAAC,aAAa,gBAAgB,IAAgB;QAC9D,cAAc;QACd,iBAAiB,AAAC,aAAa,iBAAiB,IAAgB;QAChE,kBAAkB,AAAC,aAAa,mBAAmB,IAAgB;QACnE;IACF;IAEA,QAAQ,GAAG,CAAC;IACZ,QAAQ,GAAG,CAAC,WAAW,OAAO,KAAK;IACnC,QAAQ,GAAG,CAAC,UAAU,OAAO,YAAY,EAAE,OAAO,eAAe;IACjE,QAAQ,GAAG,CAAC,WAAW;IACvB,OAAO;AACT", "debugId": null}}, {"offset": {"line": 483, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/AeroScout/aeroscout-frontend/src/app/search/results/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\nimport { useFlightResultsStore, FlightItinerary } from '@/store/flightResultsStore';\r\nimport { useRouter } from 'next/navigation';\r\n\r\nexport default function FlightResultsPage() {\r\n  const router = useRouter();\r\n  const {\r\n    directFlights,\r\n    comboDeals,\r\n    searchStatus,\r\n    disclaimers,\r\n    error\r\n  } = useFlightResultsStore();\r\n\r\n  const totalFlights = directFlights.length + comboDeals.length;\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-white\">\r\n      {/* 顶部导航 - 苹果官网风格 */}\r\n      <nav className=\"bg-white/90 backdrop-blur-md sticky top-0 z-50 border-b border-[#E5E5EA]\">\r\n        <div className=\"max-w-[980px] mx-auto px-5 py-3 flex justify-between items-center\">\r\n          <div className=\"flex items-center space-x-4\">\r\n            <button\r\n              onClick={() => router.push('/')}\r\n              className=\"flex items-center text-[#1D1D1F] hover:text-[#0071E3] transition-apple\"\r\n            >\r\n              <svg className=\"w-5 h-5 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 19l-7-7 7-7\" />\r\n              </svg>\r\n              <span className=\"text-[14px] font-medium\">返回搜索</span>\r\n            </button>\r\n            <div className=\"h-4 w-px bg-[#E5E5EA]\"></div>\r\n            <h1 className=\"text-[21px] font-medium text-[#1D1D1F]\">航班搜索结果</h1>\r\n          </div>\r\n\r\n          {/* 搜索状态指示器 */}\r\n          <div className=\"flex items-center space-x-3\">\r\n            {searchStatus === 'loading' && (\r\n              <div className=\"flex items-center text-[#0071E3]\">\r\n                <div className=\"animate-spin rounded-full h-4 w-4 border-2 border-[#0071E3] border-t-transparent mr-2\"></div>\r\n                <span className=\"text-[14px] font-medium\">搜索中...</span>\r\n              </div>\r\n            )}\r\n            {totalFlights > 0 && (\r\n              <div className=\"bg-[#34C759]/10 text-[#34C759] px-3 py-1.5 rounded-full text-[14px] font-medium\">\r\n                找到 {totalFlights} 个航班\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n      </nav>\r\n\r\n      <div className=\"max-w-[980px] mx-auto px-5 py-8\">\r\n        {/* 错误状态 */}\r\n        {error && (\r\n          <div className=\"mb-6 bg-[#FF3B30]/5 border border-[#FF3B30]/20 rounded-2xl p-6\">\r\n            <div className=\"flex items-center\">\r\n              <div className=\"w-10 h-10 bg-[#FF3B30]/10 rounded-full flex items-center justify-center mr-4\">\r\n                <svg className=\"w-5 h-5 text-[#FF3B30]\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\r\n                </svg>\r\n              </div>\r\n              <div>\r\n                <h3 className=\"text-[17px] font-semibold text-[#1D1D1F] mb-1\">搜索出现问题</h3>\r\n                <p className=\"text-[15px] text-[#86868B]\">\r\n                  {typeof error === 'string' ? error : error.message}\r\n                </p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        {/* 加载状态 */}\r\n        {searchStatus === 'loading' && totalFlights === 0 && (\r\n          <div className=\"text-center py-20\">\r\n            <div className=\"inline-flex items-center justify-center w-20 h-20 bg-[#0071E3]/10 rounded-full mb-6\">\r\n              <div className=\"animate-spin rounded-full h-10 w-10 border-3 border-[#0071E3] border-t-transparent\"></div>\r\n            </div>\r\n            <h2 className=\"text-[28px] font-semibold text-[#1D1D1F] mb-3\">正在搜索最优航班</h2>\r\n            <p className=\"text-[17px] text-[#86868B]\">请稍候，我们正在为您寻找最佳选择...</p>\r\n          </div>\r\n        )}\r\n\r\n        {/* 无结果状态 */}\r\n        {searchStatus === 'success' && totalFlights === 0 && (\r\n          <div className=\"text-center py-20\">\r\n            <div className=\"inline-flex items-center justify-center w-20 h-20 bg-[#86868B]/10 rounded-full mb-6\">\r\n              <svg className=\"w-10 h-10 text-[#86868B]\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M21 21l-5.197-5.197m0 0A7.5 7.5 0 105.196 5.196a7.5 7.5 0 0010.607 10.607z\" />\r\n              </svg>\r\n            </div>\r\n            <h2 className=\"text-[28px] font-semibold text-[#1D1D1F] mb-3\">未找到符合条件的航班</h2>\r\n            <p className=\"text-[17px] text-[#86868B] mb-8\">请尝试调整您的搜索条件</p>\r\n            <button\r\n              onClick={() => router.push('/')}\r\n              className=\"bg-[#0071E3] hover:bg-[#0077ED] text-white px-6 py-3 rounded-full text-[17px] font-medium shadow-apple-sm transition-apple\"\r\n            >\r\n              重新搜索\r\n            </button>\r\n          </div>\r\n        )}\r\n\r\n        {/* 直飞航班 */}\r\n        {directFlights.length > 0 && (\r\n          <div className=\"mb-12\">\r\n            <div className=\"flex items-center mb-8\">\r\n              <div className=\"flex items-center\">\r\n                <div className=\"w-4 h-4 bg-[#34C759] rounded-full mr-4\"></div>\r\n                <h2 className=\"text-[28px] font-semibold text-[#1D1D1F]\">直飞航班</h2>\r\n                <span className=\"ml-4 bg-[#34C759]/10 text-[#34C759] px-3 py-1.5 rounded-full text-[14px] font-medium\">\r\n                  {directFlights.length} 个\r\n                </span>\r\n              </div>\r\n            </div>\r\n            <div className=\"space-y-4\">\r\n              {directFlights.map((flight, index) => (\r\n                <FlightCard key={flight.id || index} flight={flight} />\r\n              ))}\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        {/* 组合航班 */}\r\n        {comboDeals.length > 0 && (\r\n          <div className=\"mb-12\">\r\n            <div className=\"flex items-center mb-8\">\r\n              <div className=\"flex items-center\">\r\n                <div className=\"w-4 h-4 bg-[#AF52DE] rounded-full mr-4\"></div>\r\n                <h2 className=\"text-[28px] font-semibold text-[#1D1D1F]\">隐藏城市航班</h2>\r\n                <span className=\"ml-4 bg-[#AF52DE]/10 text-[#AF52DE] px-3 py-1.5 rounded-full text-[14px] font-medium\">\r\n                  {comboDeals.length} 个\r\n                </span>\r\n              </div>\r\n            </div>\r\n            <div className=\"space-y-4\">\r\n              {comboDeals.map((flight, index) => (\r\n                <FlightCard key={flight.id || index} flight={flight} />\r\n              ))}\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        {/* 免责声明 */}\r\n        {disclaimers.length > 0 && (\r\n          <div className=\"mt-12 bg-[#0071E3]/5 border border-[#0071E3]/20 rounded-2xl p-6\">\r\n            <h3 className=\"text-[17px] font-semibold text-[#1D1D1F] mb-4 flex items-center\">\r\n              <div className=\"w-5 h-5 bg-[#0071E3]/10 rounded-full flex items-center justify-center mr-3\">\r\n                <svg className=\"w-3 h-3 text-[#0071E3]\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\r\n                </svg>\r\n              </div>\r\n              重要提示\r\n            </h3>\r\n            <ul className=\"text-[15px] text-[#86868B] space-y-3\">\r\n              {disclaimers.map((disclaimer, index) => (\r\n                <li key={index} className=\"flex items-start\">\r\n                  <span className=\"w-1.5 h-1.5 bg-[#86868B] rounded-full mr-3 mt-2 flex-shrink-0\"></span>\r\n                  <span>{disclaimer}</span>\r\n                </li>\r\n              ))}\r\n            </ul>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\n// 简化的航班卡片组件\r\nfunction FlightCard({ flight }: { flight: FlightItinerary }) {\r\n  const formatTime = (timeStr: string) => {\r\n    try {\r\n      const date = new Date(timeStr);\r\n      return date.toLocaleTimeString('zh-CN', {\r\n        hour: '2-digit',\r\n        minute: '2-digit',\r\n        hour12: false\r\n      });\r\n    } catch {\r\n      return timeStr;\r\n    }\r\n  };\r\n\r\n  const formatDate = (timeStr: string) => {\r\n    try {\r\n      const date = new Date(timeStr);\r\n      return date.toLocaleDateString('zh-CN', {\r\n        month: 'short',\r\n        day: 'numeric'\r\n      });\r\n    } catch {\r\n      return '';\r\n    }\r\n  };\r\n\r\n  const firstSegment = flight.segments[0];\r\n  const lastSegment = flight.segments[flight.segments.length - 1];\r\n\r\n  if (!firstSegment || !lastSegment) return null;\r\n\r\n  return (\r\n    <div className=\"bg-white rounded-2xl shadow-apple-sm border border-[#E8E8ED] hover:shadow-apple-md transition-apple\">\r\n      <div className=\"p-8\">\r\n        {/* 顶部：价格和标签 */}\r\n        <div className=\"flex justify-between items-start mb-8\">\r\n          <div className=\"flex items-center space-x-3\">\r\n            {/* 根据航班类型和实际航段数量显示标签 */}\r\n            {(() => {\r\n              // 如果是隐藏城市航班，强制显示为中转航班\r\n              if (flight.isHiddenCity || flight.hiddenDestination) {\r\n                const actualStops = flight.segments ? flight.segments.length - 1 : 1; // 隐藏城市航班至少1次中转\r\n                const displayStops = Math.max(actualStops, 1); // 确保至少显示1次中转\r\n                return (\r\n                  <span className=\"bg-[#0071E3]/10 text-[#0071E3] px-3 py-1.5 rounded-full text-[12px] font-medium\">\r\n                    {displayStops} 次中转\r\n                  </span>\r\n                );\r\n              }\r\n\r\n              // 普通航班逻辑\r\n              if (flight.isDirectFlight || flight.numberOfStops === 0 || !flight.numberOfStops) {\r\n                return (\r\n                  <span className=\"bg-[#34C759]/10 text-[#34C759] px-3 py-1.5 rounded-full text-[12px] font-medium\">\r\n                    直飞\r\n                  </span>\r\n                );\r\n              } else {\r\n                return (\r\n                  <span className=\"bg-[#0071E3]/10 text-[#0071E3] px-3 py-1.5 rounded-full text-[12px] font-medium\">\r\n                    {flight.numberOfStops} 次中转\r\n                  </span>\r\n                );\r\n              }\r\n            })()}\r\n          </div>\r\n          <div className=\"text-right\">\r\n            <div className=\"text-[32px] font-semibold text-[#1D1D1F] tracking-tight\">\r\n              ¥{flight.price.amount.toLocaleString()}\r\n            </div>\r\n            <div className=\"text-[14px] text-[#86868B] mt-1\">含税价格</div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* 主要航班信息 */}\r\n        <div className=\"flex items-center justify-between\">\r\n          {/* 出发信息 */}\r\n          <div className=\"text-center\">\r\n            <div className=\"text-[32px] font-semibold text-[#1D1D1F] tracking-tight\">\r\n              {formatTime(firstSegment.departureTime)}\r\n            </div>\r\n            <div className=\"text-[14px] text-[#86868B] mt-2\">\r\n              {formatDate(firstSegment.departureTime)}\r\n            </div>\r\n            <div className=\"text-[21px] font-semibold text-[#1D1D1F] mt-4\">\r\n              {firstSegment.departureAirportCode}\r\n            </div>\r\n            <div className=\"text-[15px] text-[#86868B] mt-1\">\r\n              {firstSegment.departureCityName}\r\n            </div>\r\n          </div>\r\n\r\n          {/* 航班路径 */}\r\n          <div className=\"flex-1 mx-12\">\r\n            <div className=\"relative\">\r\n              <div className=\"flex items-center justify-center\">\r\n                <div className=\"flex-1 border-t-2 border-[#E8E8ED]\"></div>\r\n                <div className=\"mx-6 text-center\">\r\n                  <div className=\"bg-[#F5F5F7] rounded-full p-3 mb-3\">\r\n                    <svg className=\"w-6 h-6 text-[#86868B]\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 19l9 2-9-18-9 18 9-2zm0 0v-8\" />\r\n                    </svg>\r\n                  </div>\r\n                  <div className=\"text-[15px] text-[#86868B] font-medium\">\r\n                    {flight.totalTravelTime ||\r\n                     (flight.totalDurationMinutes && flight.totalDurationMinutes > 0\r\n                       ? `${Math.floor(flight.totalDurationMinutes / 60)}h${flight.totalDurationMinutes % 60 > 0 ? ` ${flight.totalDurationMinutes % 60}m` : ''}`\r\n                       : 'N/A')}\r\n                  </div>\r\n                  {(() => {\r\n                    // 如果是隐藏城市航班，强制显示为中转航班\r\n                    if (flight.isHiddenCity || flight.hiddenDestination) {\r\n                      const actualStops = flight.segments ? flight.segments.length - 1 : 1; // 隐藏城市航班至少1次中转\r\n                      const displayStops = Math.max(actualStops, 1); // 确保至少显示1次中转\r\n                      return (\r\n                        <div className=\"text-[12px] text-[#86868B] mt-2\">\r\n                          {displayStops} 次中转\r\n                        </div>\r\n                      );\r\n                    }\r\n\r\n                    // 普通航班逻辑\r\n                    if (flight.numberOfStops === 0 || !flight.numberOfStops) {\r\n                      return (\r\n                        <div className=\"text-[12px] text-[#34C759] mt-2 font-medium\">\r\n                          直飞\r\n                        </div>\r\n                      );\r\n                    } else {\r\n                      return (\r\n                        <div className=\"text-[12px] text-[#86868B] mt-2\">\r\n                          {flight.numberOfStops} 次中转\r\n                        </div>\r\n                      );\r\n                    }\r\n                  })()}\r\n                </div>\r\n                <div className=\"flex-1 border-t-2 border-[#E8E8ED]\"></div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* 到达信息 */}\r\n          <div className=\"text-center\">\r\n            <div className=\"text-[32px] font-semibold text-[#1D1D1F] tracking-tight\">\r\n              {formatTime(lastSegment.arrivalTime)}\r\n            </div>\r\n            <div className=\"text-[14px] text-[#86868B] mt-2\">\r\n              {formatDate(lastSegment.arrivalTime)}\r\n            </div>\r\n            <div className=\"text-[21px] font-semibold text-[#1D1D1F] mt-4\">\r\n              {(flight.hiddenDestination && flight.hiddenDestination.code)\r\n                ? flight.hiddenDestination.code\r\n                : (lastSegment.arrivalAirportCode || 'N/A')}\r\n            </div>\r\n            <div className=\"text-[15px] text-[#86868B] mt-1\">\r\n              {(flight.hiddenDestination && flight.hiddenDestination.cityName)\r\n                ? flight.hiddenDestination.cityName\r\n                : (lastSegment.arrivalCityName || '未知城市')}\r\n            </div>\r\n            {/* 在落地时间下方显示实际目的地信息 */}\r\n            {flight.hiddenDestination && (\r\n              <div className=\"text-[12px] text-[#0071E3] mt-2 font-medium\">\r\n                {flight.hiddenDestination.cityName || flight.hiddenDestination.name}\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n\r\n        {/* 航空公司信息 */}\r\n        <div className=\"mt-8 pt-6 border-t border-[#E8E8ED]\">\r\n          <div className=\"flex items-center justify-between text-[15px]\">\r\n            <div className=\"flex items-center text-[#86868B]\">\r\n              <span className=\"font-medium text-[#1D1D1F]\">航空公司:</span>\r\n              <span className=\"ml-3\">\r\n                {flight.airlines?.map(airline => airline.name).join(', ') ||\r\n                 flight.segments.map(seg => seg.airlineName).join(', ')}\r\n              </span>\r\n            </div>\r\n            <div className=\"flex items-center text-[#86868B]\">\r\n              <span className=\"font-medium text-[#1D1D1F]\">航班号:</span>\r\n              <span className=\"ml-3\">\r\n                {flight.segments.map(seg => `${seg.airlineCode} ${seg.flightNumber}`).join(', ')}\r\n              </span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* 中转信息 */}\r\n        {flight.transfers && flight.transfers.length > 0 && (\r\n          <div className=\"mt-6 p-4 bg-[#FF9500]/5 border border-[#FF9500]/20 rounded-2xl\">\r\n            <div className=\"text-[15px]\">\r\n              <span className=\"font-semibold text-[#1D1D1F]\">中转信息:</span>\r\n              {flight.transfers.map((transfer, index) => (\r\n                <span key={index} className=\"ml-3 text-[#86868B]\">\r\n                  在{transfer.city}中转{transfer.layoverTime}\r\n                  {transfer.isDifferentAirport && <span className=\"text-[#FF9500] font-medium\"> (需换机场)</span>}\r\n                  {index < flight.transfers!.length - 1 && ', '}\r\n                </span>\r\n              ))}\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;;;AAJA;;;AAMe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EACJ,aAAa,EACb,UAAU,EACV,YAAY,EACZ,WAAW,EACX,KAAK,EACN,GAAG,CAAA,GAAA,qIAAA,CAAA,wBAAqB,AAAD;IAExB,MAAM,eAAe,cAAc,MAAM,GAAG,WAAW,MAAM;IAE7D,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS,IAAM,OAAO,IAAI,CAAC;oCAC3B,WAAU;;sDAEV,6LAAC;4CAAI,WAAU;4CAAe,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDACtE,cAAA,6LAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;sDAEvE,6LAAC;4CAAK,WAAU;sDAA0B;;;;;;;;;;;;8CAE5C,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAG,WAAU;8CAAyC;;;;;;;;;;;;sCAIzD,6LAAC;4BAAI,WAAU;;gCACZ,iBAAiB,2BAChB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAK,WAAU;sDAA0B;;;;;;;;;;;;gCAG7C,eAAe,mBACd,6LAAC;oCAAI,WAAU;;wCAAkF;wCAC3F;wCAAa;;;;;;;;;;;;;;;;;;;;;;;;0BAO3B,6LAAC;gBAAI,WAAU;;oBAEZ,uBACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;wCAAyB,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDAChF,cAAA,6LAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;8CAGzE,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAgD;;;;;;sDAC9D,6LAAC;4CAAE,WAAU;sDACV,OAAO,UAAU,WAAW,QAAQ,MAAM,OAAO;;;;;;;;;;;;;;;;;;;;;;;oBAQ3D,iBAAiB,aAAa,iBAAiB,mBAC9C,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;;;;;;;;;;0CAEjB,6LAAC;gCAAG,WAAU;0CAAgD;;;;;;0CAC9D,6LAAC;gCAAE,WAAU;0CAA6B;;;;;;;;;;;;oBAK7C,iBAAiB,aAAa,iBAAiB,mBAC9C,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;oCAA2B,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CAClF,cAAA,6LAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;;;;;0CAGzE,6LAAC;gCAAG,WAAU;0CAAgD;;;;;;0CAC9D,6LAAC;gCAAE,WAAU;0CAAkC;;;;;;0CAC/C,6LAAC;gCACC,SAAS,IAAM,OAAO,IAAI,CAAC;gCAC3B,WAAU;0CACX;;;;;;;;;;;;oBAOJ,cAAc,MAAM,GAAG,mBACtB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,6LAAC;4CAAK,WAAU;;gDACb,cAAc,MAAM;gDAAC;;;;;;;;;;;;;;;;;;0CAI5B,6LAAC;gCAAI,WAAU;0CACZ,cAAc,GAAG,CAAC,CAAC,QAAQ,sBAC1B,6LAAC;wCAAoC,QAAQ;uCAA5B,OAAO,EAAE,IAAI;;;;;;;;;;;;;;;;oBAOrC,WAAW,MAAM,GAAG,mBACnB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,6LAAC;4CAAK,WAAU;;gDACb,WAAW,MAAM;gDAAC;;;;;;;;;;;;;;;;;;0CAIzB,6LAAC;gCAAI,WAAU;0CACZ,WAAW,GAAG,CAAC,CAAC,QAAQ,sBACvB,6LAAC;wCAAoC,QAAQ;uCAA5B,OAAO,EAAE,IAAI;;;;;;;;;;;;;;;;oBAOrC,YAAY,MAAM,GAAG,mBACpB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;;kDACZ,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;4CAAyB,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDAChF,cAAA,6LAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;;;;;;oCAEnE;;;;;;;0CAGR,6LAAC;gCAAG,WAAU;0CACX,YAAY,GAAG,CAAC,CAAC,YAAY,sBAC5B,6LAAC;wCAAe,WAAU;;0DACxB,6LAAC;gDAAK,WAAU;;;;;;0DAChB,6LAAC;0DAAM;;;;;;;uCAFA;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWzB;GAlKwB;;QACP,qIAAA,CAAA,YAAS;QAOpB,qIAAA,CAAA,wBAAqB;;;KARH;AAoKxB,YAAY;AACZ,SAAS,WAAW,EAAE,MAAM,EAA+B;IACzD,MAAM,aAAa,CAAC;QAClB,IAAI;YACF,MAAM,OAAO,IAAI,KAAK;YACtB,OAAO,KAAK,kBAAkB,CAAC,SAAS;gBACtC,MAAM;gBACN,QAAQ;gBACR,QAAQ;YACV;QACF,EAAE,OAAM;YACN,OAAO;QACT;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,IAAI;YACF,MAAM,OAAO,IAAI,KAAK;YACtB,OAAO,KAAK,kBAAkB,CAAC,SAAS;gBACtC,OAAO;gBACP,KAAK;YACP;QACF,EAAE,OAAM;YACN,OAAO;QACT;IACF;IAEA,MAAM,eAAe,OAAO,QAAQ,CAAC,EAAE;IACvC,MAAM,cAAc,OAAO,QAAQ,CAAC,OAAO,QAAQ,CAAC,MAAM,GAAG,EAAE;IAE/D,IAAI,CAAC,gBAAgB,CAAC,aAAa,OAAO;IAE1C,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCAEZ,CAAC;gCACA,sBAAsB;gCACtB,IAAI,OAAO,YAAY,IAAI,OAAO,iBAAiB,EAAE;oCACnD,MAAM,cAAc,OAAO,QAAQ,GAAG,OAAO,QAAQ,CAAC,MAAM,GAAG,IAAI,GAAG,eAAe;oCACrF,MAAM,eAAe,KAAK,GAAG,CAAC,aAAa,IAAI,aAAa;oCAC5D,qBACE,6LAAC;wCAAK,WAAU;;4CACb;4CAAa;;;;;;;gCAGpB;gCAEA,SAAS;gCACT,IAAI,OAAO,cAAc,IAAI,OAAO,aAAa,KAAK,KAAK,CAAC,OAAO,aAAa,EAAE;oCAChF,qBACE,6LAAC;wCAAK,WAAU;kDAAkF;;;;;;gCAItG,OAAO;oCACL,qBACE,6LAAC;wCAAK,WAAU;;4CACb,OAAO,aAAa;4CAAC;;;;;;;gCAG5B;4BACF,CAAC;;;;;;sCAEH,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;wCAA0D;wCACrE,OAAO,KAAK,CAAC,MAAM,CAAC,cAAc;;;;;;;8CAEtC,6LAAC;oCAAI,WAAU;8CAAkC;;;;;;;;;;;;;;;;;;8BAKrD,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACZ,WAAW,aAAa,aAAa;;;;;;8CAExC,6LAAC;oCAAI,WAAU;8CACZ,WAAW,aAAa,aAAa;;;;;;8CAExC,6LAAC;oCAAI,WAAU;8CACZ,aAAa,oBAAoB;;;;;;8CAEpC,6LAAC;oCAAI,WAAU;8CACZ,aAAa,iBAAiB;;;;;;;;;;;;sCAKnC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;wDAAyB,MAAK;wDAAO,QAAO;wDAAe,SAAQ;kEAChF,cAAA,6LAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;;;;;;;;;;;;8DAGzE,6LAAC;oDAAI,WAAU;8DACZ,OAAO,eAAe,IACtB,CAAC,OAAO,oBAAoB,IAAI,OAAO,oBAAoB,GAAG,IAC1D,GAAG,KAAK,KAAK,CAAC,OAAO,oBAAoB,GAAG,IAAI,CAAC,EAAE,OAAO,oBAAoB,GAAG,KAAK,IAAI,CAAC,CAAC,EAAE,OAAO,oBAAoB,GAAG,GAAG,CAAC,CAAC,GAAG,IAAI,GACxI,KAAK;;;;;;gDAEX,CAAC;oDACA,sBAAsB;oDACtB,IAAI,OAAO,YAAY,IAAI,OAAO,iBAAiB,EAAE;wDACnD,MAAM,cAAc,OAAO,QAAQ,GAAG,OAAO,QAAQ,CAAC,MAAM,GAAG,IAAI,GAAG,eAAe;wDACrF,MAAM,eAAe,KAAK,GAAG,CAAC,aAAa,IAAI,aAAa;wDAC5D,qBACE,6LAAC;4DAAI,WAAU;;gEACZ;gEAAa;;;;;;;oDAGpB;oDAEA,SAAS;oDACT,IAAI,OAAO,aAAa,KAAK,KAAK,CAAC,OAAO,aAAa,EAAE;wDACvD,qBACE,6LAAC;4DAAI,WAAU;sEAA8C;;;;;;oDAIjE,OAAO;wDACL,qBACE,6LAAC;4DAAI,WAAU;;gEACZ,OAAO,aAAa;gEAAC;;;;;;;oDAG5B;gDACF,CAAC;;;;;;;sDAEH,6LAAC;4CAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;sCAMrB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACZ,WAAW,YAAY,WAAW;;;;;;8CAErC,6LAAC;oCAAI,WAAU;8CACZ,WAAW,YAAY,WAAW;;;;;;8CAErC,6LAAC;oCAAI,WAAU;8CACZ,AAAC,OAAO,iBAAiB,IAAI,OAAO,iBAAiB,CAAC,IAAI,GACvD,OAAO,iBAAiB,CAAC,IAAI,GAC5B,YAAY,kBAAkB,IAAI;;;;;;8CAEzC,6LAAC;oCAAI,WAAU;8CACZ,AAAC,OAAO,iBAAiB,IAAI,OAAO,iBAAiB,CAAC,QAAQ,GAC3D,OAAO,iBAAiB,CAAC,QAAQ,GAChC,YAAY,eAAe,IAAI;;;;;;gCAGrC,OAAO,iBAAiB,kBACvB,6LAAC;oCAAI,WAAU;8CACZ,OAAO,iBAAiB,CAAC,QAAQ,IAAI,OAAO,iBAAiB,CAAC,IAAI;;;;;;;;;;;;;;;;;;8BAO3E,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAA6B;;;;;;kDAC7C,6LAAC;wCAAK,WAAU;kDACb,OAAO,QAAQ,EAAE,IAAI,CAAA,UAAW,QAAQ,IAAI,EAAE,KAAK,SACnD,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAA,MAAO,IAAI,WAAW,EAAE,IAAI,CAAC;;;;;;;;;;;;0CAGtD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAA6B;;;;;;kDAC7C,6LAAC;wCAAK,WAAU;kDACb,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAA,MAAO,GAAG,IAAI,WAAW,CAAC,CAAC,EAAE,IAAI,YAAY,EAAE,EAAE,IAAI,CAAC;;;;;;;;;;;;;;;;;;;;;;;gBAOlF,OAAO,SAAS,IAAI,OAAO,SAAS,CAAC,MAAM,GAAG,mBAC7C,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAU;0CAA+B;;;;;;4BAC9C,OAAO,SAAS,CAAC,GAAG,CAAC,CAAC,UAAU,sBAC/B,6LAAC;oCAAiB,WAAU;;wCAAsB;wCAC9C,SAAS,IAAI;wCAAC;wCAAG,SAAS,WAAW;wCACtC,SAAS,kBAAkB,kBAAI,6LAAC;4CAAK,WAAU;sDAA6B;;;;;;wCAC5E,QAAQ,OAAO,SAAS,CAAE,MAAM,GAAG,KAAK;;mCAHhC;;;;;;;;;;;;;;;;;;;;;;;;;;;AAa3B;MA/MS", "debugId": null}}, {"offset": {"line": 1496, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/AeroScout/aeroscout-frontend/node_modules/next/navigation.js"], "sourcesContent": ["module.exports = require('./dist/client/components/navigation')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0], "debugId": null}}]}